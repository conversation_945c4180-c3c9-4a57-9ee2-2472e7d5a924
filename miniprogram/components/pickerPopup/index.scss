@use 'styles/variables' as *;

$namespace: 'picker-popup';

.superior-tips-dialog {
  @include tdesign-variables;

  --td-button-light-color: #{$font-color-label};
  --td-radius-default: 16rpx;
  --td-button-light-bg-color: #f7f7f7;

  padding: 0 46rpx;
  box-sizing: border-box;

  &.t-button--hover::after {
    --td-button-light-active-bg-color: #f7f7f7;
    --td-button-light-active-border-color: #f7f7f7;
  }

  .dialog-content {
    padding-left: 0;
    padding-right: 0;
  }

  .superior-dialog-content {
    margin-top: 16px;
    color: $font-color-basic;
    text-align: center;
    font-size: 32rpx;
    line-height: 48rpx;
    padding: 0 4rpx;

    .description {
      display: flex;
      font-size: 24rpx;
      margin-top: 14rpx;
    }

    .footer {
      margin-top: 50rpx;
      display: flex;

      .btn {
        &::after {
          display: none;
        }
      }

      .cancel {
        width: calc((100% - 25rpx) / 2);
        margin-right: 25rpx;
        color: $font-color-label;
        background-color: #f7f7f7;
      }

      .confirm {
        flex-grow: 1;
      }
    }
  }
}

.#{$namespace}-layout {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 80vh;
  background-color: $color-white;
  z-index: $popup-z-index;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease;

  &--show {
    transform: translateY(0);
  }

  .t-side-bar {
    --td-side-bar-width: 240rpx;
    overflow: visible;
  }

  .t-side-bar-item {
    --td-side-bar-font-size: 28rpx;

    flex: none;
    margin-right: 40rpx;
    padding: 30rpx;
    justify-content: flex-start;
  }

  .t-badge {
    position: static;
  }

  .t-badge__content-text {
    line-height: 42rpx;
  }

  .t-badge__content:not(:empty) + .t-has-count {
    top: 50%;
    left: 170rpx;
    transform: translate(0, -50%);
  }

  .t-badge--basic {
    --td-badge-bg-color: #{$color-point};
  }

  .t-badge--dot {
    --td-badge-dot-size: 12rpx;
  }
}

.#{$namespace}-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  .#{$namespace}-header {
    // 文字居中
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    height: 100rpx; /* 确保容器有足够的高度 */
  }
  .#{$namespace}-title {
    font-size: 18px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 600;
  }
}

.#{$namespace}-multiple {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  height: 90rpx;

  .multiple-text {
    margin-right: 30rpx;
    font-size: 24rpx;
  }

  .multiple-num {
    color: $color-primary;
  }

  .multiple-scroll {
    flex: 1;
    position: relative;
    height: 50rpx;
    overflow-y: hidden;
  }

  .multiple-checked {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    margin-right: 10rpx;
    height: 80rpx;
    overflow: auto;
  }

  .t-tag {
    --td-tag-default-font-color: #{$color-primary};
    --td-tag-default-color: #{$color-primary};

    height: 50rpx;
    font-size: 24rpx;
    line-height: 50rpx;

    & + .t-tag {
      margin-left: 20rpx;
    }

    .t-tag__text {
      max-width: 220rpx;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .t-icon-close {
      color: $color-primary;
    }
  }
}

.#{$namespace}-main {
  flex: 1;
  position: relative;
  margin-top: 0;
}

.#{$namespace}-content {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  .#{$namespace}-side {
    flex: none;
    width: 200rpx;
    height: 100%;
    overflow-x: hidden;
  }

  .#{$namespace}-scroll {
    flex: none;
    padding-bottom: 20rpx;
    width: calc(100vw - 200rpx);
    overflow-x: hidden;

    &.safe-area {
      margin-bottom: env(safe-area-inset-bottom);
    }

    .scroll-content {
      height: 100%;
    }
  }
}

.#{$namespace}-section {
  margin: 0 40rpx;

  .#{$namespace}-title {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 50rpx 0 20rpx 18rpx;
    font-size: 28rpx;
    font-weight: bold;
    line-height: 1;

    &::before {
      content: '';
      position: absolute;
      top: 50rpx;
      left: 0;
      bottom: 20rpx;
      width: 6rpx;
      background-color: $color-primary;
      border-radius: 3rpx;
    }
    .left {
      display: flex;
      align-items: center;
      i {
        width: 50rpx;
        height: 20rpx;
        margin-left: 10rpx;
        background: url(//img.gaoxiaojob.com/uploads/mini/icon/vip-radius.png) no-repeat center/100% 100%;
      }
    }
    .right {
      display: flex;
      align-items: center;
      .aside {
        font-size: 22rpx;
        color: #333333;
        opacity: 0.8;
      }
      i {
        width: 30rpx;
        height: 30rpx;
        margin-left: 10rpx;
        background: url(//img.gaoxiaojob.com/uploads/mini/icon/question.png) no-repeat center/contain;
      }
    }
  }

  .custom-checkbox {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10rpx 20rpx;
    width: 100%;
    height: 74rpx;
    font-size: 24rpx;
    text-align: center;
    line-height: 28rpx;
    background-color: $color-white;
    border-radius: 8rpx;
    border: 2rpx solid $border-color;
    box-sizing: border-box;

    &.is-checked {
      color: $color-primary;
      border-color: $color-primary;
      background-color: $color-primary-background;
    }

    .custom-checkbox-text {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      max-height: 56rpx;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  .t-grid-item__content {
    padding: 0;
  }
}

.#{$namespace}-footer {
  flex: none;
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  height: 140rpx;
  background-color: $color-white;
  box-sizing: border-box;

  .t-button {
    margin: 0;
  }

  .reset-button {
    width: 220rpx;
    color: $font-color-label;
    background-color: #f7f7f7;

    &.t-button--hover {
      &::after {
        background-color: inherit;
      }
    }
  }

  .confirm-button {
    width: 450rpx;
  }
}

.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: $popup-z-index - 1;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;

  &--show {
    opacity: 1;
    visibility: visible;
  }
}
