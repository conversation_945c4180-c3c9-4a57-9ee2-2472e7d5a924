<wxs src="./index.wxs" module="utils" />

<view class="picker-mask {{ popupVisible ? 'picker-mask--show' : '' }}" bindtap="handleBack"></view>

<!-- placement="bottom"  -->
<view class="picker-popup-layout {{ popupVisible ? 'picker-popup-layout--show' : '' }}">
  <view class="picker-popup-container" catchtouchmove="handleTouchMove">
    <!-- <header title="{{ title }}" fixed="{{ false }}" bindback="handleBack" /> -->
    <view class="picker-popup-header">
      <view class="picker-popup-title">{{ title }}</view>
      <!-- <view class="close" bindtap="handleBack"></view> -->
    </view>

    <slot></slot>

    <view class="picker-popup-multiple" wx:if="{{ multiple }}">
      <view class="multiple-text">
        已选(<text class="multiple-num">{{ checkedList.length | 0 }} </text>/{{ limit }})
      </view>

      <view class="multiple-scroll">
        <view class="multiple-checked">
          <t-tag wx:for="{{ checkedList }}" wx:key="index" data-value="{{ item.value }}" closable variant="outline" bindclose="handleClose"> {{ item.label }} </t-tag>
        </view>
      </view>
    </view>

    <view class="picker-popup-main">
      <view class="picker-popup-content">
        <scroll-view class="picker-popup-side" scroll-y scroll-with-animation>
          <t-side-bar value="{{ sideBarIndex }}" bindchange="handleSideBarChange">
            <t-side-bar-item class="side-bar-item" wx:for="{{ options }}" wx:key="index" value="{{ index }}" label="{{ item[optionsLabel] }}" badge-props="{{ { dot: utils.some(level, optionsValue, optionsChildren, item[optionsChildren], checkedList) } }}" />
          </t-side-bar>
        </scroll-view>

        <view class="picker-popup-scroll {{ multiple ? '' : 'safe-area' }}">
          <scroll-view class="scroll-content" scroll-y scroll-with-animation enhanced scroll-top="{{ scrollTop }}" bindscroll="handleScroll" binddragstart="handleDragStart">
            <block wx:if="{{ level === 2 }}">
              <view wx:for="{{ options }}" wx:key="index" wx:for-index="index" class="picker-popup-section">
                <view class="picker-popup-title" wx:if="{{ item[optionsLabel] && item[optionsValue].length }}"> {{ item[optionsLabel] }} </view>

                <t-grid column="{{ column }}" gutter="{{ gutter }}" border="{{ false }}">
                  <block wx:for="{{ item[optionsChildren] }}" wx:key="index">
                    <t-grid-item>
                      <view class="custom-checkbox {{ utils.includes(item[optionsValue], checkedList) ? 'is-checked' : '' }}" data-label="{{ item[optionsLabel] }}" data-value="{{ item[optionsValue] }}" bindtap="handleClick">
                        <text class="custom-checkbox-text">{{ item[optionsLabel] }}</text>
                      </view>
                    </t-grid-item>
                  </block>
                </t-grid>
              </view>
            </block>

            <block wx:else>
              <view wx:for="{{ options }}" wx:key="index" wx:for-index="index" class="picker-popup-section">
                <block wx:for="{{ item[optionsChildren] }}" wx:key="index">
                  <view class="picker-popup-title" wx:if="{{ item[optionsLabel] && item[optionsValue].length }}"> {{ item[optionsLabel] }} </view>

                  <t-grid column="{{ column }}" gutter="{{ gutter }}" border="{{ false }}">
                    <block wx:for="{{ item[optionsChildren] }}" wx:key="index">
                      <t-grid-item>
                        <view class="custom-checkbox {{ utils.includes(item[optionsValue], checkedList) ? 'is-checked' : '' }}" data-label="{{ item[optionsLabel] }}" data-value="{{ item[optionsValue] }}" bindtap="handleClick">
                          <text class="custom-checkbox-text">{{ item[optionsLabel] }}</text>
                        </view>
                      </t-grid-item>
                    </block>
                  </t-grid>
                </block>
              </view>
            </block>
          </scroll-view>
        </view>
      </view>
    </view>

    <view class="picker-popup-footer" wx:if="{{ multiple }}">
      <t-button class="reset-button" theme="light" bindtap="handleReset">重置</t-button>
      <t-button class="confirm-button" theme="primary" bindtap="handleConfirm">确定</t-button>
    </view>
  </view>
</view>
