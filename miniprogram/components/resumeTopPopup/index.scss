@use 'styles/variables' as *;

.resume-top-popup {
  .popup {
    max-height: 82vh;
    display: flex;
  }

  .popup-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .header {
    text-align: center;
    padding: 35rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    border-bottom: 2rpx solid $border-color;
  }

  .resume-top-content {
    flex-grow: 1;
    overflow-y: scroll;

    .resume-tips {
      color: #fa635c;
      line-height: 36rpx;
      font-size: 24rpx;
      padding: 6rpx 30rpx 6rpx 70rpx;
      background:
        url(#{$assets}/resume/warning.png) no-repeat 30rpx center/24rpx 24rpx,
        #fff8f0;

      .operate {
        color: $color-primary;
        display: inline-block;
        border-bottom: 2rpx solid $color-primary;
        line-height: 28rpx;
        padding-right: 14rpx;
        font-weight: bold;
        background: url(#{$assets}/icon/into.png) no-repeat right/10rpx 18rpx;
      }
    }

    .info {
      padding: 36rpx 30rpx 24rpx;

      .label {
        font-size: 28rpx;
        font-weight: bold;
        display: flex;
        margin-bottom: 26rpx;

        .amount {
          color: $color-primary;
        }
      }

      .desc {
        display: flex;

        .item {
          margin-right: 35rpx;
          display: flex;
          align-items: center;
          color: $font-color-label;
          font-size: 24rpx;

          &.primary {
            &::before {
              background-color: $color-primary;
            }
          }

          &::before {
            content: '';
            width: 18rpx;
            height: 18rpx;
            background-color: #ebebeb;
            border-radius: 50%;
            margin-right: 8rpx;
          }
        }
      }
    }

    .resume-top-calendar {
      .t-calendar__title {
        display: none;
      }

      .t-calendar__months {
        height: auto;
      }

      .is-past-selected {
        background: #f5f5f5;
        color: #cccccc;
        pointer-events: none;
      }
    }
  }

  .resume-top-bottom {
    padding: 50rpx 30rpx 40rpx;

    .warning-tips {
      font-size: 24rpx;
      line-height: 36rpx;
      color: $font-color-label;
      margin-bottom: 14rpx;
    }

    .submit {
      margin-top: 40rpx;
      width: 342rpx;
    }
  }

  .rich {
    line-height: 48rpx;
    font-size: 32rpx;
    margin-top: 20rpx;
    color: $font-color-basic;
    text-align: center;
  }
}
