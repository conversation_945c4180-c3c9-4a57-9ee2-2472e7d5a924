import { checkLogin } from '@/utils/store'
import { resumeTopCheck, resumeTopValidate, resumeTopAdd, setResumeShowStatus } from '@/api/person'
import { h5 } from '@/settings'
import { toWebPage } from '@/utils/util'
import { getVipInfo } from '@/api/person'
import { showToast } from '@/utils/util'

type formType = {
  pastDate: number[]
  setDate: number[]
}

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  observers: {
    visible(value) {
      if (!value) return
      this.init()
    }
  },

  /**
   * 页面的初始数据
   */

  options: {
    styleIsolation: 'shared'
  },

  data: {
    isLogin: false,
    realVisible: false,
    calendarVisible: false,
    completeTips: '',
    resumeHideTips: '',
    isFinishThree: false,
    sourceTopAmount: 0,
    warmTips: '',
    expireTips: '',
    openUrl: '',
    minData: 0,
    maxData: 0,
    formData: <formType>{
      pastDate: [],
      setDate: [],
      allDate: []
    },
    format(day: any) {
      const { date, type } = day

      const currentYear = new Date().getFullYear()
      const currentMonth = new Date().getMonth()
      const currentDate = new Date().getDate()

      const minData = new Date(currentYear, currentMonth, currentDate + 1)

      if (new Date(date) < minData && type === 'selected') {
        day.className = 'is-past-selected'
      }
      if (new Date(date) > new Date(currentYear, currentMonth, currentDate + 30)) {
        day.type = 'disabled'
      }
      return day
    },

    settingCallbackVisible: false,
    settingCallbackTitle: '提示',
    settingCallbackContent: '',
    settingCallbackCancelBtn: null,
    settingCallbackConfirmBtn: null,
    settingCallbackConfirmFn: () => {}
  },

  methods: {
    init() {
      const year = new Date().getFullYear()
      const month = new Date().getMonth()
      const date = new Date().getDate()
      const lastDate = new Date(year, month + 3, 0).getDate()
      const minData = new Date(year, month, date + 1).getTime()
      const maxData = new Date(year, month + 2, lastDate).getTime()

      this.setData(
        {
          isLogin: checkLogin(),
          minData,
          maxData
        },
        () => {
          const { isLogin } = this.data
          isLogin && this.fetchResumeTopCheck()
        }
      )
    },

    async fetchVipInfo() {
      const {
        buyUrl: { jobFast }
      } = await getVipInfo()

      this.setData({ openUrl: `${h5}${jobFast}` })
    },

    async fetchResumeTopCheck() {
      const res = await resumeTopCheck()
      const {
        isFinishThree,
        completeTips,
        resumeHideTips,
        warmTips,
        expireTips,
        sourceTopAmount,
        type,
        resumeTopConfig
      } = res
      const formData = <any>{
        pastDate: [],
        setDate: [],
        allDate: []
      }
      if (type === 1) {
        resumeTopConfig.forEach((item: any) => {
          const { status, setDate } = item
          const date = setDate.replace(/-/g, '/')
          const timestamp = new Date(date).getTime()
          formData[status === '1' ? 'setDate' : 'pastDate'].push(timestamp)
          formData.allDate.push(timestamp)
        })
        this.setData({
          completeTips,
          resumeHideTips,
          isFinishThree,
          warmTips,
          expireTips,
          sourceTopAmount,
          formData,
          realVisible: true,
          calendarVisible: true
        })
      } else {
        await this.fetchVipInfo()
        wx.nextTick(() => {
          const { openUrl } = this.data
          toWebPage(openUrl)
        })
      }
    },

    onVisibleChange(e: any) {
      this.setData({ calendarVisible: e.detail.visible, realVisible: e.detail.visible })
    },

    closePopup() {
      this.setData({ calendarVisible: false, realVisible: false, settingCallbackVisible: false })
    },

    handlePerfect() {
      const { isFinishThree } = this.data
      const url = isFinishThree ? '/packages/resume/index' : '/packages/resume/required'
      wx.navigateTo({
        url
      })
    },

    handleSelect(e: WechatMiniprogram.CustomEvent) {
      const { value } = e.detail
      const { pastDate } = this.data.formData

      const setData: number[] = []
      value.forEach((item: number) => {
        !pastDate.includes(item) && setData.push(item)
      })
      this.setData({ 'formData.setDate': setData })
    },

    handleSettingDialogCancel() {
      this.setData({ settingCallbackVisible: false })
    },

    handleSettingDialogConfirm() {
      const { settingCallbackConfirmFn } = this.data
      settingCallbackConfirmFn()
    },

    timestampToLocalDateArray(dateArray: number[]) {
      return dateArray.map((item) => {
        const dateObj = new Date(item)
        const year = dateObj.getFullYear()
        const month = dateObj.getMonth() + 1
        const date = dateObj.getDate()

        return `${year}-${month}-${date}`
      })
    },

    titleCase(str: string) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },

    handleOpenResume() {
      setResumeShowStatus().then(() => {
        showToast('简历公开成功！')
        this.setData({ resumeHideTips: '' })
      })
    },

    setSettingCallbackDialogData(options: any, callback = () => {}) {
      Object.keys(options).forEach((item: string) => {
        const key = `settingCallback${this.titleCase(item)}`
        const value = options[item]
        this.setData({
          [key]: value
        })
      })
      this.setData({
        settingCallbackVisible: true,
        settingCallbackConfirmFn: callback
      })
    },

    async updateResumeTop(data = {}) {
      const { setDate } = this.data.formData
      const value = this.timestampToLocalDateArray(setDate)

      const res = await resumeTopAdd({ set_date: value, ...data })
      const { type, title, message } = res

      let options = {}
      let callback = () => {}

      if (type === 1 || type === 2) {
        options = {
          cancelBtn: null,
          confirmBtn: {
            content: '我知道了',
            variant: 'base'
          }
        }
        callback = () => {
          this.closePopup()
        }
      }

      if (type === 3) {
        options = {
          cancelBtn: {
            content: '重新设置',
            variant: 'base'
          },
          confirmBtn: {
            content: '保持现在的设置',
            variant: 'base'
          }
        }
        callback = () => {
          this.updateResumeTop({ isContinue: 1 })
        }
      }

      if (type === 4) {
        options = {
          cancelBtn: null,
          confirmBtn: {
            content: '我知道了',
            variant: 'base'
          }
        }
        callback = () => {
          this.closePopup()
        }
      }

      const dialogOptions = {
        title,
        content: message,
        ...options
      }
      this.setSettingCallbackDialogData(dialogOptions, callback)
    },

    fetchResumeTopValidate() {
      const { setDate } = this.data.formData
      const value = this.timestampToLocalDateArray(setDate)
      return resumeTopValidate({ set_date: value })
    },

    async submit() {
      this.fetchResumeTopValidate().then(() => {
        this.updateResumeTop()
      })
    }
  }
})
