<view class="resume-top-popup">
  <t-popup t-class="popup" t-class-content="popup-content" visible="{{ realVisible }}" placement="bottom" bind:visible-change="onVisibleChange" close-btn>
    <view class="header">简历置顶日期设置</view>

    <view class="resume-top-content">
      <view wx:if="{{ completeTips }}" class="resume-tips"
        >{{ completeTips }}
        <view class="operate" bindtap="handlePerfect">立即完善</view>
      </view>
      <view wx:if="{{ resumeHideTips }}" class="resume-tips"
        >{{ resumeHideTips }}
        <view class="operate" bind:tap="handleOpenResume">立即公开</view>
      </view>

      <view class="info">
        <view class="label">
          剩余置顶天数:<view class="amount">{{ sourceTopAmount }}</view>
        </view>
        <view class="desc">
          <view class="item">代表已生效的置顶</view>
          <view class="item primary">代表已配置未生效的置顶</view>
        </view>
      </view>

      <t-calendar wx:if="{{ calendarVisible }}" t-class="resume-top-calendar" use-popup="{{ false }}" min-date="{{ minData }}" max-date="{{ maxData }}" value="{{ formData.allDate }}" format="{{ format }}" type="multiple" bind:select="handleSelect"> </t-calendar>
    </view>

    <view class="resume-top-bottom">
      <view wx:if="{{ warmTips }}" class="warning-tips">{{ warmTips }}</view>
      <view wx:if="{{ expireTips }}" class="warning-tips">{{ expireTips }}</view>
      <t-button class="submit" bindtap="submit" block theme="primary" size="large">确认设置（共{{ formData.setDate.length }}天）</t-button>
    </view>
  </t-popup>

  <t-dialog overlay-props="{{ { zIndex: 11600 } }}" z-index="{{ 11700 }}" visible="{{ settingCallbackVisible }}" title="{{ settingCallbackTitle }}" cancel-btn="{{ settingCallbackCancelBtn }}" confirm-btn="{{ settingCallbackConfirmBtn }}" bindcancel="handleSettingDialogCancel" bindconfirm="handleSettingDialogConfirm">
    <rich-text class="rich" slot="content" nodes="{{ settingCallbackContent }}"></rich-text>
  </t-dialog>
</view>
