<block wx:if="{{ type === 'primary' }}">
  <t-navbar style="{{ headerStyle }}" title="{{ title }}" left-arrow="{{ leftArrow }}" visible="{{ visible }}" class="navbar-container navbar-primary">
    <navigator slot="left" class="navbar-logo" target="" url="/pages/home/<USER>" open-type="switchTab"></navigator>
  </t-navbar>
</block>
<block wx:else>
  <!-- <t-navbar title="{{ title }}" left-arrow="{{ leftArrow }}" visible="{{ visible }}" class="navbar-container"> </t-navbar> -->
  <t-navbar style="{{ headerStyle }}" title="{{ title }}">
    <view slot="capsule" class="custom-capsule">
      <t-icon size="20" bind:tap="onBack" aria-role="button" aria-label="返回" name="chevron-left" class="custom-capsule__icon" />
      <t-icon size="20" bind:tap="onGoHome" aria-role="button" aria-label="首页" name="home" class="custom-capsule__icon" />
    </view>
  </t-navbar>
</block>
