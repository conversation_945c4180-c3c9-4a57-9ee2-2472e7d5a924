Component({
  options: {
    styleIsolation: 'shared'
  },
  /**
   * 组件的属性列表
   */
  properties: {
    scrollTop: {
      type: Number,
      value: 0
    },
    type: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    visible: {
      type: Boolean,
      value: true
    },
    leftArrow: {
      type: Boolean,
      value: true
    },
    placeholder: {
      type: String,
      value: '请输入'
    }
  },

  lifetimes: {
    attached() {
      const headerStyle = `--td-navbar-height: ${36}px`
      this.setData({ headerStyle })
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    headerStyle: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onBack() {
      wx.navigateBack()
    },

    onGoHome() {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  }
})
