.navbar-container {
  --td-spacer-1: 30rpx;
}

.navbar-primary {
  .navbar-logo {
    display: block;
    width: 208rpx;
    height: 52rpx;
    background: url('//img.gaoxiaojob.com/uploads/static/image/logo/mini_logo.png') no-repeat left/contain;
  }

  .t-navbar__content {
    background: url('//img.gaoxiaojob.com/uploads/mini/nav/primary.png') no-repeat center top/cover;
  }
}

.navbar-search {
  .search-content {
    background-color: #f7f7f7;
    margin-right: 20rpx;
    height: 64rpx;
    border-radius: 64rpx;
    display: flex;
    align-items: center;
    margin-left: 10rpx;

    .search-prefix {
      padding-right: 20rpx;
      padding-left: 30rpx;
      display: flex;
      align-items: center;

      .icon {
        margin-left: 12rpx;
      }
    }

    .t-input {
      --td-input-bg-color: transparent;
      --td-font-size-m: 28rpx;

      padding: 0 20rpx;
      height: 100%;
      flex-grow: 1;

      &::after {
        content: '';
        left: 0;
        width: 2rpx;
        height: 24rpx;
        background: #333;
        opacity: 0.4;
        top: 20rpx;
      }
    }
  }
}

.custom-capsule {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-capsule__icon {
  flex: 1;
  position: relative;
}

.custom-capsule__icon + .custom-capsule__icon:before {
  content: '';
  display: block;
  position: absolute;
  left: -1px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 36rpx;
  background: #e7e7e7;
}
