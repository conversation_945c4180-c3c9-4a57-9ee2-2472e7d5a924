Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: { type: String, value: '' },

    fixed: { type: Boolean, value: true },

    style: { type: String, value: '' },

    arrow: { type: Boolean, value: true }
  },

  /**
   * 组件的初始数据
   */
  data: {
    realStyle: ''
  },

  lifetimes: {
    attached() {
      const { style } = this.data
      const realStyle = `${this.getComputedStyle()}${style}`

      this.setData({ realStyle })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    getComputedStyle() {
      const { top, height } = wx.getMenuButtonBoundingClientRect()
      return `--td-navbar-padding-top: ${top}px; --td-navbar-height: ${height + 6}px;`
    },

    handleBack() {
      this.triggerEvent('back')
    }
  }
})
