<view class="job-list-dialog">
  <t-popup prevent-scroll-through="{{ false }}" visible="{{ dialogVisible }}" placement="bottom" close-btn bind:visible-change="close">
    <view class="block">
      <view class="header">简历投递</view>

      <view class="job-list">
        <view class="job-item">
          <t-radio-group t-class="theme-card" bind:change="onChange" value="{{ jobId }}">
            <t-radio wx:for="{{ applyJobList }}" wx:key="index" wx:for-item="item" label="{{ item.name }}" value="{{ item.id }}" disabled="{{ item.type }}" />
          </t-radio-group>
        </view>
      </view>
      <view class="footer">
        <t-button theme="primary" bindtap="handleSubmit" disabled="{{ isCanDelivery }}">确定</t-button>
      </view>
    </view>
  </t-popup>

  <delivery-popup id="deliveryPopup" bindclose="close" />
</view>

<t-dialog visible="{{ beforeApplyDialogVisible }}" title="{{ beforeApplyDialogTitle }}" content="{{ beforeApplyDialogContent }}" confirm-btn="{{ beforeApplyDialogConfirmBtn }}" cancel-btn="{{ beforeApplyDialogCancelBtn }}" bind:confirm="handleCheckDialogConfirm" bind:cancel="handleCheckDialogCancel" />
