// components/jobListDialog.ts
import { showToast } from '@/utils/util'
import beforeDeliveryMixin from '@/mixins/beforeDeliveryMixin'

Component({
  options: {
    styleIsolation: 'shared'
  },

  behaviors: [beforeDeliveryMixin],

  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    applyJobList: {
      type: Array,
      value: []
    }
  },

  observers: {
    visible(value: boolean) {
      this.setData({
        jobId: this.data.applyJobList
          .filter(item => item.active)
          .map(item => item.id)
          .join()
      });
      this.setData({ dialogVisible: value });
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    dialogVisible: false,
    isCanDelivery: false,

    jobId: '',
    deliveryPopup: <any>{}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    load() {
      this.data.deliveryPopup = this.selectComponent('#deliveryPopup')
    },

    onChange(event: any) {
      const { value } = event.detail
      this.setData({ jobId: value })
    },

    async handleSubmit() {
      const { jobId } = this.data

      if (jobId === '') {
        showToast('请选择职位')
        return
      }
      this.load()

      this.handleCheckJobInfo(jobId)
    },

    openDeliveryPopup(data: any) {
      const { jobId } = this.data

      this.data.deliveryPopup.open({ ...data, jobId })
    },

    close() {
      this.setData({ dialogVisible: false, jobChecked: false })

      this.triggerEvent('close', { value: false })
    }
  }
})
