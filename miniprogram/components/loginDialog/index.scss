@use 'styles/variables' as *;

.login-dailog {
  .trigger-content {
    .login-tips {
      color: $color-primary;
      text-align: center;
      margin-bottom: 34rpx;
    }

    .login-btn {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      background-color: $color-primary;
      color: $color-white;
      text-align: center;
      font-size: 30rpx;
      width: 480rpx;
      margin: 0 auto 60rpx;
    }
  }
}

.login-popup {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 99999;
  background-color: $font-color-tips;
}

.login-container {
  width: 588rpx;
  display: flex;
  flex-direction: column;

  .login-main {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 60rpx 54rpx 52rpx;

    .login-btn {
      height: 106rpx;
      margin-bottom: 40rpx;
      font-size: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;

      .icon {
        width: 46rpx;
        height: 46rpx;
        margin-right: 15rpx;
      }
    }

    .fast-login {
      border-color: $color-primary;
      background-color: $color-primary;
      color: #fff;
    }

    .mobile-login {
      border: 2rpx solid $border-color;
      background-color: transparent;
    }

    .agreement {
      display: flex;
      align-items: center;
      font-size: 26rpx;

      .unchecked {
        color: $border-color;
      }

      .checked {
        color: $color-primary;
      }

      .checkbox-content {
        margin-top: 0;
      }
      .content {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: $font-color;
      }

      .checkbox-icon {
        margin-right: 9rpx;
      }

      .checkbox-border {
        display: none;
      }

      .checkbox {
        padding: 0;
      }

      .link {
        color: $color-primary;
      }
    }
  }

  .close-circle {
    width: 58rpx;
    color: #fff;
    align-self: center;
    margin-top: 40rpx;
  }
}
