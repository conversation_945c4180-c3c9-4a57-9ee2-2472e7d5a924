import { showToast, setData } from '@/utils/util'
import { mobileCodeLogin } from '@/api/entry'
import { assetsURL } from '@/settings'
import { authSuccessCallback, checkLoginByCode, wxLogin } from '@/utils/auth'

Component({
  options: {
    virtualHost: true
  },

  externalClasses: ['c-class', 'c-trigger-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },

    showTrigger: {
      type: Boolean,
      value: false
    },

    title: {
      type: String,
      value: '更多优选职位，登录查看'
    }
  },

  observers: {
    visible(value) {
      if (!value) {
        this.setData({ dialogVisible: value })
        return
      }

      this.data.trigger = 'visible'
      this.handleCheck(value)
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    dialogVisible: false,
    isAgree: false,
    trigger: 'visible',
    wechatImage: `${assetsURL}/icon/wechat.png`,
    mobileImage: `${assetsURL}/icon/mobile.png`
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleSetData(e: any) {
      setData(e, this)
    },

    close() {
      this.setData(
        {
          dialogVisible: false
        },
        () => {
          this.closeTrigger()
        }
      )
    },

    loginSuccess() {
      this.close()
      this.loginSuccessTrigger()
    },

    loginSuccessTrigger() {
      this.triggerEvent('loginSuccess')
    },

    closeTrigger() {
      // * trigger: 'visible' | 'button'
      const { trigger } = this.data

      this.triggerEvent('close', { value: false, trigger })
    },

    handleCodeLogin() {
      showToast('请勾选同意后再登录')
    },

    // * 组件按钮点击显示登录弹窗
    handleLogin() {
      this.data.trigger = 'button'
      this.handleCheck()
    },

    async handleCheck(visible = true) {
      try {
        const data = await checkLoginByCode()

        authSuccessCallback(data, () => this.loginSuccess())
      } catch {
        this.setData({ dialogVisible: visible })
      }
    },

    async getPhoneNumber(event: WechatMiniprogram.ButtonGetPhoneNumber) {
      const { /* iv, encryptedData, */ code: mobileCode } = event.detail

      if (!mobileCode) return

      const unionCode = await wxLogin()
      const data = await mobileCodeLogin({ unionCode, mobileCode })

      authSuccessCallback(data, () => {
        this.loginSuccess()
      })
    },

    smsLogin() {
      this.setData({ dialogVisible: false })
      wx.navigateTo({ url: '/packages/person/login' })
    }
  }
})
