<view class="login-dailog c-class">
  <view class="trigger-content" wx:if="{{ showTrigger }}">
    <view class="home-container">
      <view class="login-tips">{{ title }}</view>
      <t-button hover-class="none" class="login-btn" block theme="primary" bind:tap="handleLogin">立即登录</t-button>
    </view>
  </view>

  <root-portal enable="{{ true }}">
    <view hidden="{{ !dialogVisible }}" class="login-popup">
      <view class="login-container">
        <view class="login-main">
          <button plain hidden="{{ !isAgree }}" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" class-="login-btn fast-login">
            <!-- <image class="icon" src="{{ wechatImage }}" mode="aspectFit" lazy-load="false" /> -->
            手机号快速验证
          </button>
          <button plain hidden="{{ isAgree }}" bind:tap="handleCodeLogin" class="login-btn fast-login">
            <!-- <image class="icon" src="{{ wechatImage }}" mode="aspectFit" lazy-load="false" /> -->
            手机号快速验证
          </button>

          <button plain class="login-btn mobile-login" bind:tap="smsLogin">
            <image class="icon" src="{{ mobileImage }}" mode="aspectFit" lazy-load="false" />
            手机号注册/登录
          </button>

          <view class="agreement">
            <t-checkbox bind:change="handleSetData" data-key="isAgree" t-class="checkbox" icon="none" t-class-border="checkbox-border" t-class-content="checkbox-content" value="{{ isAgree }}">
              <view class="content" slot="content">
                <t-icon name="{{ isAgree ? 'check-circle-filled' : 'circle' }}" size="32rpx" t-class="checkbox-icon {{ isAgree ? 'checked' : 'unchecked' }}" />
                我已阅读并同意
              </view>
            </t-checkbox>
            <navigator class="link" url="/pages/link/index?url=https://agreement.gaoxiaojob.com/docs/service-agreement" hover-class="none">用户协议</navigator>
            和
            <navigator class="link" url="/pages/link/index?url=https://agreement.gaoxiaojob.com/docs/privacy-policy" hover-class="none">隐私条款</navigator>
          </view>
        </view>

        <t-icon bind:tap="close" name="close-circle" size="58rpx" t-class="close-circle" />
      </view>
    </view>
  </root-portal>
</view>
