<view class="job-item c-class {{ detail.status == '0' ? 'offline' : '' }}" data-id="{{ detail.id }}" data-msg="{{ detail.toastMessage }}" bindtap="handleClick">
  <view class="list-top">
    <view class="title">
      {{ detail.jobName }}
      <view class="tag">
        <view wx:if="{{ detail.isTop == 1 && isLogin }}" class="worry">急</view>
        <view wx:if="{{ detail.isEstablishment == 1 }}" class="organization">编</view>
        <view wx:if="{{ detail.isFast == 1 }}" class="fast">快</view>
      </view>
    </view>
    <view class="salary">{{ detail.wage }}</view>
  </view>
  <view wx:if="{{ detail.announcementName }}" class="announcement">{{ detail.announcementName }}</view>
  <view class="tag-content">
    <view wx:if="{{ detail.education }}" class="tag">{{ detail.education }}</view>
    <view wx:if="{{ detail.majorName }}" class="tag">{{ detail.majorName }}</view>
    <view wx:if="{{ detail.amount }}" class="tag">招{{ detail.amount }}人</view>
    <view wx:if="{{ showExperience && detail.experience }}" class="tag">{{ detail.experience }}</view>
    <view wx:if="{{ showCity && detail.city }}" class="tag">{{ detail.city }}</view>
  </view>
  <view wx:if="{{ showWelfare && detail.welfareTagArr.length }}" class="welfare">
    <view class="welfare-title">有福利</view>
    <view class="welfare-content">{{ welfareTxt }}</view>
  </view>
  <view class="job-bottom">
    <view class="unit">{{ detail.companyName }}</view>
    <view class="aside release-time" wx:if="{{ showReleaseTime }}">{{ detail.refreshDate }}</view>
    <view class="aside" wx:else>{{ detail.city }}</view>
  </view>
</view>
