@use 'styles/variables' as *;

.job-item {
  margin-top: 30rpx;
  .list-top {
    display: flex;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      flex-grow: 1;
      margin-bottom: 20rpx;

      .tag {
        height: 30rpx;
        display: inline-flex;
        align-items: center;
        transform: translateY(-6rpx);

        .worry,
        .organization,
        .fast {
          font-size: 22rpx;
          line-height: 30rpx;
          width: 30rpx;
          height: 30rpx;
          border-radius: 4rpx;
          text-align: center;
          margin-right: 10rpx;
        }

        .worry {
          background-color: #fff0ef;
          color: $color-point;
        }

        .organization {
          background-color: #f0ffdc;
          color: #4fbc67;
        }

        .fast {
          color: transparent;
          background: $tag-primary-background url('//img.gaoxiaojob.com/uploads/mini/icon/lightning.png') no-repeat
            center/ 12rpx 22rpx;
        }
      }
    }

    .salary {
      margin-left: 100rpx;
      flex-shrink: 0;
      color: $color-point;
      font-size: 32rpx;
      font-weight: bold;
    }
  }

  .announcement {
    @include utils-ellipsis;
    margin-bottom: 16rpx;
  }

  .tag-content {
    display: flex;
    flex-wrap: nowrap;

    .tag {
      line-height: 44rpx;
      margin-bottom: 22rpx;
      padding: 0 12rpx;
      max-width: 240rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border-radius: 4rpx;
      background-color: #f4f6fb;
      color: $font-color-basic;
      font-size: 24rpx;

      & + .tag {
        margin-left: 12rpx;
      }
    }
  }

  .welfare {
    display: flex;
    font-size: 24rpx;
    margin: 0rpx 0 22rpx;
    height: 36rpx;
    align-items: stretch;

    .welfare-title {
      font-size: 22rpx;
      width: 82rpx;
      text-align: center;
      color: $color-white;
      background-color: $color-primary;
      border-radius: 4rpx 0 0 4rpx;
      line-height: 36rpx;
    }

    .welfare-content {
      line-height: 36rpx;
      background-color: #fff9f0;
      padding: 0 9rpx;
      border-radius: 0 4rpx 4rpx 0;
      color: $font-color-basic;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .job-bottom {
    display: flex;
    font-size: 24rpx;

    .unit {
      flex-grow: 1;
      @include utils-ellipsis;
    }

    .aside {
      flex-shrink: 0;
      padding-left: 34rpx;
      margin-left: 100rpx;
      max-width: 150rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: $font-color-tips;
      background: url(#{$assets}/icon/address.png) no-repeat left/24rpx;

      &.release-time {
        background: url(#{$assets}/icon/time.png) no-repeat left/24rpx;
      }
    }
  }

  &.offline {
    background:
      url('//img.gaoxiaojob.com/uploads/mini/icon/offline.png') no-repeat right top / 110rpx 96rpx,
      #fff;

    .list-top,
    .announcement,
    .tag-content,
    .welfare,
    .job-bottom {
      opacity: 0.6;
    }

    .salary {
      display: none;
    }
  }
}
