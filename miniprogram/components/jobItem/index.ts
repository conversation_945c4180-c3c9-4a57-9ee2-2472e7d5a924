import { defaultDuration } from '@/settings'

Component({
  options: {
    virtualHost: true
  },
  externalClasses: ['c-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    showWelfare: {
      type: Boolean,
      value: true
    },
    isLogin: {
      type: Boolean,
      value: false
    },
    showExperience: {
      type: Boolean,
      value: true
    },
    showCity: {
      type: Boolean,
      value: true
    },
    showReleaseTime: {
      type: Boolean,
      value: false
    },
    detail: {
      type: Object,
      value: {
        welfareTagArr: []
      }
    }
  },

  observers: {
    'detail.welfareTagArr': function (value) {
      if (Array.isArray(value)) {
        this.setData({
          welfareTxt: value.join('、')
        })
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    welfareTxt: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleClick(event: WechatMiniprogram.CustomEvent) {
      const { id, msg = '' } = event.currentTarget.dataset

      if (msg) {
        wx.showToast({ title: msg, icon: 'none', duration: defaultDuration })
        return
      }

      wx.navigateTo({ url: `/packages/job/detail/index?id=${id}` })
    }
  }
})
