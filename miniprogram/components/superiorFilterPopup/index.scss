@use 'sass:meta';
@use 'styles/variables' as *;

.superior-filter {
  position: relative;

  .superior-filter-trigger {
    display: inline-flex;
    align-items: baseline;
    position: relative;
    white-space: nowrap;

    &::after {
      content: '';
      display: inline-block;
      width: 0rpx;
      height: 0rpx;
      right: 0;
      bottom: 22rpx;
      border-left: 5rpx solid transparent;
      border-right: 5rpx solid #c7c7c7;
      border-top: 5rpx solid transparent;
      border-bottom: 5rpx solid #c7c7c7;
      margin-left: 10rpx;
    }

    &::before {
      content: '';
      width: 41rpx;
      height: 19rpx;
      top: -10rpx;
      right: -14rpx;
      position: absolute;
      background: url(//img.gaoxiaojob.com/uploads/mini/icon/vip-radius.png) no-repeat center/100% 100%;
    }

    &.active {
      font-weight: bold;
      color: $color-primary;

      &::after {
        border-color: transparent $color-primary $color-primary transparent;
      }
    }

    &.is-card {
      line-height: 48rpx;
      font-size: 24rpx;
      border-radius: 8rpx;
      background-color: #f4f6fb;
      border: 2rpx solid #f4f6fb;
      color: $font-color-basic;
      padding: 0 32rpx 0 18rpx;

      &::after {
        content: '';
        position: absolute;
        right: 14rpx;
        bottom: 14rpx;
        border: 4rpx solid #c7c7c7;
        border-left-color: transparent;
        border-top-color: transparent;
      }

      &::before {
        width: 50rpx;
        right: 0rpx;
        top: 0rpx;
        background: url(//img.gaoxiaojob.com/uploads/mini/icon/vip-radius-reverse.png) no-repeat right/100% 100%;
      }

      &.active {
        background-color: $color-primary-background;
        border-color: $color-primary;
        font-weight: normal;
        color: $color-primary;
      }
    }
  }

  .superior-filter-tips {
    position: absolute;
    background-color: #333;
    border-radius: 8rpx;
    color: #fff;
    font-size: 24rpx;
    padding: 10rpx 17rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 99;
    width: auto;
    white-space: nowrap;
    transform: translateX(-50%) translateY(6rpx);
    left: 50%;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      width: 12rpx;
      height: 12rpx;
      background-color: #333;
      transform: rotate(45deg) translateY(-50%);
    }

    .superior-filter-tips-close {
      margin-left: 20rpx;
      width: 24rpx;
      height: 24rpx;
      background: url(//img.gaoxiaojob.com/uploads/mini/icon/close-white.png) no-repeat center/contain;
      flex-shrink: 0;
    }
  }
}

.superior-filter-popup {
  $primary: #795021;
  --td-popup-border-radius: 24rpx;

  .superior-filter-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 102rpx;
    background: url(//img.gaoxiaojob.com/uploads/mini/popup/vip-filter-header-bg.png) no-repeat left top/cover;

    .superior-filter-header-title {
      font-size: 36rpx;
      font-weight: bold;
      color: $primary;
      padding-right: 73rpx;
      background: url(//img.gaoxiaojob.com/uploads/mini/popup/vip-icon.png) no-repeat right/63rpx 24rpx;
    }

    .superior-filter-header-close {
      position: absolute;
      right: 33rpx;
      width: 30rpx;
      height: 30rpx;
      background: url(//img.gaoxiaojob.com/uploads/mini/popup/close.png) no-repeat center/contain;
    }
  }

  .superior-filter-content {
    padding: 36rpx 30rpx 30rpx;

    .superior-filter-list {
      & ~ .superior-filter-list {
        margin-top: 60rpx;
      }

      .label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30rpx;

        .txt {
          display: flex;
          align-items: center;
          font-size: 30rpx;
          font-weight: bold;

          .require {
            font-size: 24rpx;
            font-weight: normal;
            color: $font-color-label;
            margin: 0 6rpx;
          }

          .query {
            width: 30rpx;
            height: 30rpx;
            background: url(//img.gaoxiaojob.com/uploads/mini/icon/question.png) no-repeat center/contain;
          }
        }

        .aside {
          font-size: 24rpx;
          color: $font-color-label;
        }
      }

      .value {
        display: flex;

        .checkbox {
          padding: 0;
          margin-right: 20rpx;

          .checkbox-content {
            margin-top: 0 !important;
          }

          .content {
            width: 216rpx;
            height: 74rpx;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2rpx solid transparent;
            background-color: #f7f7f7;
            color: $font-color;
            font-size: 24rpx;
            border-radius: 8rpx;

            &.is-checked {
              color: $primary;
              background-color: #fff6ed;
              border-color: $color-primary;
            }
          }
        }

        .icon {
          display: none;
        }
      }
    }
  }

  .superior-filter-footer {
    display: flex;
    padding: 30rpx;
    border-top: 2rpx solid #ebebeb;

    .superior-filter-reset {
      font-size: 30rpx;
      font-weight: normal;
      width: 220rpx;
      height: 80rpx;
      background: #f7f7f7;
      border-radius: 40rpx;
      color: $font-color-label;
      line-height: 80rpx;
      margin-right: 20rpx;

      &::after {
        display: none;
      }
    }

    .superior-filter-confirm {
      flex-grow: 1;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 30rpx;
      font-weight: normal;
      color: $primary;
      background: linear-gradient(to right, #fbf1e6, #fee5c8);
      border-radius: 40rpx;

      &::after {
        display: none;
      }
    }
  }
}

.superior-tips-dialog {
  @include tdesign-variables;

  --td-button-light-color: #{$font-color-label};
  --td-radius-default: 16rpx;
  --td-button-light-bg-color: #f7f7f7;

  padding: 0 46rpx;
  box-sizing: border-box;

  &.t-button--hover::after {
    --td-button-light-active-bg-color: #f7f7f7;
    --td-button-light-active-border-color: #f7f7f7;
  }

  .dialog-content {
    padding-left: 0;
    padding-right: 0;
  }

  .superior-dialog-content {
    margin-top: 16px;
    color: $font-color-basic;
    text-align: center;
    font-size: 32rpx;
    line-height: 48rpx;
    padding: 0 4rpx;

    .description {
      display: flex;
      font-size: 24rpx;
      margin-top: 14rpx;
    }

    .footer {
      margin-top: 50rpx;
      display: flex;

      .btn {
        &::after {
          display: none;
        }
      }

      .cancel {
        width: calc((100% - 25rpx) / 2);
        margin-right: 25rpx;
        color: $font-color-label;
        background-color: #f7f7f7;
      }

      .confirm {
        flex-grow: 1;
      }
    }
  }
}
