import { toWebPage } from '@/utils/util'
import { h5 } from '@/settings'
import { getVipFilterInfo } from '@/api/person'
import { getStorage, setStorage } from '@/utils/store'

Component({
  options: {
    virtualHost: true
  },

  externalClasses: ['c-class'],

  /**
   * 组件的属性列表
   */
  properties: {
    // 1职位、2公告
    type: {
      type: Number,
      value: 1
    },
    value: {
      type: Object,
      value: {
        heat: '',
        isEstablishment: ''
      }
    },
    isCardTrigger: {
      type: Boolean,
      value: false
    }
  },

  lifetimes: {
    attached: function () {
      const {
        type,
        value: { isEstablishment, heat }
      } = this.data

      const heatLabel = type == 1 ? '职位热度' : '公告热度'
      const hasContain = !!(isEstablishment || heat)
      this.setData({ hasContain, heatLabel })

      this.isEverydayFirstOpen()
    }
  },

  pageLifetimes: {
    show() {
      this.fetchVipFilterInfo()
    }
  },

  observers: {
    value: function (value) {
      const { isEstablishment, heat } = value
      this.setData(
        {
          isEstablishment: isEstablishment ? [isEstablishment] : [],
          heat: heat ? [heat] : []
        },
        () => {
          this.handleContain()
        }
      )
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isLogin: false,
    isVip: false,

    loginVisible: false,
    tipsVisible: false,
    visible: false,
    dialogVisible: false,

    hasContain: false,

    heatLabel: '职位热度',

    establishmentOptions: [
      {
        label: '含编制',
        value: '1'
      }
    ],

    heatOptions: [
      {
        label: '一般',
        value: '1'
      },
      {
        label: '较热',
        value: '2'
      },
      {
        label: '火爆',
        value: '3'
      }
    ],

    isEstablishment: <any>[],
    heat: <any>[],

    openVipTipsData: {
      dialogTitle: '提示',
      dialogContent: '开通VIP服务即可使用高级筛选功能，精准捕捉目标职位&公告',
      dialogDescription: '高级筛选功能包括：职位&公告 编制查询、热度情况筛选等',
      dialogCancelBtn: '取消',
      dialogConfirmBtn: '升级VIP',
      dialogConfirmBtnUrl: ''
    },

    asideLabel: {
      establishment: '',
      heat: ''
    },
    asideLabelOptions: {
      job: {
        establishment: '“上岸”快人一步',
        heat: '追踪职位实时热度'
      },
      announcement: {
        establishment: '捕捉每一次入编机会',
        heat: '追踪公告实时热度'
      }
    },

    explainOptions: {
      job: {
        establishment: {
          dialogTitle: '编制查询说明',
          dialogContent: '可查询包含行政编制/事业编制/备案制等编制类型的优质职位',
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        },
        heat: {
          dialogTitle: '职位热度说明',
          dialogContent: `根据职位的关注度、招录人数竞争比综合分析得出

                          一般：表示关注人数较少，可重点关注；

                          较热：表示该职位备受欢迎，可持续关注；

                          火爆：表示该职位竞争激烈，可抓紧机会争取。`,
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        }
      },
      announcement: {
        establishment: {
          dialogTitle: '编制查询说明',
          dialogContent: '可查询包含编制职位的优质公告',
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        },
        heat: {
          dialogTitle: '公告热度说明',
          dialogContent: '根据公告的浏览、关注、投递情况等综合分析得出',
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        }
      }
    },

    dialogTitle: '',
    dialogContent: '',
    dialogDescription: '',
    dialogCancelBtn: '',
    dialogConfirmBtn: '',
    dialogConfirmBtnUrl: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 搜索结果页逻辑
    isEverydayFirstOpen() {
      const { route } = <any>getCurrentPages().at(-1)
      const { type } = this.data
      const pageRoutes = ['packages/search/result/index']
      const isContain = pageRoutes.includes(route)
      if (isContain) {
        const { date } = getStorage(`searchResultPage_${type}`) || { date: '' }
        const currentDate = new Date().toLocaleDateString()
        const isFirstOpen = !(date === currentDate)
        this.setData({ tipsVisible: isFirstOpen })
      }
    },

    handleCloseTips() {
      this.setData({ tipsVisible: false })
      const { type } = this.data
      const date = new Date().toLocaleDateString()
      const key = `searchResultPage_${type}`
      setStorage(key, { date })
    },

    async fetchVipFilterInfo() {
      const { isLogin, isVip, url } = await getVipFilterInfo()
      this.setData({
        isLogin,
        isVip,
        'openVipTipsData.dialogConfirmBtnUrl': url
      })
    },

    handleOpen() {
      const { type, asideLabelOptions } = this.data

      const asideLabelKey = type == 1 ? 'job' : 'announcement'

      this.setData({
        visible: true,
        asideLabel: asideLabelOptions[asideLabelKey]
      })

      this.fetchVipFilterInfo()
    },

    handleClose() {
      this.setData({ visible: false })
    },

    handleCloseTipsDialog() {
      this.setData({ dialogVisible: false })
    },

    handleTipsConfirm() {
      const { dialogConfirmBtnUrl } = this.data

      if (dialogConfirmBtnUrl) {
        this.setData({
          dialogVisible: false,
          visible: false
        })
        toWebPage(`${h5}${dialogConfirmBtnUrl}`)
        return
      }

      this.setData({ dialogVisible: false })
    },

    handleOpenTipsDialog(event: WechatMiniprogram.CustomEvent) {
      const {
        currentTarget: {
          dataset: { key }
        }
      } = event

      const { type, explainOptions } = this.data

      const tipsObject: any = type == 1 ? explainOptions['job'] : explainOptions['announcement']
      const {
        dialogTitle,
        dialogContent,
        dialogDescription = '',
        dialogCancelBtn = '',
        dialogConfirmBtn = '',
        dialogConfirmBtnUrl = ''
      } = tipsObject[key]

      this.setData({
        dialogTitle,
        dialogContent,
        dialogDescription,
        dialogCancelBtn,
        dialogConfirmBtn,
        dialogConfirmBtnUrl,
        dialogVisible: true
      })
    },

    handleLoginSuccess() {
      this.fetchVipFilterInfo()
      this.triggerEvent('loginSuccess')
    },

    handleCallback(callback = () => {}) {
      const { isLogin, isVip } = this.data
      if (!isLogin) {
        this.setData({ loginVisible: true })
        return
      }
      if (!isVip) {
        const { openVipTipsData } = this.data
        this.setData({
          ...openVipTipsData,
          dialogVisible: true
        })
        return
      }
      callback()
    },

    handleCheckbox(key: string, value: Array<any>) {
      const { length } = value
      const singleValue = length ? [value.at(-1)] : []
      this.setData({
        [key]: singleValue
      })
    },

    handleChange(event: any) {
      const {
        currentTarget: {
          dataset: { key }
        },
        detail: { value }
      } = event

      this.handleCallback(() => {
        this.handleCheckbox(key, value)
      })
    },

    handleReset() {
      this.setData({
        isEstablishment: [],
        heat: []
      })
    },

    handleConfirm() {
      const { heat, isEstablishment, type } = this.data
      const heatKey = type === 1 ? 'applyHeat' : 'announcementHeat'

      this.triggerEvent('change', {
        isEstablishment: isEstablishment.join(),
        [heatKey]: heat.join()
      })

      this.setData({
        visible: false
      })

      this.handleContain()
    },

    handleContain() {
      const { isEstablishment, heat } = this.data
      const hasContain = !!(isEstablishment.length || heat.length)
      this.setData({ hasContain })
    }
  }
})
