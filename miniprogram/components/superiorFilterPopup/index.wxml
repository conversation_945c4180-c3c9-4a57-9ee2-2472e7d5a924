<wxs src="./index.wxs" module="_" />

<view class="superior-filter c-class">
  <view class="superior-filter-trigger {{ isCardTrigger ? 'is-card' : '' }} {{ hasContain ? 'active' : '' }}" bindtap="handleOpen">高级筛选</view>

  <!-- <view class="superior-filter-tips" wx:if="{{ tipsVisible }}">
    含编制查询、热度筛选等高级筛选功能
    <i class="superior-filter-tips-close" bindtap="handleCloseTips"></i>
  </view> -->

  <root-portal enable>
    <t-popup class="superior-filter-popup" visible="{{ visible }}" bind:visible-change="onVisibleChange" placement="bottom">
      <view>
        <view class="superior-filter-header">
          <view class="superior-filter-header-title">高级筛选</view>
          <view class="superior-filter-header-close" bindtap="handleClose"></view>
        </view>

        <view class="superior-filter-content">
          <view class="superior-filter-list">
            <view class="label">
              <veiw class="txt">
                编制查询
                <view class="require">(单选)</view>
                <i class="query" bindtap="handleOpenTipsDialog" data-key="establishment"></i>
              </veiw>
              <veiw class="aside">{{ asideLabel.establishment }}</veiw>
            </view>
            <t-checkbox-group class="value" value="{{ isEstablishment }}" bind:change="handleChange" data-key="isEstablishment">
              <t-checkbox wx:for="{{ establishmentOptions }}" wx:key="value" borderless t-class="checkbox" t-class-icon="icon" t-class-content="checkbox-content" value="{{ item.value }}">
                <view slot="content" class="content {{ _.includes(isEstablishment, item.value) ? 'is-checked' : '' }}"> {{ item.label }} </view>
              </t-checkbox>
            </t-checkbox-group>
          </view>

          <view class="superior-filter-list">
            <view class="label">
              <veiw class="txt">
                {{ heatLabel }}
                <view class="require">(单选)</view>
                <i class="query" bindtap="handleOpenTipsDialog" data-key="heat"></i>
              </veiw>
              <veiw class="aside">{{ asideLabel.heat }}</veiw>
            </view>
            <t-checkbox-group class="value" value="{{ heat }}" bind:change="handleChange" data-key="heat">
              <t-checkbox wx:for="{{ heatOptions }}" wx:key="value" borderless t-class="checkbox" t-class-icon="icon" t-class-content="checkbox-content" value="{{ item.value }}">
                <view slot="content" class="content {{ _.includes(heat, item.value) ? 'is-checked' : '' }}"> {{ item.label }} </view>
              </t-checkbox>
            </t-checkbox-group>
          </view>
        </view>

        <view class="superior-filter-footer">
          <button class="superior-filter-reset" bindtap="handleReset">重置</button>
          <button class="superior-filter-confirm" bindtap="handleConfirm">确定</button>
        </view>
      </view>
    </t-popup>
  </root-portal>
</view>

<login-dialog visible="{{ loginVisible }}" bind:loginSuccess="handleLoginSuccess" />

<root-portal>
  <t-dialog t-class="superior-tips-dialog" overlay-props="{{ { zIndex: 11510 } }}" z-index="{{ 11520 }}" visible="{{ dialogVisible }}" title="{{ dialogTitle }}" t-class-content="dialog-content">
    <view slot="content" class="superior-dialog-content">
      <text class="content">{{ dialogContent }}</text>
      <view wx:if="{{ dialogDescription }}" class="description">{{ dialogDescription }}</view>

      <view class="footer">
        <t-button class="btn cancel" hover-class="none" wx:if="{{ dialogCancelBtn }}" variant="base" shape="round" bindtap="handleCloseTipsDialog">{{ dialogCancelBtn }}</t-button>
        <t-button class="btn confirm" hover-class="none" wx:if="{{ dialogConfirmBtn }}" variant="base" shape="round" theme="primary" bindtap="handleTipsConfirm">{{ dialogConfirmBtn }}</t-button>
      </view>
    </view>
  </t-dialog>
</root-portal>
