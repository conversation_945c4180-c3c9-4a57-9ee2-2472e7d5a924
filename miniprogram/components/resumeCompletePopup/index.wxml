<view class="resume-complete-popup">
  <t-popup t-class="popup" visible="{{ completeVisible }}" placement="bottom" close-btn
    bind:visible-change="onVisibleChange">
    <view class="popup-main">
      <image src="{{ popupData.avatar }}" class="avatar" mode="aspectFill" />
      <view class="resume-complete">{{ popupData.completeText }}</view>
      <view class="title">{{ popupData.title }}</view>
      <view class="sub-title" wx:if="{{ popupData.subTitle }}">{{ popupData.subTitle }}</view>

      <rich-text nodes="{{ popupData.content }}"></rich-text>

      <div class="button-group">
        <t-button wx:for="{{ popupData.btnList }}" wx:item="item" wx:key="index"
          class="{{ popupData.btnList.length > 1 && index === 0 ? 'resume-button' : 'top-button' }}"
          theme="{{ popupData.btnList.length > 1 && index === 0 ? '' : 'primary' }}" bindtap="handlePopupButton"
          data-item="{{item}}">
          <view class="button-title">{{ item.text }}</view>
          <view class="button-sub-title" wx:if="{{ item.subText }}">{{ item.subText }}</view>
        </t-button>
      </div>
    </view>
  </t-popup>
</view>
