@use 'styles/variables' as *;

.resume-complete-popup {
  .popup {
    max-height: 697rpx;
    background: $color-white url(#{$assets}/person/resume-complete-popup.png) no-repeat top / 750rpx 355rpx;
    padding-bottom: 0;
  }

  .popup-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 50rpx;
    padding-bottom: calc(50rpx + env(safe-area-inset-bottom));
  }

  .avatar {
    width: 110rpx;
    height: 110rpx;
    background: $color-white;
    border-radius: 50%;
    border: 2px solid $color-white;
  }

  .resume-complete {
    font-size: 24rpx;
    color: $color-primary;
    margin: 30rpx 0;
  }

  .title {
    font-weight: bold;
    font-size: 32rpx;
    margin-bottom: 50rpx;
  }

  .sub-title {
    margin: -20rpx 0 50rpx;
  }

  .writing {
    padding: 0;
  }

  .writing-item {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    margin-bottom: 30rpx;

    &::before {
      content: '';
      width: 8rpx;
      height: 8rpx;
      margin-right: 12rpx;
      background: $color-primary;
      border-radius: 50%;
    }

    .writing-title {
      color: $color-primary;
    }
  }

  .button-group {
    display: flex;
    align-items: center;
    margin-top: 30rpx;

    .resume-button {
      margin-right: 20rpx;
      width: 220rpx;
      font-weight: normal;
      color: $font-color-label;
      background: #f7f7f7;
    }

    .top-button {
      width: 450rpx;
      font-weight: normal;
    }

    .button-title {
      font-weight: bold;
      font-size: 32rpx;
      line-height: 1.5;
    }

    .button-sub-title {
      font-size: 24rpx;
      line-height: 1;
    }
  }
}
