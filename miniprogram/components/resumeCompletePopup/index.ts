// packages/person/components/index.ts
import { h5 } from '@/settings'
import { toWebPage } from '@/utils/util'
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },

    data: {
      type: Object,
      value: {}
    }
  },

  observers: {
    data(value: object) {
      if (!Object.keys(value).length) return
      this.setData({ popupData: value })
    },
    visible(data) {
      this.setData({ completeVisible: data })
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    completeVisible: false,
    popupData: {}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handlePopupButton(event: WechatMiniprogram.CustomEvent) {
      const {
        dataset: { item }
      } = event.currentTarget

      const url = item.url
      const type = item.type

      if (type == 'mini') {
        // 跳转路由
        wx.navigateTo({
          url
        })
      }

      if (type == 'h5') {
        // 跳转到h5
        toWebPage(`${h5}${url}`)
      }
      this.handleClose()
    },
    handleClose() {
      this.setData({
        completeVisible: false
      })
    },
    onVisibleChange() {
      this.handleClose()
    }
  }
})
