Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: { type: Boolean, value: false },

    value: { type: null, value: '' },

    keys: { type: Object, value: { label: 'label', value: 'value' } },

    title: { type: String, value: '' },

    cancelBtn: { type: String, value: '取消' },

    confirmBtn: { type: String, value: '确认' },

    options: { type: Array, value: [] }
  },

  /**
   * 组件的初始数据
   */
  data: {
    pickerVisible: false
  },

  observers: {
    visible(value: boolean) {
      this.setData({ pickerVisible: value })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.triggerEvent('change', { label, value })
    }
  }
})
