@use 'styles/variables' as *;

@mixin title-style {
  color: #dc833f;
  font-weight: bold;
}

.qrcode-popup {
  .qrcode-main {
    position: relative;
    padding: 90rpx 30rpx 60rpx;
    width: 630rpx;
    text-align: center;
    line-height: 1;
    background: $color-white url(#{$assets}/qrcode/background.png) no-repeat center / cover;
    border-radius: $border-radius;
    box-sizing: border-box;

    &::before {
      content: '';
      position: absolute;
      top: -45rpx;
      left: 50%;
      width: 85rpx;
      height: 85rpx;
      background: url(#{$assets}/qrcode/bell.png) no-repeat center / contain;
      transform: translateX(-50%);
    }

    .subtitle {
      @include title-style;

      margin-bottom: 18rpx;
      font-size: 30rpx;
    }

    .title {
      @include title-style;

      margin-bottom: 40rpx;
      font-size: 40rpx;
    }

    .tips {
      padding-bottom: 30rpx;

      .item {
        margin-bottom: 20rpx;
        padding-left: 177rpx;
        text-align: left;
        font-weight: bold;
        background: url(#{$assets}/qrcode/prefix.png) no-repeat center left 134rpx / 26rpx 26rpx;
      }
    }

    .qr-code {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      width: 240rpx;
      height: 240rpx;
      background: linear-gradient(0deg, #ffe0c3, #ffca8b);
      border-radius: $border-radius;
    }

    .image {
      flex: none;
      width: 224rpx;
      height: 224rpx;
      background-color: $color-white;
      border-radius: $border-radius;
      object-fit: contain;
    }

    .text {
      margin-top: 20rpx;
      color: $font-color-basic;
      font-weight: bold;
    }

    .close-button {
      position: absolute;
      left: 50%;
      bottom: calc(-30rpx - 32px);
      color: $color-white;
      font-size: 32px;
      transform: translateX(-50%);
    }
  }
}
