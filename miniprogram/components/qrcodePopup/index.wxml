<t-popup class="qrcode-popup" visible="{{ popupVisible }}" placement="center">
  <view class="qrcode-main">
    <view class="subtitle">{{ subtitle }}</view>

    <view class="title">{{ title }}</view>

    <view class="tips" wx:if="{{ text.length > 0 }}">
      <view class="item" wx:for="{{ text }}" wx:key="index">{{ item }}</view>
    </view>

    <block wx:if="{{ qrcode }}">
      <view class="qr-code">
        <image class="image" src="{{ qrcode }}" show-menu-by-longpress="{{ true }}" />
      </view>

      <view class="text">{{ qrcodeText }}</view>
    </block>

    <t-icon class="close-button" name="close-circle" bindtap="handleClose" />
  </view>
</t-popup>
