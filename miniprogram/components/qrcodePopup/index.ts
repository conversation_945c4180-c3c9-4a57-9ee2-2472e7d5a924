import { getFollowQrCode } from '@/api/person'
import { showLoading } from '@/utils/util'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: { type: Boolean, value: false },

    /**
     * @type 'click' | 'auto'
     * @default 'click'
     */
    trigger: { type: String, value: 'click' }
  },

  /**
   * 组件的初始数据
   */
  data: {
    popupVisible: false,

    subtitle: '',

    title: '',

    text: [],

    qrcode: '',

    qrcodeText: ''
  },

  observers: {
    visible(value: boolean) {
      value && this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      showLoading()
      const { trigger } = this.properties
      const { subtitle, title, text, qrcode, qrcodeText } = await getFollowQrCode({
        isAutoClick: trigger === 'auto' ? 1 : 0
      })

      this.setData({ popupVisible: true, subtitle, title, text, qrcode, qrcodeText }, () => wx.hideLoading())
    },

    handleClose() {
      this.setData({ popupVisible: false })
    }
  }
})
