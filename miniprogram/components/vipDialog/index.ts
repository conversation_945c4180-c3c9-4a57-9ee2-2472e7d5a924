import { defaultDuration } from '@/settings'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: { type: Boolean, value: false }
  },

  /**
   * 组件的初始数据
   */
  data: {
    dialogVisible: false,
    content:
      '该权益目前仅支持电脑或手机端网页平台使用，请复制下方链接在浏览器打开【高校人才网手机端网页】平台购买使用： https://m.gaoxiaojob.com/vip.html',
    confirmButton: { content: '立即复制', variant: 'base' }
  },

  observers: {
    visible(value: boolean) {
      this.setData({ dialogVisible: value })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleConfirm() {
      this.setData({ dialogVisible: false })
      this.triggerEvent('close', { value: false })

      wx.setClipboardData({
        data: 'https://m.gaoxiaojob.com/vip.html',
        success: () => {
          wx.hideToast({
            success: () => {
              wx.showToast({ title: '复制链接成功，请至浏览器打开网页', icon: 'none', duration: defaultDuration })
            }
          })
        }
      })
    }
  }
})
