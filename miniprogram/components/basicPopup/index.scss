@use 'styles/variables' as *;

$namespace: 'basic-popup';

.#{$namespace}-container {
  .#{$namespace}-header {
    line-height: 100rpx;
    border-bottom: 2rpx solid $border-color;
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
  }

  .#{$namespace}-content {
    margin-top: 40rpx;
    max-height: 55vh;
    box-sizing: border-box;
    overflow: hidden;
    --td-grid-item-padding: 0;
    padding: 0rpx 30rpx;

    .t-grid-item__content {
      padding-bottom: 0;
    }

    .custom-checkbox {
      background-color: #f7f7f7;
      box-sizing: border-box;
      width: 100%;
      height: 74rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      font-size: 24rpx;
      padding: 0 6rpx;
      text-align: center;
      border: 2rpx solid #f7f7f7;

      .label {
        display: -webkit-box;
        text-overflow: ellipsis;
        overflow: hidden;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      &.is-checked {
        color: $color-primary;
        border-color: $color-primary;
        background-color: $color-primary-background;
      }
    }
  }

  .#{$namespace}-footer {
    margin-top: 40rpx;
    display: flex;
    justify-content: space-between;
    padding: 30rpx;
    height: 140rpx;
    box-sizing: border-box;
    border-top: 2rpx solid $border-color;

    .t-button {
      margin: 0;
    }

    .reset-button {
      width: 220rpx;
      color: $font-color-label;
      background-color: #f7f7f7;

      &.t-button--hover {
        &::after {
          background-color: inherit;
        }
      }
    }

    .confirm-button {
      width: 450rpx;
    }
  }
}
