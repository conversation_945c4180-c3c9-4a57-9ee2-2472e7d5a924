<wxs src="./index.wxs" module="utils" />

<t-popup visible="{{ popupVisible }}" bind:visible-change="onVisibleChange" placement="bottom" t-class="baisc-popup">
  <view class="basic-popup-container" slot="content">
    <view class="basic-popup-header">{{ title }}</view>

    <slot name="tips" />

    <scroll-view class="basic-popup-content" scroll-y enhanced show-scrollbar="{{ false }}">
      <t-grid column="{{ column }}" gutter="{{ gutter }}" border="{{ false }}">
        <block wx:for="{{ options }}" wx:key="index">
          <t-grid-item>
            <view class="custom-checkbox {{ utils.includes(item[optionsValue], checkedList) ? 'is-checked' : '' }}" data-label="{{ item[optionsLabel] }}" data-value="{{ item[optionsValue] }}" bindtap="handleClick">
              <view class="label">
                {{ item[optionsLabel] }}
              </view>
            </view>
          </t-grid-item>
        </block>
      </t-grid>
    </scroll-view>

    <view class="basic-popup-footer" wx:if="{{ multiple }}">
      <t-button class="reset-button" theme="light" bindtap="handleReset">重置</t-button>
      <t-button class="confirm-button" theme="primary" bindtap="handleConfirm">确定</t-button>
    </view>
  </view>
</t-popup>
