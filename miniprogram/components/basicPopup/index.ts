import { defaultDuration } from '@/settings'
import { showLoading } from '@/utils/util'

Component({
  options: {
    styleIsolation: 'shared',
    multipleSlots: true
  },

  /**
   * 组件的属性列表
   */
  properties: {
    /** 选项值 是否可见 */
    visible: { type: Boolean, value: false },

    /** 选项值 选中值 单选（字符串）多选（数组） */
    model: { type: null, value: '' },

    /** 选项值 顶部标题 */
    title: { type: String, value: '请选择' },

    /** 选项值 限制选择的个数 */
    limit: { type: Number, value: 1 },

    /** 选项值 选择项栏数 */
    column: { type: Number, value: 1 },

    /** 选项值 选择项间距 */
    gutter: { type: Number, value: 20 },

    /** 选项值 选项数据 */
    options: { type: Array, value: [] },

    /** 选项值 选项展示文本字段 */
    optionsLabel: { type: String, value: 'label' },

    /** 选项值 选项选中值字段 */
    optionsValue: { type: String, value: 'value' }
  },

  /**
   * 组件的初始数据
   */
  data: {
    popupVisible: false,

    multiple: false,

    checkedList: [],

    sideBarIndex: 0,
    offsetTop: 0,
    offsetTopList: []
  },

  observers: {
    visible(value: boolean) {
      this.watchVisible(value)
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    watchVisible(value: boolean) {
      if (value) {
        showLoading()
        this.setData({ popupVisible: true })

        this.updateMultiple()
        this.updateCheckedOptions()
      }
    },

    updateMultiple() {
      const { limit } = this.data
      const valid = typeof limit === 'number'
      if (valid) {
        this.setData({ multiple: limit > 1 })
      }
    },

    updateCheckedOptions() {
      const { model, options, optionsLabel, optionsValue } = this.data
      const checkedList: never[] = []
      const values: any = []
      if (Array.isArray(model)) {
        values.push(...model)
      }
      if (typeof model === 'string' && model !== '') {
        values.push(model)
      }
      const update = (target: any, data: any) => {
        const label = data[optionsLabel]
        const value = data[optionsValue]
        const checked = values.includes(value)
        const exist = target.map((checked: Miniprogram.OptionType) => checked.value).includes(value)
        if (checked && exist === false) {
          target.push(<never>{ label, value })
        }
      }
      options.map((item: any) => {
        update(checkedList, item)
      })

      this.setData({ checkedList }, () => wx.hideLoading())
    },

    handlePopupClose() {
      this.triggerEvent('close', { value: false })
    },

    handleClick(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.currentTarget.dataset
      const { checkedList, limit, multiple } = this.data

      const index = checkedList
        .map((item: Miniprogram.OptionType) => item.value)
        .findIndex((item: string) => item === value)
      const { length } = checkedList

      if (value === '') {
        this.handleReset()

        if (multiple === false) {
          this.handleConfirm()
        }
        return
      }

      if (length < limit || index > -1) {
        if (index > -1) {
          checkedList.splice(index, 1)
        } else {
          checkedList.unshift(<never>{ label, value })
        }

        this.setData({ checkedList }, () => {
          if (multiple === false && checkedList.length === limit) {
            this.handleConfirm()
          }
        })
      } else {
        if (multiple) {
          wx.showToast({ title: `最多可选${limit}项`, icon: 'none', duration: defaultDuration })
        } else {
          this.setData({ checkedList: [<never>{ label, value }] }, () => {
            this.handleConfirm()
          })
        }
      }
    },

    handleReset() {
      this.setData({ checkedList: [] })
    },

    handleConfirm() {
      const { multiple, checkedList } = this.data
      const label = checkedList.map((item: Miniprogram.OptionType) => item.label)
      const value = checkedList.map((item: Miniprogram.OptionType) => item.value)

      const result = {
        label: multiple ? label : label.join(),
        value: multiple ? value : value.join()
      }

      this.setData({ popupVisible: false }, () => {
        this.triggerEvent('change', result)
      })
    },
    onVisibleChange(e: any) {
      this.setData({
        popupVisible: e.detail.visible
      })
    }
  }
})
