import { toWebPage } from '@/utils/util'
import { h5 } from '@/settings'
import { getServiceTips } from '@/api/home'

Component({
  options: {
    virtualHost: true
  },

  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    url: {
      type: String,
      value: ''
    }
  },

  observers: {
    url: function (value) {
      this.setData({
        openLink: `${h5}${value}`
      })
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    openLink: '',

    title: '',
    content: '',
    remark: '',
    cancelButtonText: '',
    confirmButtonText: ''
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const { title, content, remark, cancelButtonText, confirmButtonText } = await getServiceTips()

      this.setData({ title, content, remark, cancelButtonText, confirmButtonText })
    },

    handleOpen() {
      const { openLink } = this.data
      this.setData({ visible: false }, () => {
        toWebPage(openLink)
      })
    },

    handleCancleDialog() {
      this.setData({ visible: false })
    }
  }
})
