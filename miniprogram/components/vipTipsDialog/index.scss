@use 'sass:meta';
@use 'styles/variables' as *;

.open-vip-tips-dialog {
  @include tdesign-variables;

  --td-button-light-color: #{$font-color-label};
  --td-button-light-bg-color: #f7f7f7;

  &.t-button--hover::after {
    --td-button-light-active-bg-color: #f7f7f7;
    --td-button-light-active-border-color: #f7f7f7;
  }

  .dialog {
    margin-top: 16px;
    color: $font-color-basic;
    text-align: center;
    font-size: 32rpx;
    line-height: 48rpx;
    padding: 0 4rpx;

    .tips {
      display: flex;
      font-size: 24rpx;
      margin-top: 14rpx;
    }
  }
}
