<root-portal enable="{{ true }}">
  <t-dialog t-class="open-vip-tips-dialog" visible="{{ visible }}" title="{{ title }}" cancel-btn="{{ { content: cancelButtonText, variant: 'base', shape: 'round' } }}" confirm-btn="{{ { content: confirmButtonText, variant: 'base', shape: 'round' } }}" bindconfirm="handleOpen" bindcancel="handleCancleDialog">
    <view slot="content" class="dialog">
      <view class="content">{{ content }}</view>
      <view class="tips">{{ remark }}</view>
    </view>
  </t-dialog>
</root-portal>
