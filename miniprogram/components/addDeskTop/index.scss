@use 'styles/variables' as *;

$namespace: 'desk-popup';

.#{$namespace}-container {
  position: fixed;
  z-index: 5002;
  right: 10rpx;
  top: 180rpx;
  width: 408rpx;
  padding: 40rpx 30rpx 30rpx;
  font-size: 24rpx;
  color: #333333;
  line-height: 36rpx;
  background: #ffffff;
  filter: drop-shadow(0rpx 4rpx 10rpx rgba(51, 51, 51, 0.14));
  border-radius: 16rpx;
  &:before {
    position: absolute;
    top: -20rpx;
    right: 100rpx;
    // transform: translateX(-50%);
    width: 0;
    height: 0;
    content: '';
    border-left: 20rpx solid transparent;
    border-right: 20rpx solid transparent;
    border-bottom: 40rpx solid white; /* 设置三角形的颜色 */
  }
  .desk-list {
    margin-top: 30rpx;
    &:first-child {
      margin-top: 0;
      display: flex;
      align-items: center;
    }
  }
  .more {
    width: 71rpx;
    height: 34rpx;
    margin-left: 6rpx;
  }
  .desk {
    display: block;
    width: 242rpx;
    height: 157rpx;
    margin-top: 4rpx;
  }
  .close {
    position: absolute;
    right: 30rpx;
    padding: 10rpx;
    .close-img {
      width: 20rpx;
      height: 20rpx;
    }
  }
}
