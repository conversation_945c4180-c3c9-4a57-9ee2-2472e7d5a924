import { assetsURL } from '@/settings'

Component({
  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {
    /** 选项值 是否可见 */
    visible: { type: Boolean, value: false }
  },

  /**
   * 组件的初始数据
   */
  data: {
    timer: <any>null,
    popupVisible: false,
    moreImage: `${assetsURL}/icon/addDeskTop/more.png`,
    deskImage: `${assetsURL}/icon/addDeskTop/desk.png`,
    closeImage: `${assetsURL}/icon/addDeskTop/close.png`
  },

  observers: {
    // visible(value: boolean) {
    //   this.watchVisible(value)
    // }
  },
  attached() {
    wx.checkIsAddedToMyMiniProgram({
      success: (res: any) => {
        // console.log(res);
        // res.added 表示是否已添加
        if (!res.added) {
          // console.log('用户未添加到我的小程序');
          this.setData({ popupVisible: true })
          // 5s关闭
          let time = null
          time = setTimeout(() => {
            this.setData({ popupVisible: false })
          }, 5 * 1000)
          this.setData({ timer: time })
          // clearTimeout(time)
        }
      },
      fail(err) {
        // console.log('检查失败', err);
      }
    })
  },
  detached() {
    // console.log('组件已被移除');
    clearTimeout(this.data.timer)
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.setData({ popupVisible: false })
    },
    handlePopupClose() {
      this.triggerEvent('close', { value: false })
    }
  }
})
