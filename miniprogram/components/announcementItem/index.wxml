<!-- offline -->
<view class="item c-class {{ detail.status == 2 ? 'offline' : '' }}" data-id="{{ detail.id }}" data-msg="{{ detail.toastMessage }}" bindtap="handleClick">
  <view class="title c-class-title">
    <view wx:if="{{ detail.establishmentType && detail.establishmentType != 0 }}" class="is-establishment">{{ detail.establishmentType == 1 ? '全部' : '部分' }}有编</view>
    {{ detail.title }}
  </view>
  <view class="tag-content">
    <view class="tag" wx:if="{{ detail.education }}">{{ detail.education }}</view>
    <view class="tag" wx:if="{{ detail.jobAmount }}">{{ detail.jobAmount }}个职位</view>
    <view class="tag" wx:if="{{ detail.recruitAmount }}">招{{ detail.recruitAmount }}人</view>
    <view class="tag" wx:if="{{ showArea && detail.area }}">{{ detail.area }}</view>
  </view>
  <view class="bright">
    {{ detail.highlightsDescribe }}
  </view>
  <view class="bottom">
    <view class="unit">{{ detail.fullName }}</view>
    <view class="time">{{ detail.time }}</view>
  </view>
</view>
