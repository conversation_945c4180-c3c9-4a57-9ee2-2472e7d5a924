import { defaultDuration } from '@/settings'

Component({
  options: {
    virtualHost: true
  },
  externalClasses: ['c-class', 'c-class-title'],
  /**
   * 组件的属性列表
   */
  properties: {
    detail: {
      type: Object,
      value: {}
    },

    showArea: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    handleClick(event: WechatMiniprogram.CustomEvent) {
      const { id, msg = '' } = event.currentTarget.dataset

      if (msg) {
        wx.showToast({ title: msg, icon: 'none', duration: defaultDuration })
        return
      }

      wx.navigateTo({ url: `/packages/announcement/detail/index?id=${id}` })
    }
  }
})
