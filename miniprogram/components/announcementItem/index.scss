@use 'styles/variables' as *;

.item {
  background-color: #fff;
  padding: 20rpx 30rpx 24rpx;

  .title {
    line-height: 48rpx;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 16rpx;

    .is-establishment {
      float: left;
      margin: 7rpx 10rpx 0 0;
      color: #4fbc67;
      font-size: 24rpx;
      font-style: oblique;
      padding: 0 15rpx 0 11rpx;
      height: 37rpx;
      line-height: 37rpx;
      background: linear-gradient(-90deg, #f0ffdc, #fafff4);
      border-radius: 16rpx 0rpx 16rpx 0rpx;
    }
  }

  .tag-content {
    display: flex;
    flex-wrap: nowrap;
    font-size: 24rpx;

    .tag {
      margin-bottom: 14rpx;
      line-height: 44rpx;
      padding: 0 12rpx;
      border-radius: 4rpx;
      background-color: $tag-info-background;
      color: $font-color-basic;

      & + .tag {
        margin-left: 12rpx;
      }
    }
  }

  .bright {
    font-weight: 500;
    font-size: 24rpx;
    color: #9F684B;
    line-height: 48rpx;
    padding-left: 40rpx;
    background: url(#{$assets}/icon/announcement-item-zan.png) no-repeat left/32rpx 28rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .bottom {
    display: flex;
    font-size: 24rpx;

    .unit {
      flex-grow: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .time {
      flex-shrink: 0;
      padding-left: 34rpx;
      margin-left: 100rpx;
      color: $font-color-tips;
      background: url(#{$assets}/icon/time.png) no-repeat left/24rpx;
    }
  }

  &.offline {
    background:
      url(#{$assets}/icon/offline.png) no-repeat right 32rpx top 30rpx /110rpx 96rpx,
      #fff;
    background-position: right 30rpx bottom 32rpx;

    .title,
    .tag-content,
    .bright,
    .bottom {
      opacity: 0.6;
    }

    .time {
      display: none;
    }
  }
}
