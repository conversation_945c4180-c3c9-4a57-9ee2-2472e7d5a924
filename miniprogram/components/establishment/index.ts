import { checkLogin } from '@/utils/store'
import { getVipInfo } from '@/api/person'

Component({
  options: {
    virtualHost: true
  },

  lifetimes: {
    attached() {
      this.init()
      this.setData({ isContinue: false })
    }
  },

  pageLifetimes: {
    show() {
      this.init()
      this.setData({ isContinue: true })
    }
  },

  /**
   * 组件的属性列表
   */
  properties: {
    value: {
      type: String,
      optionalTypes: [String, Number, Boolean],
      value: ''
    }
  },
  observers: {
    value: function (value) {
      const realValue = /1/.test(value) || value === true
      this.setData({ realValue })
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isContinue: true,

    isLogin: false,
    isVip: false,
    realValue: false,
    tipsVisible: false,
    loginVisible: false,
    buyVipUrl: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async init() {
      const { isContinue } = this.data
      if (!isContinue) return
      this.setData({ isLogin: checkLogin() })
      await this.getFetchVipInfo()
    },

    async getFetchVipInfo() {
      const { isLogin } = this.data
      if (!isLogin) return
      const {
        isVip,
        buyUrl: { vip: buyVipUrl }
      } = await getVipInfo()

      this.setData({ isVip: isVip === '1', buyVipUrl })
    },

    handleChange(e: any) {
      const {
        detail: { checked }
      } = e
      const { isVip, isLogin } = this.data
      const realValue = !isVip ? false : checked
      this.setData({ realValue })
      if (!isLogin) {
        this.setData({ loginVisible: true })
        return
      }
      if (!isVip) {
        this.setData({ tipsVisible: true })
        return
      }
      this.triggerEvent('change', { value: realValue ? 1 : '' })
    },

    handleOpen() {
      this.setData({ tipsVisible: false })
    },

    handleCancleDialog() {
      this.setData({ tipsVisible: false })
    },

    closeTrigger() {
      this.triggerEvent('change', { value: false })
    },

    loginSuccess() {
      this.setData({
        isLogin: true
      })
      this.getFetchVipInfo()
      this.triggerEvent('loginSuccess')
    }
  }
})
