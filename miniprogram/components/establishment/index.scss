@use 'sass:meta';
@use 'styles/variables' as *;

.establishment {
  .checkbox {
    padding: 0;
    white-space: nowrap;
  }

  .checkbox-content {
    display: flex;
    align-items: center;
    margin-top: 0px !important;
    position: relative;
    overflow: visible;

    .vip-icon {
      display: block;
      position: absolute;
      right: -23rpx;
      top: -10rpx;
      width: 41rpx;
      height: 20rpx;
      background: url(#{$assets}/icon/vip-radius.png) no-repeat center/contain;
    }

    .unchecked {
      color: #b2b2b2;
    }

    .checked {
      color: $color-primary;
    }

    .label {
      display: flex;
      padding-left: 7rpx;
      font-style: oblique;
      font-size: 28rpx !important;
      color: $font-color;

      .primary {
        color: $color-primary;
      }
    }
  }
}
