<view class="establishment c-class">
  <t-checkbox bind:change="handleChange" data-key="value" class="checkbox" icon="none" t-class-border="checkbox-border" t-class-content="checkbox-content" checked="{{ realValue }}">
    <view class="checkbox-content" slot="content">
      <i class="vip-icon"></i>
      <t-icon name="{{ realValue ? 'check-circle-filled' : 'circle' }}" size="28rpx" t-class="checkbox-icon {{ realValue ? 'checked' : 'unchecked' }}" />
      <view class="label">
        查
        <view class="primary">编制</view>
      </view>
    </view>
  </t-checkbox>

  <vip-tips-Dialog visible="{{ tipsVisible }}" is-login="{{ isLogin }}" url="{{ buyVipUrl }}" />
  <login-dialog bind:loginSuccess="loginSuccess" visible="{{ loginVisible }}" />
</view>
