@use 'styles/variables' as *;

.guide-box {
  // position: fixed;
  bottom: 0;
  transition: transform .3s ease;
  transform: translateY(100%);
  box-sizing: border-box;
  width: 100%;
  padding: 40rpx 30rpx 60rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 4rpx 21rpx 6rpx rgba(51,51,51,0.3);
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  z-index: 9;
  &.active {
    transform: translateY(0);
  }
  &.not-active {
    transform: translateY(100%);
  }
  .title {
    font-weight: bold;
    font-size: 36rpx;
    color: #333333;
    position: relative;
    margin-bottom: 30rpx;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 208rpx;
      height: 10rpx;
      background: linear-gradient(90deg, #FFA000, #FFFFFF);
      border-radius: 5rpx;
      opacity: 0.6;
    }
  }
  .search-header-content {
    background-color: #f7f7f7;
    height: 64rpx;
    border-radius: 64rpx;
    display: flex;
    align-items: center;

    .content-prefix {
      padding-right: 18rpx;
      padding-left: 30rpx;
      display: flex;
      align-items: center;
      position: relative;

      .search-header-label {
        max-width: 144rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .icon {
        margin-left: 10rpx;
      }

      &::after {
        content: '';
        position: absolute;
        right: 0;
        display: inline;
        width: 2rpx;
        height: 24rpx;
        background: #333;
        opacity: 0.4;
      }
    }

    .t-input {
      --td-input-bg-color: transparent;
      --td-font-size-m: 28rpx;

      padding: 0 20rpx;
      height: 100%;
      flex-grow: 1;

      .clearable-icon {
        color: #dedede;
        font-size: 32rpx;
      }

      .search-btn {
        height: 40rpx;
        padding: 0 20rpx;
        line-height: 40rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #FFFFFF;
        text-align: center;
        background: #FFA000;
        border-radius: 20rpx;
        border: 1rpx solid #FFA000;
      }
    }
  }
  .filter-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0 0;
    background-color: #fff;

    .filter {
      display: flex;
      flex: 1;
      box-sizing: border-box;
      top: 0;
    }

    .item {
      position: relative;
      box-sizing: border-box;
      padding: 0 34rpx 0 20rpx;
      line-height: 48rpx;
      color: #333333;
      opacity: 0.8;
      font-size: 24rpx;
      border-radius: 8rpx;
      max-width: 200rpx;
      background: #F7F7F7;
      border: 2rpx solid #f4f6fb;
      @include utils-ellipsis;

      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        right: 14rpx;
        bottom: 14rpx;
        display: block;
        border: 4rpx solid #c7c7c7;
        border-left-color: transparent;
        border-top-color: transparent;
      }

      & ~ .item {
        margin-left: 20rpx;
      }

      &.has-condition {
        color: $color-primary;
        background-color: $color-primary-background;
        border-color: $color-primary;

        &::after {
          border-right-color: $color-primary;
          border-bottom-color: $color-primary;
        }
      }
    }

    .vip {
      &::before {
        content: "";
        width: 50rpx;
        height: 21rpx;
        top: 0rpx;
        right: 0rpx;
        position: absolute;
        background: url(//img.gaoxiaojob.com/uploads/mini/icon/vip-radius-reverse.png) no-repeat right/100% 100%;
        background-position-x: center;
        background-position-y: center;
        background-size: 100% 100%;
        background-repeat-x: no-repeat;
        background-repeat-y: no-repeat;
        background-attachment: initial;
        background-origin: initial;
        background-clip: initial;
        background-color: initial;
      }
    }
  }
}
