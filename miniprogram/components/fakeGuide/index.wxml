<view class="{{['guide-box', visible ? 'active' : 'not-active']}}" style="{{ dynamicStyle }}">
  <view class="title">
    {{title}}
  </view>
  <view class="search-header-content" slot="left" bind:tap="toJump">
    <view class="content-prefix">
      <view class="search-header-label">
          {{ cityText }}
      </view>
      <t-icon class="icon" name="caret-down-small" size="24rpx" />
    </view>
    <t-input t-class-clearable="clearable-icon" readonly placeholder="{{ placeholder }}"  borderless>
      <view slot="suffix" class="search-btn">搜索</view>
    </t-input>
  </view>
  <view class="filter-box" bind:tap="toJump">
    <view class="filter">
      <view class="item {{ noticeId.length && hasNotice ? 'has-condition' : '' }}" data-type="area"> {{ noticeText }}</view>

      <view class="item {{ educationId.length && hasEducation ? 'has-condition' : '' }}" data-type="education">{{ educationText }}</view>

      <view class="item {{ majorId.length && hasMajor ? 'has-condition' : '' }}" data-type="major"> {{ majorText }}</view>

      <view class="item vip" data-type="vip">更多筛选</view>
    </view>
  </view>
</view>