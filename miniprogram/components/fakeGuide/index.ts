Component({
  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {
    /** 选项值 是否可见 */
    visible: { type: Boolean, value: false },
    /** 选项值 顶部标题 */
    title: { type: String, value: '搜索更多公告' },
    placeholder: { type: String, value: '搜公告名称' },
    moreSearchParams: { 
      type: Object, 
      value: {
      },
      observer(newVal) {
        // console.log(newVal);
        this.setData({
          noticeText: newVal.noticeText,
          cityText: newVal.cityText,
          educationText: newVal.educationText,
          majorText: newVal.majorText,
          noticeId: newVal.noticeId,
          educationId: newVal.educationId,
          majorId: newVal.majorId,
          cityId: newVal.cityId,
          hasNotice: newVal.hasNotice,
          hasEducation: newVal.hasEducation,
          hasMajor: newVal.hasMajor,
        })
      }
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    timer: null,
    popupVisible: true,
    cityId: [],
    noticeId: [],
    educationId: [],
    majorId: [],
    cityText: '地区',
    noticeText: '公告类型',
    educationText: '学历',
    majorText: '学科',
    hasNotice: true,
    hasEducation: true,
    hasMajor: true,
    dynamicStyle: ''
  },

  observers: {
    visible(value: boolean) {
    }
  },
  attached() {
    setTimeout(() => {
      this.setData({
        dynamicStyle: 'position: fixed;'  // 修改后的样式
      });
    },300)
  },
  detached() {
    // console.log('组件已被移除');
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.setData({ popupVisible: false })
    },
    handlePopupClose() {
      this.triggerEvent('close', { value: false })
    },
    toJump() {
       this.triggerEvent('jump', {})
    }
  }
})
