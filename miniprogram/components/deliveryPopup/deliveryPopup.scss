@use 'styles/variables' as *;

.delivery-popup {
  .popup {
    max-height: 82vh;
    display: flex;
  }

  .popup-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
  }

  .header {
    text-align: center;
    padding: 35rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    border-bottom: 2rpx solid $border-color;
  }

  .content {
    padding: 40rpx 30rpx;
    flex-grow: 1;
    overflow-y: scroll;

    .scroll-content {
      height: 100%;
      overflow-y: scroll;
    }
    background-color: $color-white;

    .resume-complete {
      display: flex;
      justify-content: space-between;
      margin-bottom: 25rpx;
    }

    .head {
      font-weight: bold;
      font-size: 30rpx;
      margin-right: 15rpx;
    }

    .is-required {
      &::before {
        content: '*';
        color: $color-point;
        font-weight: normal;
      }
    }

    .complete {
      display: flex;
      align-items: center;

      .text {
        display: flex;
        font-size: 24rpx;
      }

      .count {
        color: $color-primary;
      }
    }

    .path {
      color: $color-primary;
      font-size: 28rpx;
      padding-right: 23rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/into.png') no-repeat center right / 12rpx 22rpx;
    }

    .resume-tips {
      font-size: 26rpx;
      color: $font-color-label;
    }

    .attachment {
      display: flex;
      align-items: center;
      margin-top: 60rpx;
    }

    .head-box {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .tips {
      color: $font-color-label;
      font-size: 24rpx;
    }

    .limit {
      width: 26rpx;
      height: 26rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/question.png') no-repeat center/contain;
    }

    .upload-small {
      color: $color-primary;
      padding: 6rpx 36rpx 6rpx 11rpx;
      font-size: 24rpx;
      border-radius: 12rpx;
      border: 2rpx solid $color-primary;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/add-primary.png') no-repeat center right 12rpx/18rpx;
    }

    .attachment-upload,
    .material-file-upload {
      text-align: center;
      padding: 20rpx 0;
      margin-top: 40rpx;
      border-radius: 12rpx;
      background: #f6f7f9 url('//img.gaoxiaojob.com/uploads/mini/icon/add.png') no-repeat left 226rpx center / 26rpx;
    }

    .attachment-check {
      margin-top: 30rpx;

      .box-title {
        font-size: 24rpx;
      }

      .attachment-box {
        margin-top: 20rpx;
        overflow: hidden;
        height: 145rpx;

        .box {
          display: flex;
          flex-wrap: nowrap;
          overflow-x: auto;
          overflow-y: hidden;
          height: 159rpx;
        }

        .label-box {
          position: relative;
          flex: none;
          padding: 16rpx 16rpx 0 0;

          .t-radio {
            position: unset;
            display: unset;
          }

          .t-radio__icon {
            position: absolute;
            top: 7rpx;
            right: 15rpx;
            margin: 0;
            width: 30rpx;
            height: 30rpx;
          }

          .t-radio__icon-circle {
            width: 30px;
            height: 30px;
            background-color: $color-white;
          }

          .t-radio__icon {
            font-size: 30rpx;
          }
        }
      }
    }

    .label {
      word-break: break-all;
      font-size: 26rpx;
      padding: 12rpx;
      border-radius: 12rpx;
      background-color: #f6f7f9;
      margin-right: 10rpx;
      width: 250rpx;
      height: 79rpx;
      @include utils-ellipsis-lines(2, 1.5);
    }

    .material-file {
      display: flex;
      align-items: center;
      margin-top: 50rpx;
      margin-bottom: 30rpx;
    }

    .material-tips {
      font-size: 24rpx;
      color: $font-color-label;

      .color-primary {
        color: $color-primary;
      }
    }

    .material-file-box {
      height: 140rpx;
      margin-top: 15rpx;
      overflow-x: auto;
      overflow-y: hidden;
      margin-bottom: -30rpx;

      .box {
        display: flex;
        height: 160rpx;
        overflow-x: auto;
        overflow-y: hidden;
      }

      .file-box {
        position: relative;

        .label {
          margin-top: 12rpx;
          margin-right: 20rpx;
        }

        .remove {
          position: absolute;
          right: 5rpx;
          top: 0;
          width: 30rpx;
          height: 30rpx;
          background: url('//img.gaoxiaojob.com/uploads/mini/icon/delete-round.png') no-repeat center/contain;
        }
      }
    }

    .apply-add-top {
      --td-checkbox-vertical-padding: 0;
      --td-checkbox-icon-size: 40rpx;

      .value {
        display: flex;
        align-items: center;
      }

      .add-top-label {
        font-size: 30rpx;
        font-weight: bold;
        padding: 50rpx 0 22rpx;
      }

      .checkbox {
        .t-checkbox__icon--left {
          margin-right: 10rpx;
        }
      }

      .checkbox-label {
        font-size: 24rpx;
        color: $font-color;

        .color-primary {
          color: $color-primary;
        }
      }

      .renew {
        font-size: 24rpx;
        height: 34rpx;
        color: $color-primary;
        padding-right: 14rpx;
        background: url('#{$assets}/icon/into.png') no-repeat right/10rpx 18rpx;
        border-bottom: 2rpx solid $color-primary;
      }
    }
  }

  .delivery-tips {
    background: #fff4e1;
    color: $color-primary;
    font-size: 24rpx;
    padding: 10rpx 30rpx;

    &::before {
      content: '';
      width: 24rpx;
      height: 24rpx;
      display: inline-block;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/attention.png') no-repeat center / 24rpx;
    }
  }

  .disabled {
    opacity: 0.4;
  }

  .delivery-buttom {
    position: none;
    display: flex;
    align-items: center;
    bottom: 0;
    border-top: 1px solid $border-color;
    padding: 30rpx 40rpx;
  }

  .toast {
    position: fixed;
    top: 50%;
    width: 376rpx;
    left: 23%;
    color: $color-white;
    padding: 20rpx;
    background-color: $font-color-label;
    z-index: 80000;
    word-break: break-all;
    border-radius: $border-radius;
  }
}
