import { showToast } from '@/utils/util'
import { jobApply } from '@/api/apply'
import { getAttachmentList } from '@/api/common'
import { handleUploadMessageFile } from '@/utils/upload'
import { getVipInfo } from '@/api/person'
import { h5 } from '@/settings'
import { toWebPage } from '@/utils/util'

Component({
  /**
   * 页面的初始数据
   */

  options: {
    styleIsolation: 'shared'
  },

  data: {
    deliveryVisible: false,
    title: '简历投递',
    deliveryInfo: <any>{},
    stuffFileList: <any>[],
    attachementList: <any>[],
    isRequired: false,
    hasStuffFile: false,
    attachmentDisabled: false,
    stuffDisabled: false,
    hasAttachment: false,
    isComplete: false,
    token: '',
    stuffFileId: '',
    attachmentUrl: '/resume/upload',
    stuffUrl: '/resume/upload-file',
    attachementType: [],
    stuffType: [],
    attachementCount: 0,
    stuffCount: 0,
    content: '',
    applyTips: '',
    showTips: false,
    deliverySuccessVisible: false,

    deliveryTopIsCheckBox: '',
    deliveryTop: {
      show: false,
      disabled: false,
      openUrl: '',
      openBtn: false,
      check: false,
      tips: ''
    }
  },

  methods: {
    showStuffFileList(status: boolean) {
      this.setData({ hasStuffFile: status })
    },

    async getAttchaementData() {
      const list = await getAttachmentList()

      this.setData({
        attachementList: list,
        hasAttachment: list.length > 0,
        attachmentDisabled: list.length >= this.data.deliveryInfo.attachmentNumberText
      })
    },

    pushStuffFileList(file: any) {
      const { stuffFileList, deliveryInfo } = this.data
      const fileList = file.map((item: any) => item.data)

      stuffFileList.unshift(...fileList)

      this.setData({ stuffFileList, stuffDisabled: stuffFileList.length >= deliveryInfo.applyNumberText })

      this.showStuffFileList(true)
    },

    handleRemove(event: WechatMiniprogram.CustomEvent) {
      const {
        dataset: { id }
      } = event.currentTarget

      const { stuffFileList, deliveryInfo } = this.data
      const filterList = stuffFileList.filter((item: any) => id !== item.id)

      this.setData({
        stuffFileList: filterList,
        stuffDisabled: filterList.length >= deliveryInfo.applyNumberText
      })

      if (!filterList.length) {
        this.showStuffFileList(false)
        return
      }
    },

    handleUploadAttachment() {
      const { deliveryInfo, attachementList, attachmentUrl, attachementType } = this.data
      const count = deliveryInfo.attachmentNumberText - attachementList.length
      this.setData({ attachementCount: count })

      if (!this.data.attachmentDisabled) {
        handleUploadMessageFile({ url: attachmentUrl, type: 'file', count: 1, extension: attachementType }, () =>
          this.getAttchaementData()
        )
      } else {
        this.setData({ attachmentDisabled: true })
        showToast(`最多只能有${deliveryInfo.attachmentNumberText}份附件简历，请删除一份后再上传。`)
      }
    },

    handleUploadStuff() {
      const { deliveryInfo, stuffFileList, stuffUrl, stuffType } = this.data
      const count = deliveryInfo.applyNumberText - stuffFileList.length
      this.setData({ stuffCount: count })

      if (stuffFileList.length >= deliveryInfo.applyNumberText) {
        this.setData({ stuffDisabled: true })
        showToast(`最多只能有${deliveryInfo.applyNumberText}份应聘材料，请删除一份后再上传。`)
        return
      }

      const callback = (data: any) => this.pushStuffFileList(data)

      handleUploadMessageFile({ url: stuffUrl, count, extension: stuffType }, callback)
    },

    limitTips(event: WechatMiniprogram.CustomEvent) {
      let timer = null as any

      clearTimeout(timer)
      const {
        dataset: { type }
      } = event.currentTarget
      const {
        applyFormatText,
        applyNumberText,
        applySizeText,
        attachmentFormatText,
        attachmentNumberText,
        attachmentSizeText
      } = this.data.deliveryInfo

      this.setData({ showTips: true })

      if (type) {
        this.setData({
          content: `限${attachmentFormatText}格式材料，大小不超过${attachmentSizeText}M，最多${attachmentNumberText}份`
        })
      } else {
        this.setData({
          content: `限${applyFormatText}格式附件，单个文件大小${applySizeText}M以内，最多${applyNumberText}份`
        })
      }
      timer = setTimeout(() => {
        this.setData({ showTips: false })
      }, 3000)
    },

    open(deliveryInfo: any) {
      this.setData({
        deliveryVisible: true,
        deliveryInfo,
        applyTips: deliveryInfo.unCooperateEmailTips ? deliveryInfo.unCooperateEmailTips : deliveryInfo.systemTips,
        isRequired: deliveryInfo.attachmentTips !== '',
        attachementType: deliveryInfo.attachmentFormatText.split('/'),
        stuffType: deliveryInfo.applyFormatText.split('/'),
        isComplete: deliveryInfo.complete === 100 ? true : false,

        deliveryTopIsCheckBox: deliveryInfo.deliveryTopIsCheckBox,
        'deliveryTop.show': deliveryInfo.deliveryTopIsShow,
        'deliveryTop.disabled': deliveryInfo.deliveryTopSourceAmount === 0,
        'deliveryTop.openBtn': deliveryInfo.deliveryTopButton,
        'deliveryTop.check': deliveryInfo.deliveryTopIsCheckBox === 1,
        'deliveryTop.tips': deliveryInfo.deliveryTopTips,
        'deliveryTop.deliveryTopIsCheckBoxDisabled': deliveryInfo.deliveryTopIsCheckBoxDisabled
      })

      this.getAttchaementData()

      if (deliveryInfo.deliveryTopButton) {
        this.fetchVipInfo()
      }
    },

    handleChange(e: WechatMiniprogram.CustomEvent) {
      const { value } = e.detail
      this.setData({ token: value === null ? '' : value })
    },

    async handleDelivery() {
      const { deliveryInfo, token, deliveryTopIsCheckBox } = this.data
      const stuffFileId = this.data.stuffFileList.map((item: any) => item.id).join(',')
      const postData = { jobId: deliveryInfo.jobId, token, stuffFileId, deliveryTopIsCheckBox }

      const { applyStatus, applyId } = await jobApply(postData)
      this.setData(
        {
          deliveryVisible: false,
          stuffFileList: [],
          hasStuffFile: false,
          deliverySuccessVisible: true
        },
        () => {
          this.handleClose(applyStatus, applyId)
        }
      )
    },

    handleClose(applyStatus: number, jobApplyId: number) {
      this.triggerEvent('close', { applyStatus, jobApplyId })
    },

    onVisibleChange(e: any) {
      this.setData({ deliveryVisible: e.detail.visible, stuffFileList: [], hasStuffFile: false })
    },

    handleCheckboxChange(event: WechatMiniprogram.CustomEvent) {
      const { detail, target } = event
      const { checked } = detail
      const { key } = target.dataset
      this.setData({ [key]: checked ? 1 : 2, 'deliveryTop.check': checked })
    },

    async fetchVipInfo() {
      const {
        buyUrl: { jobFast }
      } = await getVipInfo()

      this.setData({ 'deliveryTop.openUrl': `${h5}${jobFast}` })
    },

    handleOpenVip() {
      const {
        deliveryTop: { openUrl }
      } = this.data
      toWebPage(openUrl)
    }
  }
})
