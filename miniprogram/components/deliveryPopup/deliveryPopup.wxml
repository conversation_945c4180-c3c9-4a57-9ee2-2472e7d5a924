<view class="delivery-popup">
  <t-popup t-class="popup" t-class-content="popup-content" visible="{{ deliveryVisible }}" placement="bottom" bind:visible-change="onVisibleChange" close-btn>
    <view class="header">简历投递</view>

    <view class="content">
      <view class="resume-complete">
        <view class="complete">
          <view class="head">在线简历</view>
          <view class="text"
            >(完善度：<view class="count">{{ deliveryInfo.complete }}%</view>)</view
          >
        </view>
        <navigator url="/packages/resume/index" class="path" hover-class="navigator-hover">{{ isComplete ? '预览' : '去完善' }} </navigator>
      </view>

      <view class="attachment">
        <view class="head-box">
          <view class="head">附件简历</view>
          <view class="limit" bindtap="limitTips" data-type="attachment"></view>
        </view>
        <view class="upload-small {{ attachmentDisabled ? 'disabled' : '' }}" wx:if="{{ hasAttachment }}" bindtap="handleUploadAttachment">上传</view>
        <view class="tips" wx:else>全方位展现您的优势</view>
      </view>

      <view class="attachment-check" wx:if="{{ hasAttachment }}">
        <view class="box-title">请选择要发送的1份附件简历（未选择即不发送附件）</view>
        <view class="attachment-box">
          <t-radio-group borderless t-class="box" bind:change="handleChange">
            <view class="label-box" wx:for="{{ attachementList }}" wx:for-item="item" wx:key="index">
              <t-radio block="{{ false }}" value="{{ item.token }}" allow-uncheck>
                <view class="label">{{ item.fileName }}</view>
              </t-radio>
            </view>
          </t-radio-group>
        </view>
      </view>

      <view class="attachment-upload" wx:else bindtap="handleUploadAttachment">上传附件简历</view>

      <view class="material-file">
        <view class="head-box">
          <view class="head">应聘材料</view>
          <view class="limit" bindtap="limitTips"></view>
        </view>
        <view class="upload-small {{ stuffDisabled ? 'disabled' : '' }}" wx:if="{{ hasStuffFile }}" bindtap="handleUploadStuff">上传</view>
      </view>

      <rich-text class="material-tips" wx:if="{{ !hasStuffFile }}" nodes="{{ deliveryInfo.resumeFileTips }}"> </rich-text>

      <view class="material-file-box" wx:if="{{ hasStuffFile }}">
        <view class="box">
          <view class="file-box" wx:for="{{ stuffFileList }}" wx:for-item="item" wx:key="key">
            <view class="label">{{ item.name }}</view>
            <view class="remove" bindtap="handleRemove" data-id="{{ item.id }}"></view>
          </view>
        </view>
      </view>
      <view class="material-file-upload" wx:else bindtap="handleUploadStuff">上传应聘材料</view>

      <view wx:if="{{ deliveryTop.show }}" class="apply-add-top">
        <view class="add-top-label">投递置顶</view>
        <view class="value">
          <t-checkbox class="checkbox" disabled="{{ deliveryTop.deliveryTopIsCheckBoxDisabled }}" t-class-icon="checkbox-icon" t-class-label="checkbox-label" bindchange="handleCheckboxChange" icon="rectangle" checked="{{ deliveryTop.check }}" data-key="deliveryTopIsCheckBox">
            <!-- 使用后您的简历在单位端将置顶展示（剩余<i class="color-primary">12</i>次） -->
            <rich-text nodes="{{ deliveryTop.tips }}"></rich-text>
          </t-checkbox>

          <view wx:if="{{ deliveryTop.openBtn }}" class="renew" bind:tap="handleOpenVip">解锁更多</view>
        </view>
      </view>
    </view>

    <view class="delivery-tips" wx:if="{{ applyTips }}"> {{ applyTips }}</view>

    <view class="delivery-buttom">
      <t-button bindtap="handleDelivery" block theme="primary" size="large">确认投递</t-button>
    </view>
  </t-popup>
  <view class="toast" wx:if="{{ showTips }}">{{ content }}</view>

  <delivery-success-dialog visible="{{ deliverySuccessVisible }}" />
</view>
