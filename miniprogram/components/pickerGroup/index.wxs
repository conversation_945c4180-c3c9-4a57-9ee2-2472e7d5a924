// @ts-check

/**
 *
 * @param {number} level - 含有几个子级（只支持二、三级）
 * @param {string} key - 选项值的字段名
 * @param {string} children - 选项子级字段名
 * @param {any} options - 选项数组
 * @param {Array<{label: string, value: string}>} data - 选中的列表
 * @returns {boolean}
 */
function some(level, key, children, options, data) {
  var dataValues = data.map(function (item) {
    return item.value
  })

  var optionValues = []

  if (level === 2) {
    options.map(function (item) {
      optionValues.push(item[key])
    })
  } else {
    var list = []

    options.map(function (item) {
      var items = item[children]

      if (items) {
        items.map(function (item) {
          list.push(item)
        })
      }
    })

    list.map(function (item) {
      optionValues.push(item[key])
    })
  }

  if (dataValues.length === 0) {
    return optionValues.indexOf('') > -1
  }

  return optionValues.some(function (item) {
    return dataValues.indexOf(item) > -1
  })
}

/**
 *
 * @param {string} value - 选项中的值
 * @param {Array<{label: string, value: string}>} data - 选中的列表
 * @returns {boolean}
 */
function includes(value, data) {
  if (data.length === 0) {
    return value === ''
  }

  return (
    data
      .map(function (item) {
        return item.value
      })
      .indexOf(value) > -1
  )
}

module.exports = {
  some: some,
  includes: includes
}
