<wxs src="./index.wxs" module="utils" />

<view class="picker-mask {{ popupVisible ? 'picker-mask--show' : '' }}" bindtap="handleBack"></view>

<view class="picker-group-layout" hidden="{{ popupVisible === false }}">
  <view class="picker-group-container">
    <!-- <header title="{{ title }}" fixed="{{ false }}" bindback="handleBack" /> -->
    <view class="picker-group-header">
      <view class="picker-group-title" slot="title">
        {{ title }}·<text class="amount">{{ checkedList.length | 0 }}</text>
      </view>
      <!-- <view class="close" bindtap="handleBack"></view> -->
    </view>

    <slot></slot>

    <view class="picker-group-main">
      <view class="picker-popup-content">
        <view class="picker-popup-side">
          <t-side-bar value="{{ sideBarIndex }}" bindchange="handleSideBarChange">
            <t-side-bar-item class="side-bar-item" wx:for="{{ options }}" wx:key="index" value="{{ index }}" label="{{ item[optionsLabel] }}" badge-props="{{ { dot: utils.some(level, optionsValue, optionsChildren, item[optionsChildren], checkedList) } }}" />
          </t-side-bar>
        </view>

        <view class="picker-popup-scroll {{ multiple ? '' : 'safe-area' }}">
          <scroll-view class="scroll-content" scroll-y scroll-with-animation enhanced scroll-top="{{ scrollTop }}" bindscroll="handleScroll" binddragstart="handleDragStart">
            <block wx:if="{{ level === 2 }}">
              <view wx:for="{{ options }}" wx:key="index" wx:for-index="ind" class="picker-popup-section">
                <view class="picker-popup-title" wx:if="{{ item[optionsLabel] && item[optionsValue].length }}">
                  <view class="left">
                    {{ item[optionsLabel] }} <i wx:if="{{ item.showVipTips }}"></i>  
                  </view>
                  <view wx:if="{{ item.showVipTips }}" class="right">
                    <veiw class="aside">{{ item.v === '编制查询' ? asideLabel.establishment : asideLabel.heat }}</veiw>
                    <i bindtap="handleOpenTipsDialog" data-key="{{item.v === '编制查询' ? 'establishment' : 'heat'}}"></i>
                  </view>
                </view>

                <t-grid column="{{ column }}" gutter="{{ gutter }}" border="{{ false }}">
                  <block wx:for="{{ item[optionsChildren] }}" wx:key="index">
                    <t-grid-item>
                      <view class="custom-checkbox {{ utils.includes(item[optionsValue], checkedList) ? 'is-checked' : '' }}" data-label="{{ item[optionsLabel] }}" data-value="{{ item[optionsValue] }}" data-needVip="{{ options[ind].showVipTips }}" data-type="{{ options[ind].type }}" bindtap="handleClick">
                        <text class="custom-checkbox-text">{{ item[optionsLabel] }}</text>
                      </view>
                    </t-grid-item>
                  </block>
                </t-grid>
              </view>
            </block>

            <block wx:else>
              <view wx:for="{{ options }}" wx:key="index" wx:for-index="index" class="picker-popup-section">
                <block wx:for="{{ item[optionsChildren] }}" wx:key="index">
                  <view class="picker-popup-title" wx:if="{{ item[optionsLabel] && item[optionsValue].length }}"> {{ item[optionsLabel] }} </view>

                  <t-grid column="{{ column }}" gutter="{{ gutter }}" border="{{ false }}">
                    <block wx:for="{{ item[optionsChildren] }}" wx:key="index">
                      <t-grid-item>
                        <view class="custom-checkbox {{ utils.includes(item[optionsValue], checkedList) ? 'is-checked' : '' }}" data-label="{{ item[optionsLabel] }}" data-value="{{ item[optionsValue] }}" bindtap="handleClick">
                          <text class="custom-checkbox-text">{{ item[optionsLabel] }}</text>
                        </view>
                      </t-grid-item>
                    </block>
                  </t-grid>
                </block>
              </view>
            </block>
          </scroll-view>
        </view>
      </view>
    </view>

    <view class="picker-group-footer" wx:if="{{ multiple }}">
      <t-button class="reset-button" theme="light" bindtap="handleReset">重置</t-button>
      <t-button class="confirm-button" theme="primary" bindtap="handleConfirm">确定</t-button>
    </view>
  </view>
</view>

<login-dialog visible="{{ loginVisible }}" bind:loginSuccess="handleLoginSuccess" />

<root-portal>
  <t-dialog t-class="superior-tips-dialog" overlay-props="{{ { zIndex: 11510 } }}" z-index="{{ 11520 }}" visible="{{ dialogVisible }}" title="{{ dialogTitle }}" t-class-content="dialog-content">
    <view slot="content" class="superior-dialog-content">
      <text class="content">{{ dialogContent }}</text>
      <view wx:if="{{ dialogDescription }}" class="description">{{ dialogDescription }}</view>

      <view class="footer">
        <t-button class="btn cancel" hover-class="none" wx:if="{{ dialogCancelBtn }}" variant="base" shape="round" bindtap="handleCloseTipsDialog">{{ dialogCancelBtn }}</t-button>
        <t-button class="btn confirm" hover-class="none" wx:if="{{ dialogConfirmBtn }}" variant="base" shape="round" theme="primary" bindtap="handleTipsConfirm">{{ dialogConfirmBtn }}</t-button>
      </view>
    </view>
  </t-dialog>
</root-portal>
