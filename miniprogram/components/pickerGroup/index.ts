import { defaultDuration, h5 } from '@/settings'
import { showLoading, toWebPage } from '@/utils/util'
import { getVipFilterInfo } from '@/api/person'

Component({
  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {
    // 1职位、2公告
    type: {
      type: Number,
      value: 1
    },
    
    /** 选项值 是否可见 */
    visible: { type: Boolean, value: false },

    /** 选项值 选中值 单选（字符串）多选（数组） */
    model: { type: null, value: '' },

    /** 选项值 顶部标题 */
    title: { type: String, value: '更多筛选' },

    /** 选项值 限制选择的个数 */
    limit: { type: Number, value: 1 },

    /** 选项值 数据几级（只支持2和3级） */
    level: { type: Number, value: 2 },

    /** 选项值 选择项栏数 */
    column: { type: Number, value: 1 },

    /** 选项值 选择项间距 */
    gutter: { type: Number, value: 20 },

    /** 选项值 选项数据 */
    options: { type: Array, value: [] },

    /** 选项值 选项展示文本字段 */
    optionsLabel: { type: String, value: 'label' },

    /** 选项值 选项选中值字段 */
    optionsValue: { type: String, value: 'value' },

    /** 选项值 选项子级字段 */
    optionsChildren: { type: String, value: 'children' }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isLogin: false,
    isVip: false,
    loginVisible: false,
    popupVisible: false,

    multiple: false,

    checkedList: [],
    paramsKey: {} as { [key: string]: number },
    isScroll: true,
    sideBarIndex: 0,
    scrollTop: 0,
    offsetTop: 0,
    offsetTopList: [],
    openVipTipsData: {
      dialogTitle: '提示',
      dialogContent: '开通VIP服务即可使用高级筛选功能，精准捕捉目标职位&公告',
      dialogDescription: '高级筛选功能包括：职位&公告 编制查询、热度情况筛选等',
      dialogCancelBtn: '取消',
      dialogConfirmBtn: '升级VIP',
      dialogConfirmBtnUrl: ''
    },

    asideLabel: {
      establishment: '',
      heat: ''
    },
    asideLabelOptions: {
      job: {
        establishment: '“上岸”快人一步',
        heat: '追踪职位实时热度'
      },
      announcement: {
        establishment: '捕捉每一次入编机会',
        heat: '追踪公告实时热度'
      }
    },
    explainOptions: {
      job: {
        establishment: {
          dialogTitle: '编制查询说明',
          dialogContent: '可查询包含行政编制/事业编制/备案制等编制类型的优质职位',
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        },
        heat: {
          dialogTitle: '职位热度说明',
          dialogContent: `根据职位的关注度、招录人数竞争比综合分析得出

                          一般：表示关注人数较少，可重点关注；

                          较热：表示该职位备受欢迎，可持续关注；

                          火爆：表示该职位竞争激烈，可抓紧机会争取。`,
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        }
      },
      announcement: {
        establishment: {
          dialogTitle: '编制查询说明',
          dialogContent: '可查询包含编制职位的优质公告',
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        },
        heat: {
          dialogTitle: '公告热度说明',
          dialogContent: '根据公告的浏览、关注、投递情况等综合分析得出',
          dialogConfirmBtn: '我知道了',
          dialogConfirmBtnUrl: ''
        }
      }
    },
    dialogTitle: '',
    dialogContent: '',
    dialogDescription: '',
    dialogCancelBtn: '',
    dialogConfirmBtn: '',
    dialogConfirmBtnUrl: ''
  },

  observers: {
    visible(value: boolean) {
      this.watchVisible(value)
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleLoginSuccess() {
      this.fetchVipFilterInfo()
      this.triggerEvent('loginSuccess')
    },

    async fetchVipFilterInfo() {
      const { isLogin, isVip, url } = await getVipFilterInfo()
      this.setData({
        isLogin,
        isVip,
        'openVipTipsData.dialogConfirmBtnUrl': url
      })
    },

    handleOpen() {
      const { type, asideLabelOptions } = this.data

      const asideLabelKey = type === 1 ? 'job' : 'announcement'

      this.setData({
        asideLabel: asideLabelOptions[asideLabelKey]
      })
      this.fetchVipFilterInfo()
    },

    // handleClose() {
    //   this.setData({ visible: false })
    // },

    handleCloseTipsDialog() {
      this.setData({ dialogVisible: false })
    },

    handleTipsConfirm() {
      const { dialogConfirmBtnUrl } = this.data

      if (dialogConfirmBtnUrl) {
        this.setData({
          dialogVisible: false,
          visible: false
        })
        toWebPage(`${h5}${dialogConfirmBtnUrl}`)
        return
      }

      this.setData({ dialogVisible: false })
    },

    handleOpenTipsDialog(event: WechatMiniprogram.CustomEvent) {
      const {
        currentTarget: {
          dataset: { key }
        }
      } = event

      const { type, explainOptions } = this.data

      const tipsObject: any = type == 1 ? explainOptions['job'] : explainOptions['announcement']
      const {
        dialogTitle,
        dialogContent,
        dialogDescription = '',
        dialogCancelBtn = '',
        dialogConfirmBtn = '',
        dialogConfirmBtnUrl = ''
      } = tipsObject[key]

      this.setData({
        dialogTitle,
        dialogContent,
        dialogDescription,
        dialogCancelBtn,
        dialogConfirmBtn,
        dialogConfirmBtnUrl,
        dialogVisible: true
      })
    },

    watchVisible(value: boolean) {
      if (value) {
        const { level, offsetTopList } = this.data

        showLoading()

        if (!/^2|3$/.test(level.toString())) {
          this.setData({ level: 2 })
        }

        this.updateMultiple()
        this.updateCheckedOptions()
        this.handleOpen()
        this.setData({ scrollTop: 0, sideBarIndex: 0, popupVisible: value }, () => {
          if (offsetTopList.length === 0) {
            this.updateOffsetTopData()
          }
        })
      }
    },

    updateMultiple() {
      const { limit } = this.data
      const valid = typeof limit === 'number'

      if (valid) {
        this.setData({ multiple: limit > 1 })
      }
    },

    updateCheckedOptions() {
      const { model, level, options, optionsLabel, optionsValue, optionsChildren } = this.data
      const checkedList: never[] = []
      const values: any = []

      if (Array.isArray(model)) {
        values.push(...model)
      }

      if (typeof model === 'string' && model !== '') {
        values.push(model)
      }

      const update = (target: any, data: any) => {
        const label = data[optionsLabel]
        const value = data[optionsValue]

        const checked = values.includes(value)
        const exist = target.map((checked: Miniprogram.OptionType) => checked.value).includes(value)

        if (checked && exist === false) {
          target.push(<never>{ label, value })
        }
      }

      options.map((item: any) => {
        item[optionsChildren].map((child: any) => {
          if (level === 2) {
            update(checkedList, child)
          } else {
            child[optionsChildren].map((item: any) => {
              update(checkedList, item)
            })
          }
        })
      })

      this.setData({ checkedList }, () => wx.hideLoading())
    },

    updateOffsetTopData() {
      let offsetTop = 0
      const query = this.createSelectorQuery()

      query
        .selectAll('.picker-popup-section')
        .boundingClientRect()
        .exec(([rects]) => {
          const offsetTopList = rects.map((rect: WechatMiniprogram.BoundingClientRectCallbackResult, index: number) => {
            if (index === 0) {
              offsetTop = rect.top
            }

            return rect.top - offsetTop
          })

          this.setData({ offsetTop, offsetTopList })
        })
    },

    handleBack() {
      this.setData({ popupVisible: false }, this.handlePopupClose)
    },

    handlePopupClose() {
      this.triggerEvent('close', { value: false })
    },

    handleSideBarChange(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.detail
      const { offsetTopList } = this.data

      this.setData({
        isScroll: false,
        sideBarIndex: value,
        scrollTop: offsetTopList[value]
      })
    },

    handleScroll(event: WechatMiniprogram.CustomEvent) {
      if (this.data.isScroll === false) return

      const { scrollTop } = event.detail
      const { offsetTop, offsetTopList } = this.data
      const intScrollTop = Math.floor(scrollTop)

      if (intScrollTop < offsetTop) {
        this.setData({ sideBarIndex: intScrollTop > 0 ? 1 : 0 })
        return
      }

      const index = offsetTopList.findIndex((item: number) => {
        const top = offsetTop + item
        return top > intScrollTop && top - intScrollTop <= offsetTop
      })

      if (index > -1) {
        this.setData({ sideBarIndex: index })
      }
    },

    handleDragStart() {
      if (this.data.isScroll) return

      this.setData({ isScroll: true })
    },

    handleClose(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.currentTarget.dataset
      const { checkedList } = this.data
      const index = checkedList.findIndex((item: Miniprogram.OptionType) => item.value === value)

      checkedList.splice(index, 1)
      this.setData({ checkedList })
    },

    handleClick(event: WechatMiniprogram.CustomEvent) {
      const { label, value, needvip, type } = event.currentTarget.dataset
      const { checkedList, limit, multiple, isLogin, isVip, openVipTipsData, paramsKey } = this.data
      if (needvip) {// 需要vip
        if (!isLogin) {
          this.setData({ loginVisible: true })
          return
        }
        if (!isVip) {
          this.setData({
            ...openVipTipsData,
            dialogVisible: true
          })
          return
        }
      }
      const index = checkedList
        .map((item: Miniprogram.OptionType) => item.value)
        .findIndex((item: string) => item === value)
      const { length } = checkedList

      if (value === '') {
        this.handleReset()

        if (multiple === false) {
          this.handleConfirm()
        }
        return
      }

      const key = value.split('_')[0]
      const ind = checkedList.map((item: Miniprogram.OptionType) => item.value).findIndex((item: string) => item.includes(key))
      
      if (length < limit || index > -1 || (type === 'radio' && paramsKey[key] === 1)) {
        if (index > -1) {
          checkedList.splice(index, 1)
          paramsKey[key] = 0
        } else {
          if (type === 'radio' && paramsKey[key] === 1) {
            checkedList.splice(ind, 1)
          }
          checkedList.unshift(<never>{ label, value })
          paramsKey[key] = 1
        }

        this.setData({ checkedList }, () => {
          if (multiple === false && checkedList.length === limit) {
            this.handleConfirm()
          }
        })
      } else {
        if (multiple) {
          wx.showToast({ title: `最多可选${limit}项`, icon: 'none', duration: defaultDuration })
        } else {
          this.setData({ checkedList: [<never>{ label, value }] }, () => {
            this.handleConfirm()
          })
        }
      }
    },

    handleReset() {
      this.setData({ checkedList: [] })
    },

    handleConfirm() {
      const { multiple, checkedList } = this.data
      const label = checkedList.map((item: Miniprogram.OptionType) => item.label)
      const value = checkedList.map((item: Miniprogram.OptionType) => item.value)

      const result = {
        label: multiple ? label : label.join(),
        value: multiple ? value : value.join()
      }

      this.setData({ popupVisible: false }, () => {
        this.triggerEvent('change', result)
      })
    }
  }
})
