import { checkLogin } from '@/utils/store'

import { getBaseShowcase } from '@/utils/store'
import { toWebPage } from '@/utils/util'
import { h5 } from '@/settings'

Component({
  options: {
    virtualHost: true
  },
  externalClasses: ['c-class'],
  /**
   * 组件的属性列表
   */
  properties: {},

  lifetimes: {
    attached: function () {
      this.getShowcase()
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    src: '',
    url: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async getShowcase() {
      const {
        jobList: { image, url }
      } = await getBaseShowcase()
      this.setData({ src: image, url })
    },

    onClick() {
      const { url } = this.data
      const isLogin = checkLogin()
      if (isLogin) {
        toWebPage(`${h5}${url}`)
        return
      }
      this.triggerEvent('click', { isLogin: false })
    }
  }
})
