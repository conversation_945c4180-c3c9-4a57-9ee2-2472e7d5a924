import { addUnlimited } from '@/utils/array'
import { getMajor } from '@/utils/store'

Component({
  externalClasses: ['t-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    /** 显隐 */
    visible: { type: Boolean, value: false },

    column: { type: Number, value: 2 },

    /** 单选 string；多选 string[] */
    model: { type: null, value: '' },

    /** 文本key*/
    labelKey: { type: String, value: 'majorLabel' },

    /** 默认文本*/
    defaultLabel: { type: null, value: '学科' },

    /** 单选 string；多选 string[] */
    label: { type: null, value: '' },

    title: { type: String, value: '请选择' },

    limit: { type: Number, value: 1 },

    unlimit: { type: Boolean, value: false }
  },

  observers: {
    visible: function (value) {
      this.setData({ popupVisible: value })
    },
    model: function () {
      this.handleLabel()
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    popupVisible: false,

    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const { unlimit } = this.data
      const list = await getMajor()
      const options = unlimit ? addUnlimited(list) : list

      this.setData({ options }, () => {
        this.handleLabel()
      })
    },

    handleLabel() {
      const { model, options } = this.data
      const { length = 0 } = model || {}

      const values = Array.isArray(model) ? model : length > 0 ? [model] : []
      const label: Array<string> = []
      const getLabel = (options: Array<object>) => {
        options.forEach((item: any) => {
          const { k, v, children } = item
          if (values.includes(k)) {
            label.push(v)
          }
          if (Array.isArray(children)) {
            getLabel(children)
          }
        })
      }

      if (values.length) {
        getLabel(options)
      }
      // 去重 一级、二级或者热门会有重复
      const filterLabel = Array.from(new Set(label))
      this.updateLabel(filterLabel)
    },

    updateLabel(label: Array<string> | string) {
      const { labelKey, defaultLabel } = this.data
      const { length: labelLength } = label
      const labelArray = Array.isArray(label) ? label : labelLength > 0 ? [label] : []
      const { length } = labelArray
      const realLabel = length === 0 ? defaultLabel : `${defaultLabel}·${length}`
      this.triggerEvent('updateLabel', { labelKey, label: realLabel, labelArray })
    },

    handleClick() {
      this.setData({ popupVisible: true })
    },

    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.setData({ label })
      this.triggerEvent('change', { label, value })
    }
  }
})
