import { addUnlimited } from '@/utils/array'
import { getAnnouncementType } from '@/utils/store'

Component({
  externalClasses: ['t-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    /** 显隐 */
    visible: { type: Boolean, value: false },

    column: { type: Number, value: 2 },
    /** 单选 string；多选 string[] */
    model: { type: null, value: '' },

    /** 单选 string；多选 string[] */
    label: { type: null, value: '' },

    title: { type: String, value: '请选择' },

    limit: { type: Number, value: 1 },

    unlimit: { type: Boolean, value: false },

    /** 文本key*/
    labelKey: { type: String, value: 'announcementLabel.' },

    /** 默认文本*/
    defaultLabel: { type: null, value: '公告类型' },
  },

  observers: {
    visible: function (value) {
      this.setData({ popupVisible: value })
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    popupVisible: false,

    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const { unlimit } = this.data
      const list = await getAnnouncementType()
      const options = unlimit ? addUnlimited(list) : list
      this.setData({ options })
    },

    handleClick() {
      this.setData({ popupVisible: true })
    },

    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { labelKey, defaultLabel } = this.data
      const { label, value } = event.detail
      const { length: labelLength } = label
      const labelArray = Array.isArray(label) ? label : labelLength > 0 ? [label] : []
      const { length } = labelArray
      const realLabel = length === 0 ? defaultLabel : `${defaultLabel}·${length}`
      this.setData({ label })
      this.triggerEvent('change', { label, value })
      this.triggerEvent('updateLabel', { labelKey, label: realLabel, labelArray })
    }
  }
})
