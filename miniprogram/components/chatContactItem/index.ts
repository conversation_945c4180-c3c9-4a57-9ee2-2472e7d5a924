import { deleteContact, setContactPinned } from '@/api/chat'

// components/chat-contact-item/index.ts
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    chatList: { type: Array, value: [] }
  },

  /**
   * 组件的初始数据
   */
  data: {
    list: <any>[]
  },

  observers: {
    chatList(data) {
      const pinnedList: any[] = []
      const normalList: any[] = []

      data.forEach((item: any) => {
        ;(item.isTop === '1' ? pinnedList : normalList).push(item)
      })

      pinnedList.sort((a, b) => (a.trueTime > b.trueTime ? -1 : 1))
      normalList.sort((a, b) => (a.trueTime > b.trueTime ? -1 : 1))

      this.setData({ list: [...pinnedList, ...normalList] })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    updateSessionList(id: string, data?: any) {
      const index = getApp().sessionList.findIndex((item: any) => item.chatId === id)

      data ? getApp().sessionList.splice(index, 1, data) : getApp().sessionList.splice(index, 1)

      this.setData({ chatList: getApp().sessionList })

      if (this.data.chatList.length === 0) {
        this.triggerEvent('update')
      }
    },

    async handlePinned(event: WechatMiniprogram.CustomEvent) {
      const { status, id } = event.currentTarget.dataset

      await setContactPinned(id)

      const value = this.data.list.find((item: any) => item.chatId === id)
      value.isTop = status === '1' ? '2' : '1'

      if (value) {
        this.updateSessionList(id, value)
      }
    },

    async handleDel(event: WechatMiniprogram.CustomEvent) {
      const { id } = event.currentTarget.dataset

      await deleteContact(id)

      this.updateSessionList(id)
    }
  }
})
