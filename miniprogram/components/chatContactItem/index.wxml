<t-swipe-cell wx:for="{{ list }}" wx:for-item="item" wx:for-index="index" wx:key="chatId">
  <navigator url="/packages/chat/index/index?chatId={{ item.chatId }}&jobId={{ item.jobId }}">
    <view class="item {{ item.isTop === '1' ? 'is-top' : '' }}">
      <view class="message">
        <image class="avatar" src="{{ item.avatarUrl }}" alt="" />
        <div class="count" wx:if="{{ item.unreadAmount !== 0 }}">{{ item.unreadAmount > 99 ? '99+' : item.unreadAmount }}</div>
      </view>

      <div class="message-right">
        <view class="top-information">
          <view class="name">
            <text class="user" decode="true">{{ item.username }} &nbsp; </text>
            <view class="info">{{ item.info }}</view>
          </view>

          <view class="time">{{ item.showTime }}</view>
        </view>

        <rich-text nodes="<div class='content'>{{ item.listContent }}</div>"></rich-text>
      </div>
    </view>
  </navigator>

  <view slot="right" class="btn-wrapper">
    <view class="top" bindtap="handlePinned" data-status="{{ item.isTop }}" data-id="{{ item.chatId }}">{{ item.isTop === '1' ? '取消置顶' : '置顶' }}</view>
    <view class="del" bindtap="handleDel" data-id="{{ item.chatId }}">删除</view>
  </view>
</t-swipe-cell>
