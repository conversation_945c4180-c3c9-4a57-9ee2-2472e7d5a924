@use 'styles/variables' as *;

.avatar {
  width: 90rpx;
  height: 90rpx;
  margin-right: 16rpx;
  border-radius: 50%;
  object-fit: cover;
}

.item {
  display: flex;
  padding: 25rpx 30rpx;

  &.is-top {
    background-color: #fffbf4;
  }
}

.btn-wrapper {
  display: flex;
  width: 268rpx;
  height: 100%;

  .top,
  .del {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 134rpx;
    color: $color-white;
  }

  .top {
    background-color: $color-primary;
  }

  .del {
    background-color: $color-point;
  }
}

.message {
  position: relative;

  .count {
    position: absolute;
    top: 0;
    left: 63rpx;
    padding: 0 8rpx;
    height: 26rpx;
    color: $color-white;
    font-size: 20rpx;
    line-height: 26rpx;
    background-color: $color-point;
    border-radius: 13rpx;
  }
}

.message-right {
  width: calc(100% - 100rpx);
}

.top-information {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: $font-color-label;

  .name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @include utils-ellipsis;
  }

  .user {
    font-weight: bold;
    font-size: 30rpx;
    color: $font-color;

    @include utils-ellipsis;
  }

  .info {
    @include utils-ellipsis;

    flex: 1;
    font-size: 24rpx;
  }

  .time {
    white-space: nowrap;
    font-size: 22rpx;
  }
}

.content {
  font-size: 26rpx;
  margin-top: 16rpx;
  color: $font-color-basic;
  display: block;
  @include utils-ellipsis;
}
