<view class="picker-area t-class">
  <picker-popup visible="{{ popupVisible }}" model="{{ model }}" title="{{ title }}" limit="{{ limit }}" column="3" options="{{ options }}" options-label="v" options-value="k" bindchange="handleChange">
    <!-- <view class="replenish-content">
      <view  class="wrapper-tips">
        当前可选1个地区，登录后最多可选5个。
        <t-button class="btn" theme="primary" size="small" bind:tap="openLoginDialog">立即登录</t-button>
      </view>
      <view  class="wrapper-tips">
        当前可选1个地区，完善简历后最多可选5个。
        <t-button class="btn" theme="primary" size="small">立即完善</t-button>
      </view>
    </view> -->
  </picker-popup>

  <login-dialog bind:loginSuccess="loginSuccess" visible="{{ loginDialogVisible }}" />
</view>
