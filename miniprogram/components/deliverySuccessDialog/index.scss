@use 'styles/variables' as *;

.deliver-success-popup {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;

  .mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: $font-color-tips;
  }

  .container {
    position: absolute;
    z-index: 1;
    width: 630rpx;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 62rpx 48rpx 70rpx;
    box-sizing: border-box;
    background: url(#{$assets}/delivery/success-bg.png) no-repeat left top/contain;
  }

  .title {
    font-size: 46rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 18rpx;
  }

  .sub-title {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 52rpx;

    &::before {
      content: '';
      height: 4rpx;
      flex-grow: 1;
      margin-right: 12rpx;
      background: linear-gradient(to right, transparent, transparent 60%, #ffdfa8 70%, #ffdfa8);
    }

    &::after {
      content: '';
      height: 4rpx;
      flex-grow: 1;
      margin-left: 12rpx;
      background: linear-gradient(to left, transparent, transparent 60%, #ffdfa8 70%, #ffdfa8);
    }
  }

  .content {
    padding-left: 25rpx;

    .list {
      padding-left: 96rpx;
      margin-bottom: 60rpx;

      $icon: (1, job-fast), (2, gc-vip);

      @each $i, $name in $icon {
        &:nth-child(#{$i}) {
          background: url(#{$assets}/icon/#{$name}.png) no-repeat left/80rpx 80rpx;
        }
      }

      & ~ .list {
        margin-top: 50rpx;
      }

      .name {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 4rpx;
      }

      .desc {
        display: flex;
        align-items: center;
        color: $font-color-basic;
        font-size: 28rpx;

        .nav {
          color: $color-primary;
          padding-right: 14rpx;
          background: url('#{$assets}/icon/into.png') no-repeat right/10rpx 18rpx;
        }
      }
    }
  }

  .close {
    line-height: 88rpx;
    border-radius: 88rpx;
    background-color: $color-primary;
    color: #fff;
    font-size: 32rpx;
    text-align: center;
  }
}
