import { getVipInfo } from '@/api/person'
import { h5 } from '@/settings'
import { toWebPage } from '@/utils/util'
Component({
  options: {
    virtualHost: true
  },

  externalClasses: ['c-class', 'c-trigger-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  observers: {
    visible: function (value) {
      this.setData({
        dialogVisible: value
      })
      if (value) {
        this.setData({ time: 5 })
        this.countdown()
        this.fetchVipInfo()
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    dialogVisible: false,
    time: 5,
    openVipUrl: '',
    openjobFastUrl: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    countdown() {
      const _this = this
      const timer = setTimeout(() => {
        let time = _this.data.time
        time -= 1
        if (time === -1) {
          clearInterval(timer)
          _this.setData({ dialogVisible: false })
          return
        }
        _this.setData({ time })
        _this.countdown()
      }, 1000)
    },

    close() {
      this.setData({
        dialogVisible: false
      })
      this.triggerEvent('close')
    },

    async fetchVipInfo() {
      const {
        buyUrl: { vip, jobFast }
      } = await getVipInfo()

      this.setData({
        openVipUrl: `${h5}${vip}`,
        openjobFastUrl: `${h5}${jobFast}`
      })
    },

    handleOpen(e: WechatMiniprogram.CustomEvent) {
      const {
        currentTarget: {
          dataset: { type }
        }
      } = e
      const { openVipUrl, openjobFastUrl } = this.data
      const url = type == 1 ? openjobFastUrl : openVipUrl
      toWebPage(url)
    }
  }
})
