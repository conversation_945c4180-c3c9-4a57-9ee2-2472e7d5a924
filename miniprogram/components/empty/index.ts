// components/empty/index.ts
Component({
  options: {
    multipleSlots: true,
    virtualHost: true
  },
  externalClasses: ['c-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    url: {
      type: String,
      value: '//img.gaoxiaojob.com/uploads/mini/empty/primary.png'
    },
    imageWidth: {
      type: Number,
      value: 350
    },
    imageHeight: {
      type: Number,
      value: 250
    },
    description: {
      type: String,
      value: '暂无更多'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {}
})
