@use '../../styles/variables' as *;

.resume-perfect {
  font-size: 24rpx;
  display: flex;
  align-items: center;
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: $border-radius;
  background-color: rgba($color: $font-color, $alpha: 0.8);
  bottom: calc(118rpx + env(safe-area-inset-bottom));
  bottom: calc(118rpx + constant(safe-area-inset-bottom));

  // &::after {
  //   content: '';
  //   display: block;
  //   position: absolute;
  //   top: 100%;
  //   right: 220rpx;
  //   width: 0;
  //   height: 0;
  //   border-top: 10rpx solid rgba($color: $font-color, $alpha: 0.8);
  //   border-bottom: 10rpx solid transparent;
  //   border-left: 8rpx solid transparent;
  //   border-right: 8rpx solid transparent;
  // }

  .perfect-tips {
    padding-left: 12rpx;
    flex-grow: 1;
  }

  .perfect-btn {
    width: 124rpx;
    height: 48rpx;
    background: #ffa000;
    border-radius: 24rpx;
    text-align: center;
    line-height: 48rpx;
  }
}
