import { checkMemberComplete } from '@/api/apply'

Component({
  options: {
    virtualHost: true
  },
  externalClasses: ['c-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  observers: {
    visible: function (value) {
      this.setData({
        realVisible: value
      })
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    realVisible: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    close() {
      this.setData({ realVisible: false })
    },
    async toPerfect() {
      // 看用户的简历是否完善
      const res = await checkMemberComplete({})

      const { resumeStepNum } = res

      if (resumeStepNum < 4) {
        wx.navigateTo({ url: '/packages/resume/required' })
        return
      }

      wx.navigateTo({ url: '/packages/resume/index' })
    }
  }
})
