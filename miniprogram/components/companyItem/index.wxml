<navigator class="item c-class" url="/packages/company/detail/index?id={{ detail.id }}" hover-class="none">
  <view class="unit-logo">
    <image class="logo {{ detail.companyRole === '2' ? 'vip' : '' }}" src="{{ detail.logoUrl }}" mode="aspectFit" lazy-load="false" />
  </view>
  <view class="detail">
    <view class="title"> {{ detail.name }} </view>
    <view class="info">
      在招职位：<i class="amount">{{ detail.jobAmount }}</i
      >在招公告：<i class="amount">{{ detail.announcementAmount }}</i>
    </view>
    <view class="bottom" wx:if="{{ showFooter }}">
      <view class="tag-content">
        <view class="tag" wx:if="{{ detail.type }}">{{ detail.type }}</view>
        <view class="tag" wx:if="{{ detail.nature }}">{{ detail.nature }}</view>
      </view>
      <view class="address" wx:if="{{ detail.areaName }}"> {{ detail.areaName }} </view>
    </view>
  </view>
</navigator>
