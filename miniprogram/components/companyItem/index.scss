@use 'styles/variables' as *;

.item {
  padding: 22rpx 30rpx 14rpx;
  display: flex;
  align-items: center;

  .unit-logo {
    width: 122rpx;
    flex-shrink: 0;

    .logo {
      width: 100rpx;
      height: 100rpx;
    }
    .vip {
      position: relative;
      &::after {
        content: '';
        display: block;
        position: absolute;
        right: 0;
        bottom: 0;
        width: 28rpx;
        height: 28rpx;
        background: url(#{$assets}/icon/certification.png) no-repeat center/contain;
      }
    }
  }

  .detail {
    flex-grow: 1;
    overflow: hidden;
  }

  .title {
    line-height: 48rpx;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 14rpx;
  }

  .info {
    margin-bottom: 14rpx;

    .amount {
      color: $color-primary;
      margin-right: 20rpx;
      font-weight: bold;
    }
  }

  .bottom {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    font-size: 24rpx;

    .tag-content {
      flex-grow: 1;
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;

      .tag {
        margin-bottom: 14rpx;
        line-height: 44rpx;
        padding: 0 12rpx;
        border-radius: 4rpx;
        background-color: #f3f8fd;
        color: $font-color-basic;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 12rpx;
        margin-bottom: 16rpx;
      }
    }

    .address {
      line-height: 44rpx;
      height: 44rpx;
      margin-bottom: 16rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 120rpx;
      flex-shrink: 0;
      padding-left: 32rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/address.png') no-repeat left/24rpx;
      color: $font-color-label;
    }
  }
}
