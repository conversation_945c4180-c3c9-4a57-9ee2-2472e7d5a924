@use 'styles/variables' as *;

.tips-main {
  margin-top: 40rpx;
  padding: 0 10rpx;

  .title {
    padding-left: 43rpx;
    background: url('//img.gaoxiaojob.com/uploads/mini/icon/warning.png') no-repeat left center / 26rpx;
    margin-bottom: 10rpx;
  }

  .risk {
    position: relative;
    margin-top: 10rpx;
    font-size: 24rpx;
    color: $font-color-label;
    line-height: 2;
    padding-left: 20rpx;

    &::before {
      content: '*';
      position: absolute;
      left: 0;
      color: $color-point;
    }
  }

  .check {
    display: inline-flex;
    padding-left: 20rpx;
    font-size: 24rpx;
    color: $font-color-label;

    .point {
      color: #4b6cf5;
      text-decoration: underline;
    }
  }
}

.security-pop-content {
  color: $font-color;

  .security-pop-header {
    text-align: center;
    padding: 35rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    border-bottom: 2rpx solid $border-color;
  }

  .security-pop-title {
    color: $color-point;
    background: none;
    margin: 0;
    padding-top: 30rpx;
    padding-left: 43rpx;
    font-size: 30rpx;
  }

  .security-pop-content {
    line-height: 1.5;
    padding: 36rpx 43rpx;
    font-size: 28rpx;
  }
}
