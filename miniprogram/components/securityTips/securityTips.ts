// components/securityTips/securityTips.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    visible: false,
    content:
      '如招聘单位在招聘过程中向求职者提出收取押金、保证金、体检费、材料费、成本费，或指定医院体检等，求职者有权要求招聘单位出具物价部门批准的收费许可证明材料，若无法提供相关证明，请求职者提高警惕，有可能属于诈骗或违规行为。',
    title: '*重要风险提示'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  openTips(event: any) {
    const type = event.currentTarget.dataset.type

    this.setData({
      visible: true,
      title: type === 'risk' ? '*重要风险提示' : '*责任声明',
      content:
        type === 'risk'
          ? '如招聘单位在招聘过程中向求职者提出收取押金、保证金、体检费、材料费、成本费，或指定医院体检等，求职者有权要求招聘单位出具物价部门批准的收费许可证明材料，若无法提供相关证明，请求职者提高警惕，有可能属于诈骗或违规行为。'
          : '声明：本站部分公告与职位内容由本站根据官方招聘公告进行整理编辑。由于用人单位需求专业、学历学位、资格条件、职位编制、备注内容等内容情况复杂且有变化可能，是否符合招聘条件以用人单位公告为准或请联系用人单位确认。本站整理编辑的职位信息仅供求职者参考，如因此造成的损失本站不承担任何责任！'
    })
  },

  onVisibleChange(e: any) {
    this.setData({
      visible: e.detail.visible
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
