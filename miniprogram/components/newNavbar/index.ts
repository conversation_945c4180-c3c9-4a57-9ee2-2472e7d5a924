import { assetsURL } from '@/settings'
import { checkLogin } from '@/utils/store'
import { checkMemberComplete } from '@/api/apply'
import { checkNewMessage } from '@/api/home'

Component({
  options: {
    styleIsolation: 'shared'
  },
  /**
   * 组件的属性列表
   */
  properties: {
    scrollTop: {
      type: Number,
      value: 0
    },
    type: {
      type: String,
      value: '1'
    },
    title: {
      type: String,
      value: ''
    },
    visible: {
      type: Boolean,
      value: true
    },
    leftArrow: {
      type: Boolean,
      value: true
    },
    placeholder: {
      type: String,
      value: '请输入'
    }
  },

  lifetimes: {
    attached() {
      // const headerStyle = `--td-navbar-height: ${36}px`
      // this.setData({ headerStyle })
      this.updateChatMessageCount()
      this.setData({
        hasPrePage: this.hasPreviousPage(),
        isLogin: checkLogin()
      })
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // moreImage: `/icon/addDeskTop/more.png`,
    // 是否登录
    isLogin: false,
    loginDialogVisible: false,
    navVisible: false,
    hasPrePage: false,
    chatMessageCount: 0,
    headerStyle: getApp().globalData.headerStyle,
    navList: [
      {
        icon: `${assetsURL}/icon/newNavbar/home.png`,
        text: '首页',
        type: 'home'
      },
      {
        icon: `${assetsURL}/icon/newNavbar/search.png`,
        text: '搜索',
        type: 'search'
      },
      {
        icon: `${assetsURL}/icon/newNavbar/msg.png`,
        text: '聊天消息',
        type: 'msg'
      },
      {
        icon: `${assetsURL}/icon/newNavbar/online.png`,
        text: '在线简历',
        type: 'online'
      },
      {
        icon: `${assetsURL}/icon/newNavbar/chance.png`,
        text: '附近机会',
        type: 'chance'
      },
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    updateChatMessageCount() {
      const { chatMessageCount } = getApp()
      this.setData({ chatMessageCount })
    },

    toggleNav(event: WechatMiniprogram.CustomEvent) {
      // const isToggleDiv = event.target.id === 'toggleDiv'
      // if (!isToggleDiv) return
      this.setData({
        navVisible: !this.data.navVisible
      });
    },

    // 页面点击事件，判断是否点击在目标区域外
    onPageTap: function (event: WechatMiniprogram.CustomEvent) {
      const isToggleDiv = event.target.id === 'toggleDiv';
      // 如果没有点击到目标区域，隐藏 p 标签
      if (!isToggleDiv && this.data.navVisible) {
        this.setData({
          navVisible: false
        });
      }
    },

    onBack() {
      const { hasPrePage } = this.data
      if (hasPrePage) {
        wx.navigateBack()
      } else {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    },

    hasPreviousPage() {
      const pages = getCurrentPages();  // 获取当前页面栈
      if (pages.length > 1) {
        return true;
      } else {
        return false;
      }
    },

    async onJump(evt: WechatMiniprogram.CustomEvent) {
      // console.log(evt.currentTarget.dataset);
      let jumpType = evt.currentTarget.dataset.type
      const { type } = this.properties
      const { isLogin } = this.data
      switch (jumpType) {
        case 'home':
          wx.switchTab({
            url: '/pages/home/<USER>'
          })
          break;
        case 'search':
          wx.navigateTo({
            url: `/packages/search/index/index?searchType=${type}`
          })
          break;
        case 'msg':
          if (checkLogin()) {
            wx.switchTab({
              url: '/pages/chat/index'
            })
          } else {
            this.triggerEvent('clickNav', { tyee: 'msg' })
          }
          break;
        case 'online':
          if (checkLogin()) {
            // 看用户的简历是否完善
            const res = await checkMemberComplete({})
            const { resumeStepNum } = res
            if (resumeStepNum < 4) {
              wx.navigateTo({ url: '/packages/resume/required' })
              return
            }
            wx.navigateTo({
              url: '/packages/resume/index'
            })
          } else {
            this.triggerEvent('clickNav', { tyee: 'online' })
          }
          break;
        case 'chance':
          wx.switchTab({
            url: '/pages/region/index'
          })
          break;
        default:
          break;
      }
    }
  }
})
