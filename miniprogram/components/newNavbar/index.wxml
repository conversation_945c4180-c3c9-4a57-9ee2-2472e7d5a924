<t-navbar bindtap="onPageTap" title="{{ title }}" style="{{ headerStyle }}" t-class-title="navbar-title">
  <view slot="capsule" class="custom-capsule">
    <t-icon size="20" bind:tap="onBack" aria-role="button" aria-label="返回" name="chevron-left" class="custom-capsule__icon" />
    <view bind:tap="toggleNav" id="toggleDiv" class="home-icon custom-capsule__icon {{ chatMessageCount ? 'dot' : '' }}">
      <view wx:if="{{ navVisible }}" class="placeholder-bg" hover-class="none" hover-stop-propagation="false"> </view>
      <view wx:if="{{ navVisible }}" class="nav-con">
        <view class="nav-list" wx:for="{{ navList }}" wx:key="index" data-type="{{ item.type }}" bind:tap="onJump">
          <!-- {{item.icon}} -->
          <image class="icon" src="{{ item.icon }}" alt="" />
          <text class="{{ ['text', item.type === 'msg' && chatMessageCount ? 'dot' : ''] }}">{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</t-navbar>
<!-- 调用此组件务必在引用处添加id="new-nav-bar"，因为在chat-socket.ts文件需要调用其方法 -->
