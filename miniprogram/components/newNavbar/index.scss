@use 'styles/variables' as *;
.navbar-container {
  --td-spacer-1: 30rpx;
}

.home-icon {
  width: 30rpx;
  height: 30rpx;
  background: url(#{$assets}/icon/new-home.png) no-repeat center/contain;
  position: relative;
  &.dot {
    &:after {
      position: absolute;
      right: 20rpx;
      top: -6rpx;
      content: '';
      width: 12rpx;
      height: 12rpx;
      background: #fa635c;
      border-radius: 50%;
    }
  }
}

.navbar-primary {
  .navbar-logo {
    display: block;
    width: 208rpx;
    height: 52rpx;
    background: url('//img.gaoxiaojob.com/uploads/static/image/logo/mini_logo.png') no-repeat left/contain;
  }

  .t-navbar__content {
    background: url('//img.gaoxiaojob.com/uploads/mini/nav/primary.png') no-repeat center top/cover;
  }
}

.navbar-search {
  .search-content {
    background-color: #f7f7f7;
    margin-right: 20rpx;
    height: 64rpx;
    border-radius: 64rpx;
    display: flex;
    align-items: center;
    margin-left: 10rpx;

    .search-prefix {
      padding-right: 20rpx;
      padding-left: 30rpx;
      display: flex;
      align-items: center;

      .icon {
        margin-left: 12rpx;
      }
    }

    .t-input {
      --td-input-bg-color: transparent;
      --td-font-size-m: 28rpx;

      padding: 0 20rpx;
      height: 100%;
      flex-grow: 1;

      &::after {
        content: '';
        left: 0;
        width: 2rpx;
        height: 24rpx;
        background: #333;
        opacity: 0.4;
        top: 20rpx;
      }
    }
  }
}

.navbar-title {
  padding: 0 52rpx;
}

.custom-capsule {
  width: 100%;
  height: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 0 2rpx #ebebeb;
  background-color: $color-white;
  border-radius: var(--td-navbar-capsule-height, 32px);
}

.custom-capsule__icon {
  flex: 1;
  position: relative;
}

.custom-capsule__icon + .custom-capsule__icon:before {
  content: '';
  display: block;
  position: absolute;
  left: -1px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 36rpx;
  background: #e7e7e7;
}

// 我的
.placeholder-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 998;
}

.nav-con {
  position: absolute;
  top: 54rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  width: 215rpx;
  // height: 384rpx;
  padding: 10rpx 0;
  background: #ffffff;
  filter: drop-shadow(0rpx 4rpx 10rpx rgba(51, 51, 51, 0.14));
  border-radius: 12rpx;
  &:before {
    position: absolute;
    top: -20rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    content: '';
    border-left: 30rpx solid transparent;
    border-right: 30rpx solid transparent;
    border-bottom: 40rpx solid white; /* 设置三角形的颜色 */
  }
  .nav-list {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    .icon {
      width: 30rpx;
      height: 30rpx;
      margin-right: 12rpx;
    }
    .text {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      position: relative;
      &.dot {
        &:after {
          position: absolute;
          right: 0;
          top: 0;
          transform: translateX(100%);
          content: '';
          width: 12rpx;
          height: 12rpx;
          background: #fa635c;
          border-radius: 50%;
        }
      }
    }
  }
}
