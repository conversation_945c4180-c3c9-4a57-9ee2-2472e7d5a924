import { defaultDuration } from '../settings'
import { validEmpty } from './validate'
import { h5 } from '@/settings'

/**
 *
 * @param {string} [title] - 加载中
 */
export function showLoading(title = '加载中') {
  wx.showLoading({ title, mask: true })
}

/**
 *
 * @param {string} url - 网址
 */
export function toWebPage(url: string) {
  // https://test.m.gcjob.jugaocai.comhttps (为了避免双h5链接)
  const repeatUrl = h5 + 'http'

  if (url.includes(repeatUrl)) {
    // url 去掉 h5
    url = url.replace(h5, '')
  }
  wx.navigateTo({ url: `/pages/link/index?url=${encodeURIComponent(url)}` })
}

/**
 * 示例一
 * @param { sting } title
 * @param { sting } icon 'success' | 'none'
 * @param { number } duration 1500
 * @param { Function } callback
 *
 * 示例二
 * @param { object } Object Object
 */
export function showToast(...args: any) {
  const [option] = args
  const isObject = Object.prototype.toString.call(option) === '[object Object]'

  let callback = () => {}
  let time = defaultDuration
  if (isObject) {
    const { duration, callback: cb = () => {}, ...otherOptions } = option
    time = duration
    callback = cb
    wx.showToast({
      duration,
      ...otherOptions
    })
  } else {
    const [title = '', icon = 'none', duration = defaultDuration, cb = () => {}] = args
    callback = cb
    time = duration
    wx.showToast({
      title,
      icon,
      duration
    })
  }
  setTimeout(() => {
    callback()
  }, time)
}

/**
 * 设置data值
 * @param e
 * @param _this
 */
export function setData(e: any, _this: any) {
  const {
    detail,
    currentTarget: {
      dataset: { key: k }
    }
  } = e
  const keyMap = ['checked', 'value']
  const { length } = keyMap
  let v = ''
  for (let i = 0; i < length; i = i + 1) {
    const key = keyMap[i]
    const isInclude = Object.prototype.hasOwnProperty.call(detail, key)
    if (isInclude) {
      v = detail[key]
    }
  }
  _this.setData({
    [k]: v
  })
}

/**
 *
 * @param { object } formData 表单数据
 * @param { object } formRules 表单规则
 */
export function validateForm(formData: any, formRules: any) {
  let valid = true
  const ruleKeys = Object.keys(formRules)
  const { length } = ruleKeys
  cycle: for (let i = 0; i < length; i = i + 1) {
    const key = ruleKeys[i]
    const value = formData[key]

    const ruleItemArray: any = formRules[key]
    const { length: ruleLength } = ruleItemArray

    for (let j = 0; j < ruleLength; j = j + 1) {
      const empty = validEmpty(value)
      const { required, message, validator } = ruleItemArray[j]

      let isBreak = false
      if (required && empty) {
        showToast(message)
        isBreak = true
      }
      if (validator) {
        validator(value, (msg: any) => {
          if (msg) {
            showToast(msg)
            isBreak = true
          }
        })
      }

      if (isBreak) {
        valid = false
        break cycle
      }
    }
  }
  return valid
}

/**
 * 获取元素
 * @param e
 * @param _this
 */
export function getElClientRect(el: string) {
  return new Promise((resolve) => {
    const query = wx.createSelectorQuery()
    query.select(el).boundingClientRect()
    query.exec(([res]: any) => {
      resolve(res)
    })
  })
}
