import { showToast, toWebPage } from './util'

/**
 * 富文本链接点击处理
 * @param e mp-html的linktap事件对象
 * @returns 返回false阻止默认事件
 */
function handleHtmlLinkTap(e: any): boolean {
  const detail = e.detail

  if (!detail?.href) {
    return false
  }

  const href = detail.href

  // 检查是否为内部域名
  const isInternalLink = href.includes('gaoxiaojob.com') || href.includes('jugaocai.com')

  if (isInternalLink) {
    // 内部链接直接跳转
    // 避免没有加上https,替换http到https
    const newHref = href.replace('http://', 'https://')
    toWebPage(newHref)
  } else {
    // 外部链接复制到剪贴板
    wx.setClipboardData({
      data: href,
      success: () => {
        showToast({
          title: '链接已复制，请在浏览器中打开查看',
          icon: 'none',
          duration: 5000
        })
      }
    })
  }

  return false
}

/**
 * 页面混入对象，提供富文本链接处理方法
 * 使用方式：在页面的Page()配置中展开这个对象
 */
export const htmlLinkHandlerMixin = {
  /**
   * 富文本链接点击处理方法
   * 可以直接在WXML中使用 bind:linktap="handleHtmlTap"
   */
  handleHtmlTap(e: any) {
    return handleHtmlLinkTap(e)
  }
}
