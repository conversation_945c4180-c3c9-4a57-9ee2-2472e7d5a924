import { getAreaAreaInfo } from '@/utils/store'

export function handleTabBar(_this: any, data = {}) {
  if (typeof _this.getTabBar === 'function' && _this.getTabBar()) {
    switchTabBar(_this, data)
    switchArea(_this)
  }
}

export function switchTabBar(_this: any, data: any) {
  const { value } = data
  if (!value) return
  _this.getTabBar().setData({
    value
  })
  _this.getTabBar().updateChatMessageCount()
}

export function switchArea(_this: any) {
  const area = getAreaAreaInfo()
  const { list } = _this.getTabBar().data
  list[1].label = area.name
  _this.getTabBar().setData({
    list
  })
}
