import { baseURL, defaultDuration, timeout } from '../settings'
import { getAuthorization } from './store'
import { showLoading } from './util'

enum ResultValue {
  success = 1
}

export default function request(params: Miniprogram.RequestParams): any {
  const { url, data = {}, method = 'GET', loading } = params

  const isLoading = loading ?? method === 'POST'

  if (isLoading) {
    showLoading(typeof loading === 'string' ? loading : undefined)
  }

  return new Promise((resolve, reject) => {
    const { token = '' } = getAuthorization()
    wx.request({
      url: baseURL + url,
      data,
      method,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'Authorization-Token': token
      },
      timeout,

      success(response) {
        const { data, msg, result } = <Miniprogram.RequestResponse>response.data

        if (result === ResultValue.success) {
          resolve(data)
          if (isLoading) {
            wx.hideLoading()
          }
        } else {
          wx.showToast({
            title: msg,
            icon: 'none',
            duration: defaultDuration
          })

          reject(response)
        }
      },

      fail(error) {
        reject(error)
        if (isLoading) {
          wx.hideLoading()
        }
      },

      complete() {}
    })
  })
}
