// export function toChineseTime(time: number) {
//   // 处理时间为年月日

//   console.log(time)

//   var date = new Date(time * 1000)

//   // 将日期对象格式化为指定的字符串形式
//   var formattedDate = date.toLocaleDateString('zh-CN', {
//     year: 'numeric',
//     month: '2-digit',
//     day: '2-digit',
//     weekday: 'long'
//   })

//   // 拼接小时和分钟,需要完整的
//   // 获取小时和分钟，并使用 padStart() 方法确保输出始终包含两位数字
//   var hours = String(date.getHours()).padStart(2, '0')
//   var minutes = String(date.getMinutes()).padStart(2, '0')
//   formattedDate = formattedDate + hours + ':' + minutes

//   // 在11个字符串后加上(
//   // 在11个字符串后加上)
//   formattedDate = formattedDate.substring(0, 11) + '(' + formattedDate.substring(11)
//   formattedDate = formattedDate.substring(0, 15) + ')' + formattedDate.substring(15)

//   console.log(formattedDate)
//   return formattedDate
// }
export function toChineseTime(time: number) {
  // 处理时间为年月日
  const date = new Date(time * 1000) // 将时间戳转换为毫秒

  const year = date.getFullYear()
  const month = date.getMonth() + 1 // 月份从0开始，因此需要加1
  const day = date.getDate()
  const weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][date.getDay()] // 获取星期几的文本表示

  // 将小时和分钟补零到两位数
  const hours = ('0' + date.getHours()).slice(-2)
  const minutes = ('0' + date.getMinutes()).slice(-2)

  const formattedDate = year + '年' + month + '月' + day + '日' + '(' + weekday + ')' + hours + ':' + minutes

  return formattedDate
}
