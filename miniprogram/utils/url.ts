import { clickAdvert } from '@/api/person'
import { getChannelSphid } from '@/utils/store'
import { toWebPage } from './util'

export function jump(url: string, type: string, positionId = '') {
  // 根据不同的类型去做不同的跳转 跳转类型，1：小程序站内指定页面；2:小程序网页；3：小程序视频号；4:小程序小程序
  let compatibilityType = ''
  // 视频号的id
  let sphid = ''
  if (type) {
    compatibilityType = type + ''
  }
  // 访问一个图片链接
  if (positionId) {
    // 用户的token
    clickAdvert({
      id: positionId
    })
  }
  switch (compatibilityType) {
    // 这里写一下对应的点击量

    case '1':
      wx.navigateTo({
        url: url
      })
      break
    case '2':
      toWebPage(url)
      break
    case '3':
      // 视频号跳转
      sphid = getChannelSphid()
      if (url != '') {
        wx.openChannelsActivity({
          finderUserName: sphid,
          feedId: url
        })
      } else {
        // 跳转去视频号主页
        wx.openChannelsUserProfile({
          finderUserName: sphid
        })
      }

      break
    case '4':
      // 小程序跳转

      wx.navigateToMiniProgram({
        // 去掉两边空格
        shortLink: url.trim()
      })
      break
    default:
      wx.navigateTo({
        url: url
      })
      break
  }
}

// 场景码转obj 后端
// $scene = http_build_query(['id' => $id]);
export function sceneStringToObj(sceneJsonString: string) {
  try {
    const scene = decodeURIComponent(sceneJsonString)

    const sceneObj = JSON.parse('{"' + scene.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
      return key === '' ? value : decodeURIComponent(value)
    })

    return sceneObj
  } catch (error) {
    return {}
  }
}
