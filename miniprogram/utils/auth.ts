import { checkCodeLogin } from '@/api/entry'
import { resetStorage, setAuthorization, setUserInfo } from './store'

export async function wxLogin() {
  const result = await wx.login()
  return result.code
}

export async function checkLoginByCode(scene: string = '') {
  const code = await wxLogin()

  try {
    const data = await checkCodeLogin({ code, scene })
    if (Array.isArray(data)) {
      throw data
    }
    return data
  } catch (error) {
    throw error
  }
}

export function authSuccessCallback(data: any, callback: () => void) {
  const { Authorization, ...userInfo } = data

  resetStorage()

  setAuthorization(Authorization)
  setUserInfo(userInfo)
  getApp().socketConnect()

  callback && callback()
}
