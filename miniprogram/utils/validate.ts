/**
 * 校验是否空字符串、空数组、空对象等
 * @param { any } value
 * @returns { boolean }
 */
export function validEmpty(value: any) {
  if (value === '') return true //检验空字符串
  if (/^\s+$/.test(value)) return true //检验连续空格
  if (value === 'null') return true //检验字符串类型的null
  if (value === 'undefined') return true //检验字符串类型的 undefined
  if (!value && value !== 0 && value !== '') return true //检验 undefined 和 null
  if (Array.isArray(value) && value.length === 0) return true //检验空数组
  if (Object.prototype.toString.call(value) && Object.keys(value).length === 0) return true //检验空对象
  return false
}

/**
 *
 * @param {string} mobile
 * @returns {boolean}
 */
export function validMobile(mobile: string): boolean {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(mobile)
}

/**
 * 邮箱校验
 * @param {string} email - 邮箱地址
 * @returns {boolean}
 */
export function validEmail(email: string): boolean {
  const reg = /^[\w\-\\.]+@[\w\-\\.]+(\.\w+)+$/
  return reg.test(email)
}
