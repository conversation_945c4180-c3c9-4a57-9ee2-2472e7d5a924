import {
  getAnnouncementType as getAnnouncement<PERSON>ype<PERSON><PERSON>,
  getArrive as getArriveA<PERSON>,
  getCompanyNature as getCompanyNatureApi,
  getCompanyType as getCompanyTypeApi,
  getEducation as getEducationApi,
  getIntentionCity as getIntentionCityApi,
  getJobCategory as getJobCategoryApi,
  getMajor as getMajorApi,
  getNative as getNativeApi,
  getNature as getNatureApi,
  getPolitical as getPoliticalApi,
  getStatus as getStatusApi,
  getWage as getWageApi,
  getCommonShareInfo as getCommonShareInfoApi,
  getSearchParams as getSearchParamsApi,
  getAnnouncementSearchParams as getAnnouncementSearchParamsApi,
  getEmoji as getEmojiApi
} from '@/api/config'

import { env } from '@/settings'

export const setStorage = (key: string, value: any) => {
  wx.setStorageSync(`${env}-${key}`, value)
}

export const getStorage = (key: string) => {
  return wx.getStorageSync(`${env}-${key}`)
}

export const removeStorage = (key: string) => {
  wx.removeStorageSync(`${env}-${key}`)
}

const clearStorage = () => {
  wx.clearStorageSync()
}

// 视频号sphid
const channelSphidKey = 'channel.sphid'

// 全局地区信息
const globalAreaInfoKey = 'globalAreaInfo'

// 登录授权信息
const authorizationKey = 'authorization'

// 登录后的用户信息
const userInfoKey = 'userInfo'

// 地区页面地区信息
const areaAreaInfoKey = 'areaAreaInfo'

// 搜索中转页历史
const searchHistoryKey = 'searchHistory'

// 求职意向ID关键词
const intentionIdKey = 'intentionId'

/**
 * 这一堆都是在app加载的时候从接口获取好，直接存在缓存里面，后面直接就使用了
 */
const nativeCityAreaListKey = 'nativeCityAreaList'
const hierarchyCityListKey = 'hierarchyCityList'
const politicalStatusListKey = 'politicalStatusList'
const educationListKey = 'educationList'
const miniPersonMajorListKey = 'miniPersonMajorList'
const categoryJobListKey = 'categoryJobList'
const natureListKey = 'natureList'
const wageRangeListKey = 'wageRangeList'
const jobStatusListKey = 'jobStatusList'
const arriveDateListKey = 'arriveDateList'
const announcementTypeListKey = 'announcementTypeList'
const companyTypeListKey = 'companyTypeList'
const companyNatureListKey = 'companyNatureList'
const jobSearchParamsKey = 'jobSearchParams'
const announcementSearchParamsKey = 'announcementSearchParams'
const miniShareInfoKey = 'miniShareInfo'
const dictionaryTextKey = 'dictionaryText'
const miniIsShowDiscoverLiveKey = 'miniIsShowDiscoverLive'
const emoteListKey = 'emoteList'
const baseShowcaseKey = 'baseShowcase'

// 改变了求职意向的内容,如果有这个,首页就需要刷新了
const intentionChangedKey = 'intentionChanged'

export function getChannelSphid() {
  return 'sph58cVcjcJOJ8J'
}

export function setChannelSphid(sphid: string) {
  setStorage(channelSphidKey, sphid)
}

export function getGlobalAreaInfo() {
  return getStorage(globalAreaInfoKey)
}

export function setGlobalAreaInfo(areaInfo: any) {
  setStorage(globalAreaInfoKey, areaInfo)
  if (!getAreaAreaInfo()) {
    setAreaAreaInfo(areaInfo)
  }
}

export function getAuthorization() {
  return getStorage(authorizationKey)
}

export function setAuthorization(authorization: any) {
  setStorage(authorizationKey, authorization)
}

export function removeAuthorization() {
  removeStorage(authorizationKey)
}

export function getUserInfo() {
  return getStorage(userInfoKey) || {}
}

export function setUserInfo(userInfo: any) {
  setStorage(userInfoKey, userInfo)
}

// 获取地区页面地区信息
export function getAreaAreaInfo() {
  return getStorage(areaAreaInfoKey)
}

// 设置页面地区信息
export function setAreaAreaInfo(areaInfo: any) {
  setStorage(areaAreaInfoKey, areaInfo)
}

// 获取求职意向ID
export function getIntentionId() {
  return getStorage(intentionIdKey)
}

// 设置求职意向ID
export function setIntentionId(id: string) {
  setStorage(intentionIdKey, id)
}

// 获取搜索中转页历史
export function getSearchHistory() {
  return getStorage(searchHistoryKey) || { job: [], announcement: [], company: [] }
}

// 设置搜索中转页历史
export function setSearchHistory(history: any) {
  setStorage(searchHistoryKey, history)
}

// 求职意向改变
export function setIntentionChanged(value: boolean) {
  setStorage(intentionChangedKey, value)
}

// 求职意向改变
export function getIntentionChanged() {
  return getStorage(intentionChangedKey)
}

// 设置基础缓存（基本上是那些config) 从这里开始都是设置到最基本的那些配置，整个小程序启动的时候就设置好了，以后获取就用下面的方法去获取，不要直接用接口，那些方法如果缓存拿不到，是会自动补的
// ========= 基础缓存这里开始 =========
export function setBaseConfig(params: any) {
  // 递归
  Object.keys(params).forEach((key) => {
    setStorage(`${key}`, params[key])
  })
}

export async function getNative() {
  let list = getStorage(nativeCityAreaListKey)
  if (!list) {
    list = await getNativeApi()
    setStorage(nativeCityAreaListKey, list)
  }
  return list
}

export async function getIntentionCity() {
  let list = getStorage(hierarchyCityListKey)
  if (!list) {
    list = await getIntentionCityApi()
    setStorage(hierarchyCityListKey, list)
  }
  return list
}

export async function getPolitical() {
  let list = getStorage(politicalStatusListKey)
  if (!list) {
    list = await getPoliticalApi()
    setStorage(politicalStatusListKey, list)
  }
  return list
}

export async function getEducation() {
  let list = getStorage(educationListKey)
  if (!list) {
    list = await getEducationApi()
    setStorage(educationListKey, list)
  }
  return list
}

export async function getMajor() {
  let list = getStorage(miniPersonMajorListKey)
  if (!list) {
    list = await getMajorApi()
    setStorage(miniPersonMajorListKey, list)
  }
  return list
}

export async function getJobCategory() {
  let list = getStorage(categoryJobListKey)
  if (!list) {
    list = await getJobCategoryApi()
    setStorage(categoryJobListKey, list)
  }
  return list
}

export async function getNature() {
  let list = getStorage(natureListKey)
  if (!list) {
    list = await getNatureApi()
    setStorage(natureListKey, list)
  }
  return list
}

export async function getWage() {
  let list = getStorage(wageRangeListKey)
  if (!list) {
    list = await getWageApi()
    setStorage(wageRangeListKey, list)
  }
  return list
}

export async function getStatus() {
  let list = getStorage(jobStatusListKey)
  if (!list) {
    list = await getStatusApi()
    setStorage(jobStatusListKey, list)
  }
  return list
}

export async function getArrive() {
  let list = getStorage(arriveDateListKey)
  if (!list) {
    list = await getArriveApi()
    setStorage(arriveDateListKey, list)
  }
  return list
}

export async function getAnnouncementType() {
  let list = getStorage(announcementTypeListKey)
  if (!list) {
    list = await getAnnouncementTypeApi()
    setStorage(announcementTypeListKey, list)
  }
  return list
}

export async function getCompanyType() {
  let list = getStorage(companyTypeListKey)
  if (!list) {
    list = await getCompanyTypeApi()
    setStorage(companyTypeListKey, list)
  }
  return list
}

export async function getCompanyNature() {
  let list = getStorage(companyNatureListKey)
  if (!list) {
    list = await getCompanyNatureApi()
    setStorage(companyNatureListKey, list)
  }
  return list
}

export async function getSearchParams() {
  let params = getStorage(jobSearchParamsKey)
  if (!params) {
    params = await getSearchParamsApi()
    setStorage(jobSearchParamsKey, params)
  }
  return await getSearchParamsApi()
}

export async function getAnnouncementSearchParams() {
  let params = getStorage(announcementSearchParamsKey)
  if (!params) {
    params = await getAnnouncementSearchParamsApi()
    setStorage(announcementSearchParamsKey, params)
  }
  return params
}

export async function getCommonShareInfo() {
  let info = getStorage(miniShareInfoKey)
  if (!info) {
    info = await getCommonShareInfoApi()
    setStorage(miniShareInfoKey, info)
  }
  return info
}

export async function getMiniIsShowDiscoverLive() {
  let show = getStorage(miniIsShowDiscoverLiveKey)
  if (!show) {
    show = await getMiniIsShowDiscoverLive() // 假设有一个获取是否显示发现直播的API
    setStorage(miniIsShowDiscoverLiveKey, show)
  }
  return show
}

export async function getEmoji() {
  let list = getStorage(emoteListKey)
  if (!list) {
    list = await getEmojiApi() // 假设有一个获取表情列表的API
    setStorage(emoteListKey, list)
  }
  return list
}

export async function getBaseShowcase() {
  let showcase = getStorage(baseShowcaseKey)
  if (!showcase) {
    showcase = await getBaseShowcase() // 假设有一个获取基础展示的API
    setStorage(baseShowcaseKey, showcase)
  }
  return showcase
}

// ========= 基础缓存这里结束 =========

export function resetStorage() {
  const channel = getChannelSphid()
  const intentionId = getIntentionId()
  const globalAreaInfo = getGlobalAreaInfo()
  const areaInfo = getAreaAreaInfo()
  const searchHistory = getSearchHistory()
  const intentionChanged = getIntentionChanged()

  clearStorage()

  channel && setChannelSphid(channel)
  intentionId && setIntentionId(intentionId)
  globalAreaInfo && setGlobalAreaInfo(globalAreaInfo)
  areaInfo && setAreaAreaInfo(areaInfo)
  searchHistory && setSearchHistory(searchHistory)
  intentionChanged && setIntentionChanged(intentionChanged)
}

// 检查用户是否已经登录了
export function checkLogin() {
  const { expireTime } = getAuthorization()
  if (!expireTime) {
    return false
  }
  const now = new Date().getTime()
  return now < expireTime
}
