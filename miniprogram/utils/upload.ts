import { baseURL, defaultDuration } from '@/settings'
import { getAuthorization } from '@/utils/store'
import { showLoading } from './util'

const uploadFile = (option: WechatMiniprogram.UploadFileOption) => {
  const { token = '' } = getAuthorization()

  return new Promise((resolve, reject) => {
    wx.uploadFile({
      ...option,
      url: `${baseURL}${option.url}`,
      header: {
        'Content-Type': 'multipart/form-data',
        'Authorization-Token': token
      },
      success: (res: any) => {
        resolve(JSON.parse(res.data))
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

const uploadFileCallback = async (
  fetchList: ReturnType<typeof uploadFile>[],
  success?: Miniprogram.UploadSuccessCallback,
  failure?: Miniprogram.UploadFailureCallback
) => {
  showLoading('上传中')

  const result = (await Promise.allSettled(fetchList)).map((res: any) => res.value)

  const error: any[] = result.filter((item: any) => item.result === 0)

  wx.hideLoading({
    complete: () => {
      if (error.length) {
        wx.showToast({ title: error[0].msg || '上传失败', icon: 'none', duration: defaultDuration })
        failure && failure(error)
      } else {
        success && success(result)
      }
    }
  })
}

const chooseMessageFile = (option: WechatMiniprogram.ChooseMessageFileOption) => {
  return new Promise((resolve) => {
    wx.chooseMessageFile({
      ...option,
      success: (res) => {
        resolve(res.tempFiles)
      }
    })
  })
}

const chooseMedia = (option: WechatMiniprogram.ChooseMediaOption) => {
  return new Promise((resolve) => {
    wx.chooseMedia({
      ...option,
      success: (res) => {
        resolve(res.tempFiles)
      }
    })
  })
}

async function handleUploadImage(
  option: Miniprogram.UploadImageOption,
  success?: Miniprogram.UploadSuccessCallback,
  failure?: Miniprogram.UploadFailureCallback
) {
  const { url, count, mediaType = ['image'], formData = {} } = option
  const tempFiles = (await chooseMedia({ count, mediaType })) as WechatMiniprogram.MediaFile[]

  const fetchList = tempFiles.map((file: WechatMiniprogram.MediaFile) => {
    const { tempFilePath } = file

    return uploadFile({ url, name: 'file', filePath: tempFilePath, formData })
  })

  await uploadFileCallback(fetchList, success, failure)
}

async function handleUploadMessageFile(
  option: Miniprogram.UploadFileOption,
  success?: Miniprogram.UploadSuccessCallback,
  failure?: Miniprogram.UploadFailureCallback
) {
  const { url, type = 'all', count, extension, formData = {} } = option

  const params: WechatMiniprogram.ChooseMessageFileOption = { type, count }

  if (extension) {
    params['extension'] = extension
  }

  const tempFiles = (await chooseMessageFile(params)) as WechatMiniprogram.ChooseFile[]

  const fetchList = tempFiles.map((file: WechatMiniprogram.ChooseFile) => {
    const { name, path } = file

    return uploadFile({ url, name: 'file', filePath: path, formData: { name, ...formData } })
  })

  await uploadFileCallback(fetchList, success, failure)
}

export { handleUploadImage, handleUploadMessageFile }
