// 把一个一维数组转换成特定的二维数组,每个里面包含n个元素
export function arrayToMatrix(array: [], n: number) {
  const result = []
  for (let i = 0; i < array.length; i += n) {
    result.push(array.slice(i, i + n))
  }
  return result
}

// 写一个方法，给数组前面加上不限然后给公共搜索框用的
export function addUnlimited(array: [], txt = '不限') {
  //     // 不再需要了，让前端处理
  //  [
  //     'k'        => '',
  //     'v'        => '不限',
  //     'children' => [
  //         [
  //             'v' => '不限',
  //             'k' => '',
  //         ],
  //     ],
  // ];
  const limitArray = [
    {
      k: '',
      v: txt,
      children: [
        {
          v: txt,
          k: ''
        }
      ]
    }
  ]

  return limitArray.concat(array)
}
