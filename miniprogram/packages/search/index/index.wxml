<view class="search-container">
  <searchType model="{{ urlParams }}" bind:change="onChange" bind:search="search" bind:updateLocation="updateLocation" />

  <view hidden="{{ searchType != 1 }}" class="search-content">
    <view class="search-history" wx:if="{{ jobHistory.length }}">
      <view class="history-top">
        <view class="history-label">历史</view>
        <view class="history-clear" bind:tap="removeAllHistory">清除</view>
      </view>
      <view class="history-content">
        <view wx:for="{{ jobHistory }}" wx:key="key" class="history-list" bind:tap="navigateHistoryToResult"
          data-key="jobType" bind:longpress="removeHistory" wx:for-index="index" data-index="{{ index }}"
          data-title="{{ item.title }}">{{ item.title }}</view>
      </view>
    </view>

    <view class="search-job" wx:if="{{ subscribeList.length }}">
      <view class="job-top">
        <view class="job-label">搜索订阅职位</view>
        <view class="job-clear" bind:tap="handleJobEdit">编辑</view>
      </view>
      <scroll-view class="scroll" enhanced show-scrollbar="{{ false }}" scroll-x="{{ true }}">
        <view class="job-content">
          <view wx:for="{{ subscribeList }}" wx:key="key" class="job-list" bind:tap="navigateHistoryToResult"
            data-key="intentionJobType" data-jobType="{{item.jobType}}" data-areaIds="{{item.areaIds}}" data-educationIds="{{item.educationIds}}" wx:for-index="index" data-index="{{ index }}"
            data-title="{{ item.jobName }}">{{ item.jobName }}</view>
        </view>
      </scroll-view>
    </view>

    <view class="search-recommend" wx:if="{{ jobRecommend.length }}">
      <view class="recommend-top">
        <view class="recommend-label">推荐</view>
      </view>
      <view class="recommend-content">
        <view wx:for="{{ jobRecommend }}" wx:key="key" class="recommend-list" bind:tap="navigateToResult"
          data-index="{{ index }}" data-item="{{ item }}"> {{ item.title }}</view>
      </view>
    </view>

    <view class="hot" wx:if="{{ jobHot.length }}">
      <view class="hot-top job">热搜职位</view>
      <view class="hot-content">
        <view class="hot-list {{ index < 5 ? 'hot-icon' : '' }}" hover-class="none" wx:for="{{ jobHot }}" wx:key="key"
          bind:tap="navigateToResult" data-item="{{ item }}" data-index="{{ index }}">
          <i class="number">{{ index + 1 }}</i>
          <view class="name"> {{ item.title }} </view>
        </view>
      </view>
    </view>
  </view>

  <view hidden="{{ searchType != 2 }}" class="search-content">
    <view class="search-history" wx:if="{{ noticeHistory.length }}">
      <view class="history-top">
        <view class="history-label">历史</view>
        <view class="history-clear" bind:tap="removeAllHistory">清除</view>
      </view>
      <view class="history-content">
        <view wx:for="{{ noticeHistory }}" wx:key="key" class="history-list" bind:tap="navigateHistoryToResult"
          data-key="areaId" bind:longpress="removeHistory" data-index="{{ index }}" data-title="{{ item.title }}">
          {{ item.title }}</view>
      </view>
    </view>

    <view class="search-recommend" wx:if="{{ noticeRecommend.length }}">
      <view class="recommend-top">
        <view class="recommend-label">推荐</view>
      </view>
      <view class="recommend-content">
        <view wx:for="{{ noticeRecommend }}" wx:key="key" class="recommend-list" bind:tap="navigateToResult"
          data-index="{{ index }}" data-item="{{ item }}"> {{ item.title }}</view>
      </view>
    </view>

    <view class="hot" wx:if="{{ noticeHot.length }}">
      <view class="hot-top announcement">热搜公告</view>
      <view class="hot-content">
        <view class="hot-list {{ index < 5 ? 'hot-icon' : '' }}" hover-class="none" wx:for="{{ noticeHot }}"
          wx:key="key" bind:tap="navigateToResult" data-item="{{ item }}" data-index="{{ index }}">
          <i class="number">{{ index + 1 }}</i>
          <view class="name"> {{ item.title }} </view>
        </view>
      </view>
    </view>
  </view>

  <view hidden="{{ searchType != 3 }}" class="search-content">
    <view class="search-history" wx:if="{{ companyHistory.length }}">
      <view class="history-top">
        <view class="history-label">历史</view>
        <view class="history-clear" bind:tap="removeAllHistory">清除</view>
      </view>
      <view class="history-content">
        <view wx:for="{{ companyHistory }}" wx:key="key" class="history-list" bind:tap="navigateHistoryToResult"
          data-key="areaId" bind:longpress="removeHistory" data-index="{{ index }}" data-title="{{ item.title }}">
          {{ item.title }}</view>
      </view>
    </view>

    <view class="search-recommend" wx:if="{{ companyRecommend.length }}">
      <view class="recommend-top">
        <view class="recommend-label">推荐</view>
      </view>
      <view class="recommend-content">
        <view wx:for="{{ companyRecommend }}" wx:key="key" class="recommend-list" bind:tap="navigateToResult"
          data-index="{{ index }}" data-item="{{ item }}"> {{ item.title }}</view>
      </view>
    </view>

    <view class="hot" wx:if="{{ companyHot.length }}">
      <view class="hot-top company">热搜单位</view>
      <view class="hot-content">
        <view class="hot-list {{ index < 5 ? 'hot-icon' : '' }}" hover-class="none" wx:for="{{ companyHot }}"
          wx:key="key" bind:tap="navigateToResult" data-item="{{ item }}" data-index="{{ index }}">
          <i class="number">{{ index + 1 }}</i>
          <view class="name"> {{ item.title }} </view>
        </view>
      </view>
    </view>
  </view>
</view>
