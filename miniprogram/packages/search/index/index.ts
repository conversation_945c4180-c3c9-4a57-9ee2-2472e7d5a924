import { getHomeShowcase, getAllHistory, deleteHistory, deleteAllHistory, addAllHistory } from '../api/search'
import { jump } from '@/utils/url'
import { checkLogin, getSearchHistory, setSearchHistory } from '@/utils/store'
import { getStorage, setStorage } from '@/utils/store'

Page({
  /**
   * 页面的初始数据
   */
  data: <any>{
    isLogin: false,
    searchType: 1,
    urlParams: {},

    type: {
      jobType: '',
      areaId: ''
    },

    jobHistory: [],
    jobRecommend: [],
    jobHot: [],

    noticeHistory: [],
    noticeRecommend: [],
    noticeHot: [],

    companyHistory: [],
    companyRecommend: [],
    companyHot: [],

    subscribeList: [
      // {
      //   "jobCategoryIds": "42,31",
      //   "areaIds": "1999,1988,1965",
      //   "educationIds": "4,3",
      //   "jobType": "31",
      //   "jobName": "学科带头人/学术骨干"
      // },
      // {
      //   "jobCategoryIds": "42,31",
      //   "areaIds": "1999,1988,1965",
      //   "educationIds": "4,3",
      //   "jobType": "42",
      //   "jobName": "中小学校长/校领导/单位负责人"
      // },
      // {
      //   "jobCategoryIds": "42,31",
      //   "areaIds": "1999,1988,1965",
      //   "educationIds": "4,3",
      //   "jobType": "43",
      //   "jobName": "中小学校长/校领导/单位负责人"
      // }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getShowcase()
    this.handleParams(options)
  },

  handleParams(options: any) {
    const { jobType = '', areaId = '' } = options
    this.setData({
      urlParams: options,
      'type.jobType': jobType,
      'type.areaId': areaId
    })
    if (!areaId) {
      this.getSearchArea()
    }
  },

  checkLogin() {
    const isLogin = checkLogin()
    this.setData({ isLogin })
    this.addAllHistory()
  },

  addAllHistory() {
    const { isLogin } = this.data
    if (!isLogin) return

    const { job, announcement, company } = getSearchHistory()
    const list = [...job, ...announcement, ...company].map((item) => {
      const { title, type, addTime } = item
      return { title, type, addTime }
    })
    addAllHistory({ list: JSON.stringify(list) })
  },

  getShowcase() {
    getHomeShowcase().then((res: any) => {
      this.setData({
        jobRecommend: res.job.recommend,
        noticeRecommend: res.announcement.recommend,
        companyRecommend: res.company.recommend,
        jobHot: res.job.hot,
        noticeHot: res.announcement.hot,
        companyHot: res.company.hot
      })
    })
  },

  removeHistory(e: any) {
    const _this = this
    const { isLogin } = this.data
    const {
      target: {
        dataset: { index }
      }
    } = e
    const { searchType } = this.data
    const keyMap = <any>{
      type_1: 'jobHistory',
      type_2: 'noticeHistory',
      type_3: 'companyHistory'
    }
    const key = keyMap[`type_${searchType}`]
    const list = this.data[key]
    wx.showModal({
      title: '提示',
      content: '是否删除该记录',
      success: async (res) => {
        // 这里有两种情况,第一种是用户未登录
        if (res.confirm) {
          if (isLogin) {
            const { id } = list[index]
            await deleteHistory(id).then(() => {
              list.splice(index, 1)
            })
          } else {
            list.splice(index, 1)
          }
          _this.setData({ [key]: list }, () => {
            _this.updateSearchStorage()
          })
        }
      }
    })
  },

  removeAllHistory() {
    const _this = this
    const { searchType, isLogin } = this.data
    const keyMap = <any>{
      '1': 'jobHistory',
      '2': 'noticeHistory',
      '3': 'companyHistory'
    }
    const key = keyMap[`${searchType}`]
    wx.showModal({
      title: '提示',
      content: '是否删除全部记录',
      success: async (res) => {
        // 这里有两种情况,第一种是用户未登录
        if (res.confirm) {
          if (isLogin) {
            await deleteAllHistory(searchType)
          }
          _this.setData({ [key]: [] }, () => {
            _this.updateSearchStorage()
          })
        }
      }
    })
  },

  handleJobEdit() {
    wx.navigateTo({ url: '/packages/person/subscribe' })
  },

  navigateToResult(e: any) {
    const item = e.currentTarget.dataset.item
    jump(item.url, item.targetLinkType, item.id)
  },

  navigateHistoryToResult(e: any) {
    const { searchType } = this.data
    const { title, key, jobtype, areaids, educationids } = e.currentTarget.dataset

    const value = this.data.type[key]

    let query = ``
    const params = `&jobType=${jobtype}&areaId=${areaids}&educationType=${educationids}`
    if (key === 'intentionJobType') {
      // 意向职位
      query = `?searchType=${searchType}${params}`
    } else if (key === 'jobType') {
      query = `?searchType=${searchType}&keyword=${title}&${key}=${value}&areaId=${this.data.type.areaId}`
    } else {
      query = `?searchType=${searchType}&keyword=${title}&${key}=${value}`
    }
    
    wx.navigateTo({
      url: `/packages/search/result/index${query}`
    })
  },

  async getAllHistory() {
    const { isLogin } = this.data
    const { job, announcement, company } = getSearchHistory()

    let zhiweiList = [],
      gonggaoList = [],
      danweiList = []

    if (isLogin) {
      const resp = await getAllHistory()
      const { zhiwei, gonggao, danwei, subscribeList = [] } = resp
      zhiweiList = zhiwei.list
      gonggaoList = gonggao.list
      danweiList = danwei.list
      this.setData({
        subscribeList: subscribeList.length ? subscribeList : []
      })
    }

    this.setData(
      {
        jobHistory: zhiweiList.length ? zhiweiList : job,
        noticeHistory: gonggaoList.length ? gonggaoList : announcement,
        companyHistory: danweiList.length ? danweiList : company
      },
      () => {
        this.updateSearchStorage()
      }
    )
  },

  // 更新搜索历史记录缓存
  updateSearchStorage() {
    const { jobHistory, noticeHistory, companyHistory } = this.data
    setSearchHistory({
      job: jobHistory,
      announcement: noticeHistory,
      company: companyHistory
    })
  },

  updateLocation({ detail }: any ) {
    const { areaId } = detail
    this.setData({'type.areaId': areaId})
  },

  onChange({ detail }: any) {
    const { type } = detail
    this.setData({
      searchType: type
    })
  },

  getSearchArea() {
    const searchAreaIds = getStorage('searchAreaIds') || {};
    const { areaIds } = searchAreaIds;
    this.setData({'type.areaId': areaIds})
    return areaIds;
  },

  search({ detail }: WechatMiniprogram.CustomEvent) {
    const { searchType } = this.data
    const areaIds = this.getSearchArea();
    const keys = Object.keys(detail)
    let query = `?`
    keys.forEach((key: string) => {
      query = query + `${key}=${detail[key]}&`
      // 点击搜索记录也要带职位类型或地区过去
      switch (key) {
        case 'jobType':
          this.setData({ 'type.jobType': detail[key] })
          break
        case 'areaId':
          this.setData({ 'type.areaId': detail[key] })
          break
        default:
          break
      }
    })

    if (detail.areaId.length < 1) {
      areaIds && (query = query + `areaId=${areaIds}&`);
    }
    
    const { length } = query
    query = query.substring(0, length - 1)

    wx.navigateTo({
      url: `/packages/search/result/index${query}&searchType=${searchType}`
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkLogin()
    this.getAllHistory()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
