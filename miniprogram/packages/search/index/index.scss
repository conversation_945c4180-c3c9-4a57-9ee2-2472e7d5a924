.search-container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .search-content {
    padding: 0 30rpx 60rpx;
    flex-grow: 1;
    overflow: scroll;
  }

  .search-history {
    .history-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 0 20rpx;

      .history-label {
        font-size: 28rpx;
        font-weight: bold;
      }

      .history-clear {
        color: var(--font-color-tips);
        padding-left: 36rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/delete.png') no-repeat left/28rpx;
      }
    }

    .history-content {
      display: flex;
      flex-wrap: wrap;

      .history-list {
        font-size: 24rpx;
        line-height: 36rpx;
        padding: 6rpx 24rpx;
        background-color: #f7f7f7;
        border-radius: 8rpx;
        margin: 0 10rpx 10rpx 0;
      }
    }
  }

  .search-job {
    .job-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 0 20rpx;

      .job-label {
        padding-left: 43rpx;
        font-size: 28rpx;
        font-weight: bold;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/subscribe.png') no-repeat left/28rpx;
      }

      .job-clear {
        color: var(--font-color-tips);
        padding-left: 36rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/job-edit.png') no-repeat left/28rpx;
      }
    }
    .job-content {
      display: flex;
      // flex-wrap: wrap;
      white-space: nowrap;
      .job-list {
        font-size: 24rpx;
        line-height: 36rpx;
        padding: 6rpx 24rpx;
        background-color: #f7f7f7;
        border-radius: 8rpx;
        margin: 0 10rpx 10rpx 0;
      }
    }
  }

  .search-recommend {
    .recommend-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 0 20rpx;

      .recommend-label {
        font-size: 28rpx;
        font-weight: bold;
      }
    }

    .recommend-content {
      display: flex;
      flex-wrap: wrap;

      .recommend-list {
        line-height: 36rpx;
        padding: 6rpx 24rpx;
        background-color: #f7f7f7;
        border-radius: 8rpx;
        margin: 0 10rpx 10rpx 0;
        font-size: 24rpx;
      }
    }
  }

  .hot {
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-top: 40rpx;
    box-shadow: 0rpx 6rpx 38rpx 2rpx rgba(51, 51, 51, 0.08);

    .hot-top {
      font-size: 32rpx;
      font-weight: bold;
      padding-left: 88rpx;
      line-height: 82rpx;

      &.job {
        background:
          url('//img.gaoxiaojob.com/uploads/mini/icon/job.png') no-repeat 30rpx center/40rpx,
          linear-gradient(180deg, #ffefd2, #ffffff);
      }

      &.company {
        background:
          url('//img.gaoxiaojob.com/uploads/mini/icon/company.png') no-repeat 30rpx center/40rpx,
          linear-gradient(180deg, #ffefd2, #ffffff);
      }

      &.announcement {
        background:
          url('//img.gaoxiaojob.com/uploads/mini/icon/announcement.png') no-repeat 30rpx center/40rpx,
          linear-gradient(180deg, #ffefd2, #ffffff);
      }
    }

    .hot-content {
      .hot-list {
        padding: 11rpx 30rpx;
        display: flex;
        align-items: center;

        .name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }

        &.hot-icon {
          &::after {
            content: 'HOT!';
            font-size: 24rpx;
            font-weight: bold;
            color: var(--color-primary);
            font-style: italic;
            margin-left: 8rpx;
            flex-shrink: 0;
          }
        }

        .number {
          flex-shrink: 0;
          width: 30rpx;
          height: 30rpx;
          color: var(--color-primary);
          border-radius: 4rpx;
          text-align: center;
          line-height: 30rpx;
          margin-right: 20rpx;
          font-weight: bold;
        }

        &:first-child {
          // 1.fa5e56    2.f86f29    3.ff945c    4.faaf28    5.ffc151
          .number {
            color: #fff;
            background-color: #fa5e56;
          }
        }
        &:nth-child(2) {
          .number {
            color: #fff;
            background: #f86f29;
          }
        }
        &:nth-child(3) {
          .number {
            color: #fff;
            background: #ff945c;
          }
        }
        &:nth-child(4) {
          .number {
            color: #fff;
            background: #faaf28;
          }
        }
        &:nth-child(5) {
          .number {
            color: #fff;
            background: #ffc151;
          }
        }
      }
    }
  }
}
