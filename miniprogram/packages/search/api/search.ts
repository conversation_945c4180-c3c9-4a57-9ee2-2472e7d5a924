import request from '@/utils/request'

// 获取简历完成到第几步
export function getResumeStepNum() {
  return request({
    url: '/resume/get-step-num'
  })
}

// 获取搜索中转页的广告位
export function getHomeShowcase() {
  return request({
    url: '/search/middle-showcase'
  })
}

// 获取搜索中转页中的搜索历史
export function getAllHistory() {
  return request({
    url: '/search/get-resume-search-log'
  })
}

// 添加搜索记录（批量）
export function addAllHistory(data: any) {
  return request({
    url: '/search/add-search-list-log',
    method: 'POST',
    data
  })
}

// 添加搜索记录（单个）
export function addHistory(data: any) {
  return request({
    url: '/search/add-search-log',
    method: 'POST',
    data
  })
}

// 删除单个搜索记录
export function deleteHistory(id: number | string) {
  return request({
    url: '/search/delete-search-log',
    method: 'POST',
    data: { id }
  })
}

// 删除全部搜索记录
export function deleteAllHistory(type: number | string) {
  return request({
    url: '/search/delete-all-search-log',
    method: 'POST',
    data: { type }
  })
}

// 职位列表
export function getJob(data: object) {
  return request({
    url: '/job/get-list',
    method: 'GET',
    data
  })
}

// 公告列表
export function getAnnouncement(data: object) {
  return request({
    url: '/announcement/search-for-announcement-list',
    method: 'GET',
    data
  })
}

// 单位列表
export function getCompany(data: object) {
  return request({
    url: '/company/get-list',
    method: 'GET',
    data
  })
}

// 推荐单位列表
export function getRecommendCompany(data: object) {
  return request({
    url: '/company/get-recommend-list',
    method: 'GET',
    data
  })
}

// 是否职位订阅
export function getJobSubscribeInfo() {
  return request({ url: '/job-subscribe/get-info' })
}
