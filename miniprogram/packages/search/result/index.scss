@use 'styles/variables' as *;

.result-container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .flex-grow {
    flex-grow: 1;
    overflow: hidden;
  }

  .scroll-panel {
    height: 100%;
    background-color: var(--page-background);

    .result-data {
      padding: 20rpx 30rpx 30rpx;
      .subscribe-guide {
        width: 690rpx;
        height: 136rpx;
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: url(#{$assets}/icon/announcement-guide-bg.png) no-repeat center/100%;
        .left {
          padding-left: 30rpx;
          .tit {
            display: flex;
            font-weight: bold;
            font-size: 28rpx;
            color: #333333;
            text {
              display: inline-block;
              max-width: 220rpx;
              color: #FFA000;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .desc {
            margin-top: 16rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            opacity: 0.8;
          }
        }
        .right {
          display: flex;
          align-items: center;
          .btn {
            width: 124rpx;
            height: 56rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #FFA000;
            border-radius: 28rpx;
            font-weight: bold;
            font-size: 28rpx;
            color: #FFFEFE;
          }
          .close {
            width: 60rpx;
            height: 60rpx;
            background: url(#{$assets}/icon/close.png) no-repeat center/20rpx;
          }
        }
      }

      .item {
        background-color: #fff;
        border-radius: var(--border-radius);
        padding: 20rpx 30rpx 24rpx;
        margin: 0 0 20rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .ending-content {
      padding: 40rpx 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--font-color-basic);
      font-size: 24rpx;

      &::after,
      &::before {
        content: '';
        width: 127rpx;
        height: 2rpx;
        margin: 0 14rpx;
        background: #ebebeb;
      }
    }

    .empty-content {
      padding-top: 150rpx;
    }

    .empty-company {
      display: flex;
      align-items: center;
      padding: 10rpx 30rpx;
      color: var(--font-color-basic);

      &::before,
      &::after {
        content: '';
        flex-grow: 1;
        height: 2rpx;
        background-color: #ebebeb;
      }
    }
  }

  .open-login-dialog {
    padding: 30rpx 0 60rpx;

    .login-tips {
      color: $color-primary;
      text-align: center;
      margin-bottom: 34rpx;
    }

    .login-btn {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      background-color: $color-primary;
      color: $color-white;
      text-align: center;
      font-size: 30rpx;
      width: 480rpx;
    }
  }

  .loading {
    padding-top: 40rpx;
  }
}
