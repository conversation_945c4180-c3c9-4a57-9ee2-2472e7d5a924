import { throttle } from 'throttle-debounce'
import { showLoading, getElClientRect } from '@/utils/util'
import { getJob, getAnnouncement, getCompany, getRecommendCompany, getJobSubscribeInfo } from '../api/search'
import { checkLogin, getStorage, setStorage } from '@/utils/store'
import { checkMemberComplete } from '@/api/apply'

let _this = <any>null
Page({
  /**
   * 页面的初始数据
   */
  data: <any>{
    isLogin: false,
    // scroll-view高度
    remainHeight: 0,
    isCompleteRequire: false,
    isFirstComplete: false,
    loginDialogVisible: false,
    urlParams: {},
    searchType: 1,

    scrollTop: 0,

    page: 1,
    params: {},

    // 是否首次加载
    first: {
      job: true,
      announcement: true,
      company: true
    },

    // 是否完成所有数据加载
    finish: {
      job: false,
      announcement: false,
      company: false,
      recommendCompany: false
    },

    refresherEnd: false,

    isRequest: false,

    jobList: [],
    announcementList: [],
    companyList: [],

    recommendCompany: [],

    showBackTop: false,

    subscribeGuideVisible: false,

    isSubscribe: 0, // >0 代表已订阅
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    _this = this
    const { searchType = 1 } = options
    this.setData({ urlParams: options, searchType })
    this.getRemainHeight()
    this.checkSubscribe()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({ isLogin: checkLogin() })
    this.hasShowSubscribeGuide()
  },

  async getRemainHeight() {
    const rect: any = await getElClientRect('.flex-grow')
    this.setData({ remainHeight: rect.height })
  },

  async getList(isInit = false) {
    const { searchType, params, page } = this.data

    const requestMap = <any>{
      type_1: {
        api: getJob,
        listKey: 'jobList',
        finishKey: 'finish.job',
        firstKey: 'job'
      },
      type_2: {
        api: getAnnouncement,
        listKey: 'announcementList',
        finishKey: 'finish.announcement',
        firstKey: 'announcement'
      },
      type_3: {
        api: getCompany,
        listKey: 'companyList',
        finishKey: 'finish.company',
        firstKey: 'company'
      }
    }
    const request = requestMap[`type_${searchType}`]
    const { api, listKey, finishKey, firstKey } = request

    const isHideLoading = this.data.first[firstKey]

    if (!isHideLoading) {
      showLoading()
    }

    const query: any = {}
    Object.keys(params).forEach((key: string) => {
      const value = params[key]
      const isArray = Array.isArray(value)
      query[key] = isArray ? value.join() : value
    })

    if (isInit) {
      this.setData({
        [finishKey]: false,
        isFirstComplete: false
      })
      await api({ ...query, page }).then((resp: any) => {
        const { list } = resp
        const { length } = list
        this.setData({
          [listKey]: list
        })
        this.handleFinish(finishKey, length)
        if (searchType == 3 && !length) {
          this.getRecommendCompany(true)
        }
      })
    } else {
      await api({ ...query, page }).then((resp: any) => {
        const { list } = resp
        const { length } = list
        const oldList = this.data[listKey]
        this.setData({
          [listKey]: oldList.concat(list)
        })
        this.handleFinish(finishKey, length)
      })
    }
    this.setData({
      isRequest: false,
      isFirstComplete: true,
      isCompleteRequire: true,
      [`first.${firstKey}`]: false
    })

    if (!isHideLoading) {
      wx.hideLoading()
    }
  },

  async getRecommendCompany(isInit = false) {
    showLoading()
    this.setData({ isRequest: true })

    const { page } = this.data
    const { list } = await getRecommendCompany({ page })
    let newList = []
    if (isInit) {
      newList = list
    } else {
      const { recommendCompany } = this.data
      newList = recommendCompany.concat(list)
    }
    this.setData({ recommendCompany: newList, isRequest: false })
    this.handleFinish('finish.recommendCompany', list.length)
    wx.hideLoading()
  },

  handleFinish(key: string, length: number) {
    const row = 20
    if (length < row) {
      this.setData({ [key]: true })
    }
  },

  onChange({ detail }: any) {
    const { type } = detail
    this.setData({
      searchType: type
    })
  },

  search({ detail }: WechatMiniprogram.CustomEvent) {
    this.setData(
      {
        params: detail,
        page: 1,
        scrollTop: 0,
        showBackTop: false
      },
      () => {
        this.getList(true)
      }
    )
  },

  scrolltolower(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { finishKey }
      }
    } = e
    const { finish, isRequest, isLogin, page, companyList, searchType } = this.data
    const isFinish = finish[finishKey]

    if (!isLogin || isRequest) return

    if (companyList.length === 0 && !finish['recommendCompany'] && searchType == 3) {
      this.setData(
        {
          page: page + 1,
          isRequest: true
        },
        () => {
          this.getRecommendCompany()
        }
      )
    }

    if (isFinish) return

    this.setData(
      {
        page: page + 1,
        isRequest: true
      },
      () => {
        this.getList()
      }
    )
  },

  async handleRefresh() {
    this.setData({ page: 1 })
    await this.getList(true)
    this.setData({ refresherEnd: false })
  },

  openLoginDialog() {
    this.setData({ loginDialogVisible: true })
  },

  loginSuccess() {
    this.setData({ isLogin: checkLogin(), scrollTop: 0 })
  },

  setBacktop: throttle(300, ({ scrollTop }) => {
    const { remainHeight, showBackTop } = _this.data
    const isShow = scrollTop > remainHeight
    if (isShow !== showBackTop) {
      _this.setData({
        showBackTop: isShow
      })
    }
  }),

  backTop() {
    this.setData({
      scrollTop: 0
    })
  },

  async checkSubscribe() {
    const { jobSubscribeId } = await getJobSubscribeInfo()
    this.setData({ isSubscribe: jobSubscribeId})
  },

  async handleToSubscribe() {
    const { isLogin, params } = this.data
    const query: any = {}
    Object.keys(params).forEach((key: string) => {
      const value = params[key]
      const isArray = Array.isArray(value)
      query[key] = isArray ? value.join() : value
    })
    
    if (isLogin) {
      // 看用户的简历是否完善
      const res = await checkMemberComplete({})
      const { resumeStepNum } = res
      if (resumeStepNum < 4) {
        wx.navigateTo({ url: '/packages/resume/required' })
        return
      }
      wx.navigateTo({ url: `/packages/person/subscribe?&formType=serachResult&jobType=${query.jobType}&areaId=${query.areaId}&backupAreaLabel=${query.backupAreaLabel}&backupJobLabel=${query.backupJobLabel}`})
    } else {
      this.openLoginDialog()
    }
  },

  hasShowSubscribeGuide() {
    const storedDateStr = getStorage('subscribeGuideDate');
    const subscribeGuideStr = getStorage('subscribeGuideVisible');
    if (storedDateStr) {
      const storedDate:any = new Date(storedDateStr);
      const currentDate:any = new Date();  // 获取当前日期
      // 计算当前时间与缓存时间的差值（单位：毫秒）
      const timeDifference = currentDate - storedDate;
      // 将毫秒差值转换为天数
      const daysDifference = timeDifference / (1000 * 3600 * 24);
      // 判断是否超过7天
      if (daysDifference >= 7 && !subscribeGuideStr) {
        this.setData({ subscribeGuideVisible: true })
      }
    } else {
      this.setData({ subscribeGuideVisible: true })
    }
  },
  
  handleCloseSubscribeGuide() {
    this.setData({ subscribeGuideVisible: false })
    const currentDate = new Date();
    const dateStr = currentDate.toLocaleDateString();  // 格式化为 "yyyy/MM/dd"
    setStorage('subscribeGuideDate', dateStr);
  },

  onScroll(e: any) {
    const {
      currentTarget: { offsetTop },
      detail: { scrollTop }
    } = e
    this.setBacktop({ offsetTop, scrollTop })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
