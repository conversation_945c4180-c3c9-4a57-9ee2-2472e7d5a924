<view class="result-container">
  <searchType model="{{ urlParams }}" update="{{ isLogin }}" bind:change="onChange" bind:search="search" pageType="{{ 2 }}" />

  <view class="flex-grow">
    <scroll-view style="height: {{ remainHeight + 'px' }}" hidden="{{ searchType != 1 }}" class="scroll-panel" scroll-y scroll-top="{{ scrollTop }}" lower-threshold="50" refresher-enabled refresher-triggered="{{ refresherEnd }}" bindrefresherrefresh="handleRefresh" bindscrolltolower="scrolltolower" data-param-key="jobParams" data-finish-key="job" bindscroll="onScroll" scroll-with-animation>
      <loading wx:if="{{ first.job }}" visible c-class="loading"></loading>

      <view class="result-data">
        <view wx:if="{{ params.backupJobLabel[0] && subscribeGuideVisible && (!isLogin || !isSubscribe) }}" class="subscribe-guide">
          <view class="left">
            <view class="tit">
              对<text>{{ params.backupAreaLabel[0] && params.backupAreaLabel[0] + '-' }}{{ params.backupJobLabel[0] }}</text>相关内容感兴趣
            </view>
            <view class="desc">
              一键订阅，定时为您推送最新职位信息
            </view>
          </view>
          <view class="right">
            <view bind:tap="handleToSubscribe" class="btn">
              去订阅
            </view>
            <view class="close" bind:tap="handleCloseSubscribeGuide"></view>
          </view>
        </view>
        <job-item welfare-tag="{{ false }}" showReleaseTime wx:for="{{ jobList }}" wx:for-item="item" wx:for-index="index" wx:key="key" detail="{{ item }}" isLogin="{{ isLogin }}" c-class="item" />
      </view>

      <view wx:if="{{ jobList.length && finish.job && isLogin }}" class="ending-content">到底啦~</view>

      <empty wx:if="{{ !jobList.length && !first.job }}" c-class="empty-content" description="暂未搜到更多职位，修改搜索条件再试试" />

      <view wx:if="{{ !isLogin && jobList.length >= 20 }}" class="open-login-dialog">
        <view class="login-tips">更多优选职位，登录查看</view>
        <t-button hover-class="none" class="login-btn" bind:tap="openLoginDialog" block theme="primary">立即登录</t-button>
      </view>
    </scroll-view>

    <scroll-view style="height: {{ remainHeight + 'px' }}" hidden="{{ searchType != 2 }}" class="scroll-panel" scroll-y scroll-top="{{ scrollTop }}" lower-threshold="50" refresher-enabled refresher-triggered="{{ refresherEnd }}" bindrefresherrefresh="handleRefresh" bindscrolltolower="scrolltolower" data-param-key="announcementParams" data-finish-key="announcement" bindscroll="onScroll" scroll-with-animation>
      <loading wx:if="{{ first.announcement }}" visible c-class="loading"></loading>

      <view class="result-data">
        <announcement-item wx:for="{{ announcementList }}" wx:for-item="item" wx:for-index="index" wx:key="key" detail="{{ item }}" c-class="item" />
      </view>

      <view wx:if="{{ announcementList.length && finish.announcement && isLogin }}" class="ending-content">到底啦~</view>

      <empty wx:if="{{ !announcementList.length && !first.announcement }}" c-class="empty-content" description="暂未搜到更多公告，修改搜索条件再试试" />

      <view wx:if="{{ !isLogin && announcementList.length >= 20 }}" class="open-login-dialog">
        <view class="login-tips">更多优选公告，登录查看</view>
        <t-button hover-class="none" class="login-btn" bind:tap="openLoginDialog" block theme="primary">立即登录</t-button>
      </view>
    </scroll-view>

    <scroll-view style="height: {{ remainHeight + 'px' }}" hidden="{{ searchType != 3 }}" class="scroll-panel" scroll-y scroll-top="{{ scrollTop }}" lower-threshold="50" refresher-enabled refresher-triggered="{{ refresherEnd }}" bindrefresherrefresh="handleRefresh" bindscrolltolower="scrolltolower" data-param-key="companyParams" data-finish-key="company" bindscroll="onScroll" scroll-with-animation>
      <loading wx:if="{{ first.company }}" visible c-class="loading"></loading>

      <view class="result-data">
        <company-item wx:for="{{ companyList }}" wx:for-item="item" wx:for-index="index" wx:key="key" detail="{{ item }}" c-class="item" />
      </view>

      <view wx:if="{{ companyList.length && finish.company && isLogin }}" class="ending-content">到底啦~</view>

      <view wx:if="{{ !companyList.length && !first.company }}" class="empty-company">暂未搜到更多单位，为您推荐以下单位</view>

      <view class="result-data" wx:if="{{ !companyList.length }}">
        <company-item wx:for="{{ recommendCompany }}" wx:for-item="item" wx:for-index="index" wx:key="key" detail="{{ item }}" c-class="item" />
      </view>

      <view wx:if="{{ recommendCompany.length && finish.recommendCompany && isLogin }}" class="ending-content">到底啦~</view>

      <view wx:if="{{ !isLogin && (companyList.length >= 20 || recommendCompany.length >= 20) }}" class="open-login-dialog">
        <view class="login-tips">更多优选单位，登录查看</view>
        <t-button hover-class="none" class="login-btn" bind:tap="openLoginDialog" block theme="primary">立即登录</t-button>
      </view>
    </scroll-view>
  </view>

  <block wx:if="{{ !isLogin }}">
    <login-dialog bind:loginSuccess="loginSuccess" visible="{{ loginDialogVisible }}" />
  </block>

  <view wx:if="{{ showBackTop }}" class="back-top-trigger" bind:tap="backTop"></view>
</view>
