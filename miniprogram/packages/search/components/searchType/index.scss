@use 'styles/variables' as *;

$wrapper: 'search-header';

.#{$wrapper} {
  --td-spacer-1: 30rpx;

  .#{$wrapper}-content {
    background-color: #f7f7f7;
    margin-right: 20rpx;
    height: 64rpx;
    border-radius: 64rpx;
    display: flex;
    align-items: center;
    margin-left: 10rpx;

    .content-prefix {
      padding-right: 18rpx;
      padding-left: 30rpx;
      display: flex;
      align-items: center;
      position: relative;

      .#{$wrapper}-label {
        max-width: 114rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .icon {
        margin-left: 10rpx;
      }

      &::after {
        content: '';
        position: absolute;
        right: 0;
        display: inline;
        width: 2rpx;
        height: 24rpx;
        background: #333;
        opacity: 0.4;
      }
    }

    .t-input {
      --td-input-bg-color: transparent;
      --td-font-size-m: 28rpx;

      padding: 0 20rpx;
      height: 100%;
      flex-grow: 1;

      .clearable-icon {
        color: #dedede;
        font-size: 32rpx;
      }
    }
  }
}

.search-tabs {
  display: flex;
  padding: 0 30rpx;
  border-bottom: 2rpx solid $border-color;

  .search-tab-item {
    padding: 18rpx 0;
    margin-right: 40rpx;

    &.active {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      font-weight: bold;
      font-size: 32rpx;

      &::after {
        position: absolute;
        bottom: 0;
        content: '';
        width: 40rpx;
        height: 6rpx;
        background: $color-primary;
        border-radius: 4rpx;
      }
    }
  }
}

.filter-content {
  height: 72rpx;
  padding: 0 30rpx;
}

.filter-fixed {
  z-index: 99;
  height: 100%;
  display: flex;
  box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(51, 51, 51, 0.04);
  align-items: center;

  .flex-1 {
    flex-grow: 1;
    display: flex;
    align-items: center;
  }
}

.filter-item {
  line-height: 72rpx;
  margin-right: 58rpx;
  max-width: 160rpx;
  // 因 overflow: hidden 会影响溢出的vip图标，而且现在没有文本过长限制的需求，故注释
  // overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;
  position: relative;
  padding-right: 20rpx;

  &.more {
    position: relative;
    &::before {
      content: "";
      width: 41rpx;
      height: 19rpx;
      top: 6rpx;
      right: -20rpx;
      position: absolute;
      background: url(//img.gaoxiaojob.com/uploads/mini/icon/vip-radius.png) no-repeat center/100% 100%;
    }
  }

  &.filter-group {
    margin-right: 0;
  }

  &::after {
    content: '';
    position: absolute;
    display: inline-block;
    width: 0rpx;
    height: 0rpx;
    right: 0;
    bottom: 22rpx;
    border-left: 5rpx solid transparent;
    border-right: 5rpx solid #c7c7c7;
    border-top: 5rpx solid transparent;
    border-bottom: 5rpx solid #c7c7c7;
    margin-left: 10rpx;
  }

  &.active {
    font-weight: bold;
    color: var(--color-primary);

    &::after {
      border-color: transparent var(--color-primary) var(--color-primary) transparent;
    }
  }
}
