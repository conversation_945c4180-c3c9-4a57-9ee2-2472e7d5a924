import { getResumeStepNum, addHistory } from '../../api/search'

import { checkLogin, getAreaAreaInfo, getSearchHistory, setSearchHistory } from '@/utils/store'
import { getAreaCurrent } from '@/api/region'
import { validEmpty } from '@/utils/validate'
import { getStorage, setStorage } from '@/utils/store'
import { getDictionaryLabel } from '@/api/config'

Component({
  options: {
    virtualHost: true,
    styleIsolation: 'shared'
  },
  externalClasses: ['c-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    model: {
      type: Object,
      value: {}
    },
    update: {
      type: Boolean,
      value: false
    },
    // 1搜索中转页，2搜索结果页
    pageType: {
      type: Number,
      value: 1
    }
  },

  observers: {
    model: function (value) {
      const { isInitEnd } = this.data
      if (isInitEnd) {
        const param = this.handleParam(value)
        this.updateParams(param)
      }
    },
    update: function (value) {
      if (value) {
        this.setData({ isLogin: value })
        this.getResumeStep()
      }
    },
    type: function () {
      const paramKey = this.getParamKey()
      this.setData({ paramKey })
    }
  },

  lifetimes: {
    attached() {
      this.setData({ isInitEnd: true })
    }
  },

  pageLifetimes: {
    show() {
      this.setData({
        isLogin: checkLogin()
      })
    }
  },

  /**
   * 组件的初始数据
   */
  data: <any>{
    limit: 5,
    headerStyle: getApp().globalData.headerStyle,

    isInitEnd: false,

    isCompleteResume: false,
    isLogin: false,

    visible: {
      jobType: false,
      education: false,
      area: false,
      major: false,
      groupType: false,

      companyType: false,

      companyNature: false
    },

    type: 1,

    areaLabel: '地区',
    backupAreaLabel: '',
    backupJobLabel: '',
    hasPickArea: 0, //0非不限，1不限
    jobLabel: {
      jobType: '职位类型',
      education: '学历',
      major: '学科',
      groupType: '更多'
    },
    announcementLabel: {
      type: '公告类型',
      education: '学历',
      major: '学科',
      groupType: '更多',
      companyType: '单位类型'
    },
    companyLabel: {
      education: '学历',
      companyType: '单位类型',
      companyNature: '单位性质'
    },

    key: '',
    paramKey: 'jobParams',
    // 多选类型
    multipleKey: ['areaId', 'companyType', 'companyNature', 'majorId', 'jobType'],

    keyword: '',
    areaId: '',
    placeholder: '搜职位',

    jobParams: {
      jobType: [],
      educationType: [],
      majorId: [],
      groupType: [],
      isEstablishment: '',
      applyHeat: ''
    },
    announcementParams: {
      type: [],
      educationType: [],
      majorId: [],
      groupType: [],
      companyType: [],
      isEstablishment: '',
      announcementHeat: ''
    },
    companyParams: {
      educationType: [],
      companyType: [],
      companyNature: []
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleParam(urlParam: any) {
      const { searchType = 1 } = urlParam
      const type = Number(searchType)
      let param: any = {}
      // 将除职位类型、地区、学科外，其他值处理成groupType
      if (type === 1) {
        const exceptMap = [
          'keyword',
          'searchType',
          'jobType',
          'areaId',
          'educationType',
          'majorId',
          'isEstablishment',
          'applyHeat',
          'groupType',
          'backupAreaLabel',
          'backupJobLabel',
          'hasPickArea'
        ]
        Object.keys(urlParam).forEach((key: string) => {
          const value = urlParam[key]
          if (!exceptMap.includes(key)) {
            const { groupType = [] } = param
            const values = value.split(',')
            const array: any = []
            values.forEach((value: string | number) => array.push(`${key}_${value}`))
            param['groupType'] = groupType.concat(array)
          } else {
            param[key] = value
          }
        })
      } else {
        param = urlParam
      }
      return param
    },

    updateParams(value: any) {
      const { searchType = 1, ...otherOptions } = value
      const type = Number(searchType)
      const { pageType, jobParams, announcementParams, companyParams, keyword, areaId, multipleKey } = this.data
      let paramsKey = ''
      let params = <any>{}
      switch (type) {
        case 1:
          paramsKey = 'jobParams'
          params = jobParams
          break
        case 2:
          paramsKey = 'announcementParams'
          params = announcementParams
          break
        case 3:
          paramsKey = 'companyParams'
          params = companyParams
          break
      }

      const query = <any>{}
      Object.keys(params).forEach((key) => {
        const value = otherOptions[key] || params[key]
        const isMultiple = multipleKey.includes(key)
        const isEmpty = validEmpty(value)
        if (!isEmpty) {
          if (isMultiple) {
            query[key] = value.split(',')
          } else {
            query[key] = value
          }
        } else {
          if (isMultiple) {
            query[key] = []
          } else {
            query[key] = ''
          }
        }
      })
      const areaIdStr = value.areaId || areaId
      const areaIdArr = areaIdStr ? areaIdStr.split(',') : []
      this.setData(
        {
          type,
          [paramsKey]: { ...query },
          keyword: otherOptions.keyword || keyword,
          areaId: areaIdArr,
          hasPickArea: otherOptions.hasPickArea - 0 || 0
        },
        async () => {
          // 不限值为空与空选择默认地区冲突，暂时注释-2023.7.4
          // await this.getArea()
          this.getSearchArea()
          await this.getResumeStep()
          if (pageType === 2) {
            this.onChange()
          }
        }
      )
      this.updatePlaceholder(type)
      this.triggerEvent('change', { type })
    },

    // 由于需求更改,所以不会再出现多个地区变成1个的逻辑
    updateArea() {
      // 完成简历前三步多选，否则单选
      // const { isCompleteResume } = this.data
      // const { areaId } = this.data
      // let id = ''
      // if (isCompleteResume) {
      //   id = areaId
      // } else {
      //   id = areaId.length !== 0 ? areaId[0] : ''
      // }
      // this.setData({ areaId: id })
    },

    async getArea() {
      const { areaId } = this.data
      const isAreaIdEmpty = validEmpty(areaId)
      if (!isAreaIdEmpty) return
      const storageAreaInfo = getAreaAreaInfo()
      const isStorageAreaEmpty = validEmpty(storageAreaInfo)
      const areaInfo = isStorageAreaEmpty ? await getAreaCurrent() : storageAreaInfo
      const { id, name } = areaInfo
      this.setData({ areaId: id, areaLabel: name })
    },

    async getSearchArea() {
      const { areaId, hasPickArea } = this.data
      const searchAreaIds = getStorage('searchAreaIds') || {};
      const { areaIds } = searchAreaIds;
      const areaCurrent = await getAreaCurrent()
      const res = areaId?.length ? areaId : areaIds?.length ? areaIds : areaCurrent.id
      const resStr = Array.isArray(res) ? res.join(',') : res;
      const txt = await getDictionaryLabel({ type: 101, value: resStr })
      this.setData({ areaId: hasPickArea && !areaId?.length ? [] : res, backupAreaLabel: (hasPickArea && !areaId?.length) ? '' : txt['textList'] })
      this.triggerEvent('updateLocation', { areaId: hasPickArea && !areaId?.length ? [] : res })
    },

    async setSearchArea(arr:any) {
      setStorage('searchAreaIds', { areaIds: arr })
    },

    async getResumeStep() {
      const { isLogin } = this.data
      if (isLogin) {
        // 获取当前简历步骤
        await getResumeStepNum().then((resp: any) => {
          const { step } = resp
          this.setData({ isCompleteResume: step === 4 })
        })
      }
      this.updateArea()
    },

    onTabs(e: WechatMiniprogram.CustomEvent) {
      const { pageType } = this.data
      const {
        currentTarget: {
          dataset: { type }
        }
      } = e
      this.updatePlaceholder(type)
      wx.nextTick(() => {
        if (pageType === 2) this.onChange()
      })
      this.triggerEvent('change', { type })
    },

    updatePlaceholder(type: number) {
      let placeholder = ''
      switch (type) {
        case 1:
          placeholder = '搜职位'
          break
        case 2:
          placeholder = '搜公告'
          break
        case 3:
          placeholder = '搜单位'
          break
        default:
          placeholder = '搜职位'
          break
      }
      this.setData({ type, placeholder })
    },

    handleShow(e: WechatMiniprogram.CustomEvent) {
      const {
        currentTarget: {
          dataset: { show: visibleKey, key }
        }
      } = e
      this.setData({ [visibleKey]: true, key })
    },

    getParamKey() {
      const { type } = this.data
      let paramKey = ''
      switch (type) {
        case 1:
          paramKey = 'jobParams'
          break
        case 2:
          paramKey = 'announcementParams'
          break
        case 3:
          paramKey = 'companyParams'
          break
      }
      return paramKey
    },

    pickerChange(e: WechatMiniprogram.CustomEvent) {
      const { paramKey, key } = this.data
      const {
        detail: { value = '', label = '' }
      } = e

      const valueKey = key === 'areaId' ? 'areaId' : `${paramKey}.${key}`

      this.setData({
        [valueKey]: value
      })

      // 拿到文案填充职位引导模块
      if (valueKey === 'areaId') {
        this.setData({
          backupAreaLabel: label,
          // 选择地区后，设为true，因为选择“不限”地区，也算是选择了地区
          hasPickArea: 1
        })
        
        this.setSearchArea(value)
      }

      // 拿到文案填充职位引导模块
      if (`${paramKey}.${key}` === 'jobParams.jobType') {
        this.setData({
          backupJobLabel: label
        })
      }
      

      wx.nextTick(() => {
        this.addSearchLog()
        this.onChange()
      })
    },

    pickerLabel({ detail }: WechatMiniprogram.CustomEvent) {
      const { labelKey, label, labelArray } = detail
      this.setData({ [labelKey]: label })
    },

    keywordInput(e: WechatMiniprogram.CustomEvent) {
      const {
        detail: { value }
      } = e
      this.setData({ keyword: value })
    },

    clearKeyword() {
      this.setData({ keyword: '' })
    },

    submit() {
      this.addSearchLog()
      this.onChange()
    },

    addSearchLog() {
      const { type, keyword, isLogin, pageType } = this.data
      if (validEmpty(keyword) || pageType == 2) return
      const time = Math.round(new Date().getTime() / 1000)

      let { job, announcement, company } = getSearchHistory()

      const findIndex = (kw: any, arr: any) => {
        return arr.findIndex((item: any) => item.title == kw)
      }
      let index = 0

      switch (type) {
        case 1:
          index = findIndex(keyword, job)
          if (index >= 0) {
            job.splice(index, 1)
          }
          job.unshift({ id: time, title: keyword, type, addTime: time })
          // 最多保存6个
          job = job.slice(0, 6)
          break
        case 2:
          index = findIndex(keyword, announcement)
          if (index >= 0) {
            announcement.splice(index, 1)
          }
          announcement.unshift({ id: time, title: keyword, type, addTime: time })
          announcement = announcement.slice(0, 6)
          break
        case 3:
          index = findIndex(keyword, company)
          if (index >= 0) {
            company.splice(index, 1)
          }
          company.unshift({ id: time, title: keyword, type, addTime: time })
          company = company.slice(0, 6)
          break
        default:
          break
      }

      if (isLogin) {
        addHistory({ type, title: keyword, addTime: time })
      }

      setSearchHistory({
        job,
        announcement,
        company
      })
    },

    onChange() {
      const { keyword, areaId, paramKey, backupAreaLabel, backupJobLabel, hasPickArea } = this.data
      const param = this.data[paramKey]
      this.triggerEvent('search', { keyword, areaId, backupAreaLabel,
    backupJobLabel, hasPickArea, ...param })
    },

    handleSuperior(e: WechatMiniprogram.CustomEvent) {
      const { detail } = e
      const { paramKey } = this.data
      let query = this.data[paramKey]
      query = {
        ...query,
        ...detail
      }
      this.setData({
        [paramKey]: query
      })

      wx.nextTick(() => {
        this.addSearchLog()
        this.onChange()
      })
    }
  }
})
