<t-navbar style="{{ headerStyle }}" left-arrow class="search-header">
  <view class="search-header-content" slot="left">
    <view class="content-prefix" data-show="visible.area" data-key="areaId" bind:tap="handleShow">
      <view class="search-header-label">
        {{ areaLabel }}
      </view>
      <t-icon class="icon" name="caret-down-small" size="24rpx" />
    </view>
    <t-input t-class-clearable="clearable-icon" value="{{ keyword }}" placeholder="{{ placeholder }}" clearable borderless confirm-type="search" bind:enter="submit" bind:change="keywordInput" bind:clear="clearKeyword"></t-input>
  </view>
</t-navbar>

<view class="search-tabs">
  <view class="search-tab-item {{ type == 1 ? 'active' : '' }}" data-type="{{ 1 }}" bind:tap="onTabs">搜职位</view>
  <view class="search-tab-item {{ type == 2 ? 'active' : '' }}" data-type="{{ 2 }}" bind:tap="onTabs">搜公告</view>
  <view class="search-tab-item {{ type == 3 ? 'active' : '' }}" data-type="{{ 3 }}" bind:tap="onTabs">搜单位</view>
</view>

<block wx:if="{{ pageType === 2 }}">
  <view class="filter-content">
    <view wx:if="{{ type == 1 }}" class="filter-fixed">
      <view class="flex-1">
        <view class="filter-item {{ jobParams.jobType.length ? 'active' : '' }}" bind:tap="handleShow" data-key="jobType" data-show="visible.jobType">
          {{ jobLabel.jobType }}
        </view>
        <view class="filter-item {{ jobParams.educationType.length ? 'active' : '' }}" bind:tap="handleShow" data-key="educationType" data-show="visible.education">
          {{ jobLabel.education }}
        </view>
        <view class="filter-item {{ jobParams.majorId.length ? 'active' : '' }}" bind:tap="handleShow" data-key="majorId" data-show="visible.major">
          {{ jobLabel.major }}
        </view>
        <!-- <superiorFilterPopup bind:change="handleSuperior" type="{{ 1 }}" value="{{ { isEstablishment: jobParams.isEstablishment, heat: jobParams.applyHeat } }}" /> -->
      </view>

      <view class="filter-item filter-group more {{ jobParams.groupType.length ? 'active' : '' }}" bind:tap="handleShow" data-key="groupType" data-show="visible.groupType">
        {{ jobLabel.groupType }}
      </view>
    </view>

    <view wx:if="{{ type == 2 }}" class="filter-fixed">
      <view class="flex-1">
        <view class="filter-item {{ announcementParams.type.length ? 'active' : '' }}" bind:tap="handleShow" data-key="type" data-show="visible.type">
          {{ announcementLabel.type }}
        </view>
        <view class="filter-item {{ announcementParams.educationType.length ? 'active' : '' }}" bind:tap="handleShow" data-key="educationType" data-show="visible.education">
          {{ announcementLabel.education }}
        </view>
        <view class="filter-item {{ announcementParams.majorId.length ? 'active' : '' }}" bind:tap="handleShow" data-key="majorId" data-show="visible.major">
          {{ announcementLabel.major }}
        </view>
        <!-- <view class="filter-item {{ announcementParams.companyType.length ? 'active' : '' }}" bind:tap="handleShow" data-key="companyType" data-show="visible.companyType">
          {{ announcementLabel.companyType }}
        </view> -->
        <!-- <superiorFilterPopup bind:change="handleSuperior" type="{{ 2 }}" value="{{ { isEstablishment: announcementParams.isEstablishment, heat: announcementParams.announcementHeat } }}" /> -->
      </view>
       <view class="filter-item filter-group more {{ announcementParams.groupType.length ? 'active' : '' }}" bind:tap="handleShow" data-key="groupType" data-show="visible.groupType">
        {{ announcementLabel.groupType }}
      </view>
    </view>

    <view wx:if="{{ type == 3 }}" class="filter-fixed">
      <view class="filter-item {{ companyParams.companyType.length ? 'active' : '' }}" bind:tap="handleShow" data-key="companyType" data-show="visible.companyType">
        {{ companyLabel.companyType }}
      </view>
      <view class="filter-item {{ companyParams.companyNature.length ? 'active' : '' }}" bind:tap="handleShow" data-key="companyNature" data-show="visible.companyNature">
        {{ companyLabel.companyNature }}
      </view>
    </view>
  </view>

  <!-- <picker-group-type model="{{ jobParams.groupType }}" visible="{{ visible.groupType }}" limit="{{ 12 }}" label-key="jobLabel.groupType" bind:updateLabel="pickerLabel" bind:change="pickerChange" /> -->


  <!-- 不同tab共用一个组件 start-->
   <picker-education model="{{ type === 1 ? jobParams.educationType : announcementParams.educationType }}" visible="{{ visible.education }}" label-key="{{ type === 1 ? 'jobLabel.education' : 'announcementLabel.education' }}" bind:updateLabel="pickerLabel" bindchange="pickerChange" limit="5" />

  <picker-major limit="{{ limit }}" unlimit model="{{ type === 1 ? jobParams.majorId : announcementParams.majorId }}" visible="{{ visible.major }}" label-key="{{ type == 1 ? 'jobLabel.major' : 'announcementLabel.major' }}" bind:updateLabel="pickerLabel" bind:change="pickerChange" />

  <picker-group-type defaultLabel="更多" type="{{ type }}" model="{{ type === 1 ? jobParams.groupType : announcementParams.groupType }}" visible="{{ visible.groupType }}" limit="{{ 12 }}" label-key="{{ type === 1 ? 'jobLabel.groupType' : 'announcementLabel.groupType' }}" bind:updateLabel="pickerLabel" bind:change="pickerChange" />
  <!-- 不同tab共用一个组件 end-->

  <picker-company-nature limit="{{ limit }}" unlimit model="{{ companyParams.companyNature }}" visible="{{ visible.companyNature }}" label-key="companyLabel.companyNature" bind:updateLabel="pickerLabel" bind:change="pickerChange" />
</block>

<picker-job-category model="{{ jobParams.jobType }}" limit="{{ limit }}" unlimit visible="{{ visible.jobType }}" label-key="jobLabel.jobType" bind:updateLabel="pickerLabel" bind:change="pickerChange" />
<picker-area model="{{ areaId }}" type="intention" unlimit limit="5" visible="{{ visible.area }}" isLogin="{{ isLogin }}" isCompleteResum="{{ isCompleteResume }}" label-key="areaLabel" bind:updateLabel="pickerLabel" bind:change="pickerChange" />

<picker-company-type limit="{{ limit }}" unlimit model="{{ companyParams.companyType }}" visible="{{ visible.companyType }}" label-key="{{ type == 2 ? 'announcementLabel.companyType' : 'companyLabel.companyType' }}" bind:updateLabel="pickerLabel" bind:change="pickerChange" />

<picker-announcement-type data-type="announcement-type" title="公告类型" visible="{{ visible.type }}" model="{{ announcementParams.type }}" label-key="announcementLabel.type"  bind:updateLabel="pickerLabel" bind:change="pickerChange" limit="{{ 5 }}" unlimit />
