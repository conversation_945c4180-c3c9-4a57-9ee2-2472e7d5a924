<!-- <t-navbar title="职位详情">
  <view slot="capsule" class="custom-capsule">
    <t-icon size="20" bind:tap="onBack" aria-role="button" aria-label="返回" name="chevron-left" class="custom-capsule__icon" />
    <view class="home-icon custom-capsule__icon" bind:tap="onGoHome"></view>
  </view>
</t-navbar> -->
<!-- 调用此组件务必在引用处添加id="new-nav-bar"，因为在chat-socket.ts文件需要调用其方法 -->
<new-nav-bar id="new-nav-bar" title="职位详情" type="1" bind:clickNav="checkLogin"></new-nav-bar>
<view class="job-detail-container">
  <view class="switch-bar detail-head">
    <view class="tag-cell" wx:if="{{ jobInfo.isTop === 1 || jobInfo.isEstablishment === 1 || jobInfo.isFast === 1 }}">
      <view class="help-wanted" wx:if="{{ jobInfo.isTop === 1 }}">急聘</view>
      <view class="organization" wx:if="{{ jobInfo.isEstablishment === 1 }}">编制</view>
      <view class="fast-feedback" wx:if="{{ jobInfo.isFast === 1 }}">反馈快</view>
    </view>
    <view class="job-title">
      <view class="job-name-title">{{ jobInfo.name }}</view>
      <view class="job-salary">{{ jobInfo.wage }}</view>
    </view>
    <view class="job-info">{{ jobInfo.jobBasicsInfo }}</view>
    <view class="time">
      <view class="date">截止日期：{{ jobInfo.periodDate }}</view>
      <view class="publish">{{ jobInfo.refreshTime }}发布 </view>
    </view>
    <navigator url="/packages/announcement/detail/index?id={{ jobInfo.announcementId }}" hover-class="none">
      <view class="association-announcement" wx:if="{{ jobInfo.announcementId }}">
        <view class="annoucement-name">{{ jobInfo.announcementTitle }}</view>
      </view>
    </navigator>
  </view>

  <view class="switch-bar contact-detail" wx:if="{{ jobInfo.isCooperation }}">
    <view class="{{['avatar', jobInfo.contactInfo.isOnline ? 'online-tag' : '']}}">
      <image src="{{ jobInfo.contactInfo.avatar }}" />
    </view>
    <view class="contact">
      <view class="hd" hover-class="none" hover-stop-propagation="false">
        <text class="name">{{ jobInfo.contactInfo.contact }}</text>
        <text class="tag">{{ jobInfo.contactInfo.activeName }}</text>
      </view>
      <view class="bd">{{ jobInfo.contactInfo.department }}</view>
    </view>
  </view>

  <view class="switch-bar detail-main">
    <view class="head">职位详情</view>
    <view class="welfare-box" wx:if="{{ jobInfo.welfareTagArr.length }}">
      <view class="welfare">
        <view class="tag" wx:for="{{ jobInfo.welfareTagArr }}" wx:key="index" wx:item="item">{{ item }}</view>
      </view>
    </view>

    <view class="info">基本信息</view>
    <view class="base">
      <view class="text" wx:if="{{ jobInfo.title }}">职称要求：{{ jobInfo.title }}</view>
      <view class="text is-specials" wx:if="{{ jobInfo.age }}">年龄要求：{{ jobInfo.age }}</view>
      <view class="text" wx:if="{{ jobInfo.political }}">政治面貌：{{ jobInfo.political }}</view>
      <view class="text" wx:if="{{ jobInfo.abroad }}">海外经历：{{ jobInfo.abroad }}</view>
      <view class="text" wx:if="{{ jobInfo.jobNature }}">工作性质：{{ jobInfo.jobNature }}</view>

      <view class="text is-special" wx:if="{{ jobInfo.department }}">用人部门：{{ jobInfo.department }}</view>
      <view class="text establishment" wx:if="{{ jobInfo.establishmentName }}" bindtap="handleEstablishment">
        <view class="title">职位编制</view>
        <view>：{{ jobInfo.establishmentName }}</view>
      </view>

      <view class="text" wx:if="{{ jobInfo.applyTypeText }}">报名方式 ：{{ jobInfo.applyTypeText }}</view>
      <view class="text row" wx:if="{{ jobInfo.majorTxt }}">需求专业(供参考)：{{ jobInfo.majorTxt }}</view>
    </view>

    <view class="message" wx:if="{{ jobInfo.duty }}">岗位职责</view>
    <view class="text-main" wx:if="{{ jobInfo.duty }}">
      <rich-text nodes="{{ jobInfo.duty }}"> </rich-text>
    </view>

    <view class="requirement" wx:if="{{ jobInfo.requirement }}">任职要求</view>
    <view class="text-main" wx:if="{{ jobInfo.requirement }}">
      <rich-text nodes="{{ jobInfo.requirement }}"></rich-text>
    </view>

    <view class="other" wx:if="{{ jobInfo.remark }}">其他说明</view>
    <view class="text-main" wx:if="{{ jobInfo.remark }}">
      <rich-text nodes="{{ jobInfo.remark }}"></rich-text>
    </view>
  </view>
  <view class="location">{{ jobInfo.address }}</view>

  <view class="switch-bar job-files" wx:if="{{ jobInfo.fileList.length > 0 }}">
    <view class="head">附件下载</view>
    <view class="file-list {{ item.suffix }}" wx:for="{{ jobInfo.fileList }}" wx:key="index" wx:item="item" data-url="{{ item.path }}" bind:tap="copyDownloadUrl"> {{ item.name }}</view>
  </view>

  <view class="competitiveness-analysis {{ showMatchData ? 'is-show' : '' }}">
    <view class="title">竞争力分析</view>

    <view class="description">
      您与该职位的匹配度:
      <block wx:if="{{ showMatchData }}">
        <text class="match">{{ markText }}</text
        ><block wx:if="{{ showMatchText }}"
          >，已超过了 <text class="percentage">{{ percentage }}%</text> 的竞争者</block
        >，{{ matchTips }}
      </block>

      <block wx:else> <text class="match">***</text>，已超过了 <text class="percentage">***</text> 的竞争者，建议<text class="tips">************</text> </block>
    </view>

    <view class="percentage-chart">
      <view wx:if="{{ showMatchData === false }}" class="tips">您在 ？位置</view>

      <view class="chart">
        <text class="item" wx:for="{{ chartList }}" wx:key="index">{{ item }}</text>
        <text wx:if="{{ showMatchData }}" class="mark" style="left: {{ 100 / 8 + (100 / 4) * (markIndex - 1) }}%;"></text>
      </view>
    </view>

    <view class="button">
      <text class="button-text" bindtap="handleReport">{{ showMatchData ? '查看' : '解锁' }}详细分析</text>
    </view>
  </view>

  <view class="switch-bar company-card" bind:tap="toCompany">
    <view class="logo-box {{ jobInfo.companyPackageType === 2 ? 'vip' : '' }}">
      <image class="company-logo" src="{{ jobInfo.logo }}" />
    </view>
    <view class="company-info">
      <view class="company-name">{{ jobInfo.companyName }}</view>
      <view class="company-message">
        <text wx:if="{{ jobInfo.nature }}">{{ jobInfo.nature }}</text>
        <text wx:if="{{ jobInfo.type }}">{{ jobInfo.type }}</text>
        <text wx:if="{{ jobInfo.scale }}">{{ jobInfo.scale }}</text>
      </view>
    </view>
  </view>

  <view class="switch-bar recommend-job" wx:if="{{ jobRecommend.list.length > 0 }}">
    <view class="head">推荐职位</view>

    <view class="swiper-content" style="{{ jobRecommend.height }}">
      <swiper interval="5000" autoplay duration="1000" circular="true" class="swiper" bindchange="jobRecommendChange">
        <swiper-item wx:for="{{ jobRecommend.list }}" wx:key="key" wx:for-item="list" class="swiper-item">
          <view wx:for="{{ list }}" wx:key="key" c-class="list" c-class-title="name" wx:for-item="item" class="card job-item">
            <navigator url="/packages/job/detail/index?id={{ item.id }}" hover-class="none">
              <view class="top">
                <view class="title">{{ item.name }}</view>
                <view class="salary">{{ item.wage }}</view>
              </view>
              <view class="announcement" wx:if="{{ item.announcement }}">{{ item.announcement }}</view>

              <view class="tag-content">
                <view class="tag" wx:if="{{ item.education }}">{{ item.education }}</view>
                <view class="tag" wx:if="{{ item.amount }}">招{{ item.amount }}人</view>
                <view class="tag" wx:if="{{ item.experience }}">{{ item.experience }}</view>
                <view class="tag" wx:if="{{ item.areaName }}">{{ item.areaName }}</view>
              </view>

              <view class="bottom">
                <view class="company">{{ item.companyName }}</view>
                <view class="time">{{ item.refreshTime }}</view>
              </view>
            </navigator>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <view class="swiper-paging" wx:if="{{ jobRecommend.list.length > 1 }}">
      <t-swiper-nav total="{{ jobRecommend.list.length }}" current="{{ jobRecommend.current }}" type="dots-bar"> </t-swiper-nav>
    </view>
  </view>
  <view wx:if="{{!hasPrevPage}}" class="more-box">
    <view bind:tap="jumpToSearch" class="more">
      <image class="logo-img" src="{{ moreSearchImage }}" />
      更多“<text>{{ moreSearchParams.noticeText }}</text>”职位
    </view>
  </view>
  <view class="tips tips-ts" style="padding-bottom: {{ !hasPrevPage ? guideHeigth : 0 }}px">
    <security-tips />
  </view>
</view>

<resume-perfect-tips visible="{{ resumePerfectVisible }}" />
<fake-guide class="fake-guide" bind:jump="jumpToSearch" visible="{{ showGuide }}" title="搜索更多职位" placeholder="搜索职位名称" moreSearchParams="{{ moreSearchParams }}"></fake-guide>

<view class="footer-fixed">
  <t-button hover-class="none" class="btn share" open-type="share"> 分享 </t-button>
  <view class="btn like {{ isCollect ? 'like-primary' : '' }}" bind:tap="collect">{{ isCollect ? '已收藏' : '收藏' }}</view>
  <view wx:if="{{ jobInfo.chatBtnAuth.isShow === 1 }}" class="btn chat" bindtap="handleChat">{{ jobInfo.chatBtnAuth.buttonName }}</view>

  <t-button wx:if="{{ !buttonDisabled }}" theme="primary" class="deliver" data-id="{{ jobId }}" bindtap="openDelivery"> 投递简历</t-button>
  <t-button wx:else disabled="{{ buttonDisabled }}" theme="primary" class="deliver">{{ buttonText }}</t-button>
</view>
<delivery-popup id="deliveryPopup" bindclose="getDetailApi" />

<t-dialog visible="{{ beforeApplyDialogVisible }}" title="{{ beforeApplyDialogTitle }}" content="{{ beforeApplyDialogContent }}" confirm-btn="{{ beforeApplyDialogConfirmBtn }}" cancel-btn="{{ beforeApplyDialogCancelBtn }}" bind:confirm="handleCheckDialogConfirm" bind:cancel="handleCheckDialogCancel" />
<t-popup visible="{{ establishmentVisible }}" placement="bottom" close-btn bind:visible-change="onVisibleChange">
  <view class="block">
    <view class="header">职位编制说明</view>
    <view class="prop-main">
      <view class="content">职位编制内容仅供参考！请以用人单位发布的公告&职位信息内容为准，或联系用人单位确认。</view>
    </view>
  </view>
</t-popup>
<login-dialog visible="{{ loginDialogVisible }}" bind:loginSuccess="fetchData" />

<t-dialog visible="{{ chatDialogVisible }}" title="{{ chatTitle }}" content="{{ chatContent }}" confirm-btn="{{ chatConfirmBtn }}" cancel-btn="{{ chatCancelBtn }}" bind:confirm="handleChatConfirm" bind:cancel="handleChatClose"></t-dialog>

<!-- 竞争力分析异常 -->
<t-dialog visible="{{ reportDialogVisible }}" title="{{ reportDialogTitle }}" confirm-btn="{{ reportConfirmButton }}" cancel-btn="{{ reportCancelButton }}" bind:confirm="handleReportConfirm" bind:cancel="handleReportCancel">
  <view slot="content">
    <view class="report-dialog-content">{{ reportDialogContent }}</view>
    <view wx:if="{{ reportDialogDescription }}" class="report-dialog-description">{{ reportDialogDescription }}</view>
  </view>
</t-dialog>
