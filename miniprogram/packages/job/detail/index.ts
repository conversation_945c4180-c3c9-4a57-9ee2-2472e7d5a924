import beforeDeliveryMixin from '@/mixins/beforeDeliveryMixin'

import { getDetail, collect as collectJob, checkJobReport, createJobReport } from '@/api/jobDetail'
import { checkLogin, getUserInfo } from '@/utils/store'
import { showLoading, showToast, getElClientRect, toWebPage } from '@/utils/util'
import { defaultDuration, h5, assetsURL } from '@/settings'
import { getResumeStepNum } from '@/api/person'
import { beforeCreateChatRoom } from '@/api/chat'
import { throttle } from 'throttle-debounce'
let _this = <any>null

// pages/jobDetail/index.ts
Page({
  /**
   * 页面的初始数据
   */

  behaviors: [beforeDeliveryMixin],

  deliveryPopup: <any>{},
  data: {
    jobId: '',
    jobInfo: <any>{},
    jobRecommend: {
      height: `--swiper-height:${270 * 3}rpx`,
      current: 0,
      list: []
    },
    resumePerfectVisible: false,
    chatDialogVisible: false,
    establishmentVisible: false,
    isLogin: false,
    isCollect: false,
    buttonDisabled: false,
    buttonText: '',
    deliveryInfo: <any>{},
    chatTitle: '',
    chatConfirmBtn: <any>{},
    chatCancelBtn: {},
    userInfo: {},

    showMatchData: false,
    showMatchText: true,
    matchTips: '',
    percentage: 0,
    markIndex: 4,
    markText: '非常匹配',
    chartList: ['较低', '一般', '比较匹配', '非常匹配'],
    reportUrl: '',

    reportDialogVisible: false,
    reportDialogTitle: '',
    reportDialogContent: '',
    reportDialogDescription: '',
    reportConfirmButton: <any>null,
    reportCancelButton: <any>null,
    reportConfirmCallback: () => {},

    // 底部搜索更多参数
    moreSearchParams: <any>{},
    moreSearchImage: `${assetsURL}/icon/common/more-search.png`,

    tipsTop: 0,
    guideHeigth: 0,
    showGuide: false,
    hasPrevPage: false
  },

  jumpToSearch() {
    // console.log(this.data.moreSearchParams);

    const { cityId, noticeId, educationId, cityText, noticeText, educationText } = this.data.moreSearchParams
    const params = `?searchType=1&keyword=&jobType=${noticeId}&areaId=${cityId}&educationType=${educationId}`
    wx.navigateTo({ url: `/packages/search/result/index${params}` })
  },

  async calculateGuideHeight() {
    const systemInfo = wx.getWindowInfo()
    const screenWidth = systemInfo.screenWidth
    const designWidth = 750
    const designHeight = 158

    // 根据屏幕宽度动态计算 guideHeigth 的值
    const guideHeigth = (screenWidth / designWidth) * designHeight

    this.setData({ guideHeigth })
  },

  jobRecommendChange(e: any) {
    const {
      detail: { current }
    } = e
    const { list } = this.data.jobRecommend
    const { length } = list[current]
    const height = `--swiper-height:${270 * length}rpx`
    this.setData({
      'jobRecommend.height': height,
      'jobRecommend.current': current
    })
  },

  async handleChat() {
    if (!this.checkLogin()) return
    const { title, tips1, tips2, cancelBtnTxt, confirmBtnTxt, chatId, jobId } = await beforeCreateChatRoom({
      jobId: this.data.jobId
    })

    if (chatId) {
      wx.navigateTo({ url: `/packages/chat/index/index?chatId=${chatId}&jobId=${jobId}` })
      return
    }

    const content = tips2 ? tips1 + tips2 : tips1
    const confirm = confirmBtnTxt ? { content: confirmBtnTxt, variant: 'base' } : null
    const cancel = { content: cancelBtnTxt, variant: 'base' }

    this.setData({
      chatDialogVisible: true,
      chatTitle: title,
      chatContent: content,
      chatConfirmBtn: confirm,
      chatCancelBtn: cancel
    })
  },

  handleChatClose() {
    this.setData({ chatDialogVisible: false })
  },

  handleChatConfirm() {
    wx.navigateTo({ url: '/packages/resume/required' })

    this.handleChatClose()
  },

  toCompany() {
    const companyId = this.data.jobInfo.companyId
    wx.navigateTo({
      url: '/packages/company/detail/index?id=' + companyId
    })
  },

  handleEstablishment() {
    this.setData({ establishmentVisible: true })
  },

  onVisibleChange(e: any) {
    this.setData({ establishmentVisible: e.detail.visible })
  },

  openDelivery(event: WechatMiniprogram.CustomEvent) {
    if (!this.checkLogin()) return

    this.setData({ jobId: event.currentTarget.dataset.id })

    this.handleCheckJobInfo(event.currentTarget.dataset.id)
  },

  openDeliveryPopup(data: any) {
    const { jobId } = this.data

    this.deliveryPopup.open({ ...data, jobId })
  },

  onBack() {
    wx.navigateBack()
  },

  onGoHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  handleReport() {
    if (!this.checkLogin()) return

    this.checkJobReport()
  },

  handleReportConfirm() {
    this.data.reportConfirmCallback()
    this.handleReportCancel()
  },

  handleReportCancel() {
    this.setData({ reportDialogVisible: false })
  },

  async checkJobReport(params: any = {}) {
    const { jobId } = this.data
    const { jumpType, jumpUrl, isConfirm, title, confirmBtnTxt, cancelBtnTxt, tips1, tips2 } = await checkJobReport({
      jobId,
      ...params
    })

    const reportConfirmButton = confirmBtnTxt ? { content: confirmBtnTxt, variant: 'base' } : null
    const reportCancelButton = cancelBtnTxt ? { content: cancelBtnTxt, variant: 'base' } : null

    const reportDialogData = {
      reportDialogVisible: true,
      reportDialogTitle: title,
      reportDialogContent: tips1,
      reportDialogDescription: tips2,
      reportConfirmButton,
      reportCancelButton
    }

    // 跳转类型，1:有弹窗带跳转链接,2:无弹窗带跳转链接,-1:有弹窗无跳转链接,9:可能是简历前三步未完善（🤷）

    if (jumpType === 1) {
      const callback =
        params.isConfirm || isConfirm === 0
          ? async () => {
              const { jumpUrl } = await createJobReport(jobId)

              toWebPage(`${h5}${jumpUrl}`)
            }
          : () => {
              this.checkJobReport({ isConfirm: '1' })
            }

      this.setData({
        ...reportDialogData,
        reportConfirmCallback: callback
      })
      return
    }

    if (jumpType === 2) {
      toWebPage(`${h5}${jumpUrl}`)
      return
    }

    if (jumpType === 9) {
      this.setData({
        ...reportDialogData,
        reportConfirmCallback: () => wx.navigateTo({ url: '/packages/resume/required' })
      })
      return
    }

    if (jumpType === -1) {
      this.setData({
        ...reportDialogData,
        reportConfirmCallback: () => {}
      })
      return
    }
  },

  async getTipsTop() {
    const El: any = await getElClientRect('.tips-ts')
    const { top } = El
    // console.log(El);
    this.setData({ tipsTop: top })
  },

  async getGuideHeigth() {
    const El1: any = await getElClientRect('.fake-guide')
    const El2: any = await getElClientRect('.footer-fixed')
    this.setData({ guideHeigth: El1.height - El2.height })
  },

  async getDetailApi() {
    showLoading()
    const jobId = this.data.jobId
    const jobDetailInfo = await (<any>getDetail({
      id: jobId
    }))

    const { hasRecord, url, matchType, matchTips, showExceed, jobMatchCompleteExceed } = jobDetailInfo.matchInfo

    const { city, cityId, education, educationType, jobCategory, jobCategoryId, jobName, majorId, majorTxt } =
      jobDetailInfo.info.moreSearchParams

    let obj = {
      hasNotice: true,
      hasEducation: true,
      hasMajor: false,
      noticeText: jobCategory,
      cityText: city,
      educationText: education,
      majorText: '专业',
      noticeId: jobCategoryId,
      educationId: educationType,
      majorId: majorId,
      cityId: cityId
    }

    this.setData(
      {
        jobInfo: jobDetailInfo.info,
        moreSearchParams: obj,
        'jobRecommend.list': jobDetailInfo.recommendList,
        isCollect: jobDetailInfo.info.collectStatus === 1 ? true : false,
        buttonDisabled: jobDetailInfo.info.status === 0 || jobDetailInfo.info.applyStatus === 1,
        buttonText: jobDetailInfo.info.status === 0 ? '已下线' : '已投递',

        showMatchData: hasRecord,
        showMatchText: hasRecord ? showExceed === 1 : true,
        percentage: jobMatchCompleteExceed,
        markIndex: matchType,
        matchTips,
        markText: this.data.chartList[matchType - 1],
        reportUrl: url
      },
      () => {
        // this.getTipsTop()
        // const observer = wx.createIntersectionObserver(this, {
        //   thresholds: [0] // 0 表示当元素完全进入可视区域时触发
        // });

        // observer.relativeToViewport() // 监听元素相对于视口的交叉情况
        //   .observe('.tips', (res) => {
        //     if (res.intersectionRatio > 0) {
        //       // 进入可视区域时触发的事件
        //       console.log('元素进入可视区域');
        //       this.setData({ showGuide: true });
        //     } else {
        //       this.setData({ showGuide: true });
        //     }
        //   });
        this.checkElementPosition() // 页面加载时检查一次
      }
    )
    wx.hideLoading()
  },

  // 收藏职位
  async collect() {
    const { isCollect, jobId } = this.data

    if (!this.checkLogin()) return

    await collectJob(jobId)
    if (isCollect) {
      showToast({ title: '已取消收藏', duration: defaultDuration })
    } else {
      showToast({ title: '收藏成功', duration: defaultDuration })
    }
    this.setData({ isCollect: !isCollect })
  },

  fetchData() {
    this.initData()
  },

  hasPreviousPage() {
    const pages = getCurrentPages() // 获取当前页面栈
    if (pages.length > 1) {
      return true
    } else {
      return false
    }
  },

  setGuide: throttle(5, ({ scrollTop }) => {
    // console.log(scrollTop, _this.data.tipsTop);
    const { tipsTop, hasPrevPage } = _this.data
    // console.log('hasPrevPage',getApp().globalData);
    // console.log(tipsTop);
    if (hasPrevPage) return
    if (scrollTop >= tipsTop) {
      _this.setData({ showGuide: true })
    } else {
      _this.setData({ showGuide: false })
    }
  }),

  checkElementPosition() {
    const { hasPrevPage, showGuide } = this.data
    if (hasPrevPage) return

    const query = wx.createSelectorQuery()
    query.select('.recommend-job').boundingClientRect()
    query.select('.tips-ts').boundingClientRect()
    query.exec((res) => {
      const windowHeight = wx.getWindowInfo().windowHeight
      const recommendRect = res[0]
      const tipsRect = res[1]

      if (recommendRect && recommendRect.top < windowHeight) {
        if (!showGuide) {
          this.setData({ showGuide: true })
        }
      } else if (tipsRect && tipsRect.top < windowHeight) {
        if (!showGuide) {
          this.setData({ showGuide: true })
        }
      } else {
        if (showGuide) {
          this.setData({ showGuide: false })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 有两种情况,一种是有场景值(也就是二维码扫码进来的)
    // 一种是没有场景值(也就是分享进来的)
    // 有场景值的时候,需要把场景值存到本地,然后在投递的时候带上
    // 没有场景值的时候,需要判断是否有本地存储的场景值,有的话就带上
    // 没有的话就不带
    _this = this
    if (options.scene) {
      const scene = decodeURIComponent(options.scene)
      // string转json
      const sceneObj = JSON.parse('{"' + scene.replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) => {
        return key === '' ? value : decodeURIComponent(value)
      })

      const { id } = sceneObj
      this.setData({ jobId: id })
    } else {
      this.setData({ jobId: options.id })
    }

    this.deliveryPopup = this.selectComponent('#deliveryPopup')

    this.setData({
      hasPrevPage: this.hasPreviousPage()
    })
  },
  onPageScroll(e: any) {
    // e.scrollTop 获取当前页面的滚动位置
    // console.log('当前滚动位置：', e.scrollTop);
    // this.setGuide({ scrollTop: e.scrollTop })
    this.checkElementPosition() // 页面滚动时检查元素位置
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // this.getGuideHeigth()
    this.calculateGuideHeight()
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    this.initData()
  },

  initData() {
    const isLogin = checkLogin()
    this.setData({ isLogin })

    const user = getUserInfo()
    this.setData({
      userInfo: user
    })
    if (isLogin) {
      // 设置一些信息
      getResumeStepNum().then((res: any) => {
        const resumePercent = res.resumeCompletePercent
        if (resumePercent < 75) {
          this.setData({
            resumePerfectVisible: true
          })
        } else {
          this.setData({
            resumePerfectVisible: false
          })
        }
      })
    }

    this.getDetailApi()
  },

  checkLogin() {
    const { isLogin } = this.data

    if (!isLogin) {
      this.setData({ loginDialogVisible: true })
    }
    return isLogin
  },

  copyDownloadUrl(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { url }
      }
    } = e
    wx.setClipboardData({
      data: url,
      success: () => {
        showToast({ title: '链接已复制，请在浏览器中打开查看', icon: 'none', duration: defaultDuration })
      },
      fail: () => {
        showToast(`复制失败`, 'error')
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // {单位名称} 正在招聘 {职位名称}
    const companyName = this.data.jobInfo.companyName
    const jobName = this.data.jobInfo.name
    return {
      title: `${companyName}正在招聘${jobName}`
    }
  },
  onShareTimeline() {
    // {单位名称} 正在招聘 {职位名称}
    const companyName = this.data.jobInfo.companyName
    const jobName = this.data.jobInfo.name
    return {
      title: `${companyName}正在招聘${jobName}`
    }
  }
})
