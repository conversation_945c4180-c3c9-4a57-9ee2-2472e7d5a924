@use 'styles/variables' as *;

.home-icon {
  width: 30rpx;
  height: 30rpx;
  background: url(#{$assets}/icon/home.png) no-repeat center/contain;
}

.job-detail-container {
  padding: 20rpx 30rpx;
  background-color: $color-background;

  .switch-bar {
    padding: 30rpx;
    margin-top: 20rpx;
    border-radius: $border-radius;
    background-color: $color-white;

    &:first-of-type {
      margin-top: 0;
    }

    .swiper {
      height: var(--swiper-height);
    }

    .card {
      background-color: #fff;
      overflow: hidden;
    }

    .job-item {
      padding: 20rpx 0;
      margin: 0 30rpx;
      border-bottom: 1px solid $border-color;

      .top {
        display: flex;
        justify-content: space-between;

        .title {
          line-height: 48rpx;
          font-size: 32rpx;
          font-weight: bold;
          @include utils-ellipsis;
        }

        .salary {
          margin-left: 100rpx;
          flex-shrink: 0;
          color: $color-point;
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .announcement {
        @include utils-ellipsis;
        margin: 20rpx 0;
        margin-bottom: 16rpx;
      }

      .tag-content {
        display: flex;
        flex-wrap: nowrap;
        flex: 1;
        font-size: 24rpx;

        .tag {
          line-height: 44rpx;
          padding: 0 12rpx;
          border-radius: 4rpx;
          background-color: #f4f6fb;
          color: $font-color-basic;

          & + .tag {
            margin-left: 12rpx;
          }
        }
      }

      &.offline {
        background:
          url('//img.gaoxiaojob.com/uploads/mini/icon/offline.png') no-repeat right 32rpx top / 110rpx 96rpx,
          #fff;

        .top,
        .announcement,
        .bottom {
          opacity: 0.6;
        }

        .salary {
          display: none;
        }
      }

      .bottom {
        display: flex;
        margin-top: 20rpx;
        justify-content: space-between;
        font-size: 24rpx;

        .company {
          max-width: 450rpx;
          @include utils-ellipsis;
        }

        .time {
          flex-shrink: 0;
          padding-left: 34rpx;
          color: $font-color-tips;
          background: url('//img.gaoxiaojob.com/uploads/mini/icon/time.png') no-repeat left/24rpx;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .swiper-paging {
      height: 60rpx;
      position: relative;

      --td-swiper-nav-dot-size: 10rpx;
      --td-swiper-nav-dots-bar-active-width: 20rpx;
      --td-swiper-radius: var(--border-radius);
      --td-swiper-nav-dot-color: #c7c7c7;
      --td-swiper-nav-dot-active-color: var(--color-primary);

      .t-swiper-nav__dots-bar-item {
        margin: 0 5rpx;
      }
    }
  }

  .more-box {
    display: flex;
    justify-content: center;
    .more {
      display: flex;
      align-items: center;
      height: 64rpx;
      margin-top: 40rpx;
      padding: 0 30rpx;
      color: #FFA000;
      line-height: 64rpx;
      font-weight: 500;
      font-size: 28rpx;
      background: #FFFAF1;
      border-radius: 32rpx;
    }
    image {
      width: 26rpx;
      height: 26rpx;
      margin-right: 13rpx;
    }
    text {
      display: inline-block;
      max-width: 250rpx;
      font-weight: bold;
      @include utils-ellipsis;
    }
  }

  .recommend-job {
    padding: 0;
    padding-top: 30rpx;

    .head {
      margin: 0 0 20rpx 30rpx;
    }
  }

  .detail-head {
    .tag-cell {
      display: flex;
      font-size: 22rpx;
      margin-bottom: 25rpx;

      .help-wanted {
        padding: 4rpx 10rpx;
        background-color: #fff0ef;
        border-radius: 4rpx;
        color: #fa635c;
        margin-right: 8rpx;
      }

      .organization {
        padding: 4rpx 10rpx;
        border-radius: 4rpx;
        margin-right: 8rpx;
        color: #4fbc67;
        background-color: #f0ffdc;
      }

      .fast-feedback {
        padding: 4rpx 10rpx 4rpx 25rpx;
        color: $color-primary;
        border-radius: 4rpx;

        background: $tag-primary-background url('//img.gaoxiaojob.com/uploads/mini/icon/lightning.png') no-repeat 10rpx/10rpx
          18rpx;
      }
    }

    .job-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25rpx;

      .job-name-title {
        font-weight: bold;
        font-size: 36rpx;
        line-height: 54rpx;
        flex: 1;
      }

      .job-salary {
        text-align: right;
        color: $color-point;
        font-weight: bold;
        font-size: 30rpx;
        margin-left: 20rpx;
        max-width: 40%;
        @include utils-ellipsis;
      }
    }

    .job-info {
      margin-bottom: 30rpx;
      font-size: 24rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .time {
      display: flex;
      justify-content: space-between;
      font-size: 24rpx;

      .date {
        background: url(#{$assets}/icon/time-black.png) no-repeat left center/26rpx;
        padding-left: 35rpx;
      }
    }

    .association-announcement {
      position: relative;
      margin-top: 30rpx;
      border-radius: 8rpx;
      padding: 15rpx 15rpx 15rpx 144rpx;
      background: $color-primary-background url('//img.gaoxiaojob.com/uploads/mini/announcement/association.png')
        no-repeat left 20rpx center / 114rpx 34rpx;

      .annoucement-name {
        max-width: 432rpx;
        @include utils-ellipsis;
      }

      &::after {
        content: '';
        position: absolute;
        top: 12rpx;
        right: 15rpx;
        width: 36rpx;
        height: 36rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/into.png') no-repeat 20rpx 14rpx/ 18rpx 18rpx;
      }
    }
  }

  .contact-detail {
    display: flex;
    align-items: center;
    padding: 15rpx 30rpx;
    line-height: 1;

    .contact {
      flex: 1;
      color: #333333;
    }
    .hd {
      display: flex;
      font-weight: 500;
      // justify-content: center;
      align-items: center;
      .name {
        max-width: 422rpx;
        // flex-grow: 1;
        font-size: 28rpx;
        @include utils-ellipsis;
      }
      .tag {
        width: 120rpx;
        font-size: 22rpx;
        opacity: 0.6;
        text-align: right;
      }
    }

    .bd {
      width: 560rpx;
      padding-top: 16rpx;
      font-size: 24rpx;
      color: #333333;
      opacity: 0.8;
      @include utils-ellipsis;
    }

    .avatar {
      position: relative;
      &.online-tag {
        &::after {
          position: absolute;
          right: 10rpx;
          bottom: 0;
          content: '';
          width: 20rpx;
          height: 20rpx;
          background: #4FBC67;
          border-radius: 50%;
          border: 2px solid #FFFFFF;
        }
      }
      image {
        width: 71rpx;
        height: 71rpx;
        border-radius: 50%;
        margin-right: 18rpx;
      }
    }
  }

  .detail-main {
    border-radius: $border-radius $border-radius 0 0;

    .welfare-box {
      height: 80rpx;
      overflow: hidden;
    }

    .welfare {
      display: flex;
      white-space: nowrap;
      margin-top: 30rpx;
      height: 90rpx;
      overflow-y: hidden;
      overflow-x: auto;

      .tag {
        height: 40rpx;
        line-height: 40rpx;
        padding: 5rpx 12rpx;
        background-color: $tag-info-background;
        margin-right: 20rpx;
        border-radius: 4rpx;
        color: $font-color-basic;
        font-size: 24rpx;
      }
    }

    $list: info, message, requirement, other;

    @each $item in $list {
      .#{$item} {
        font-weight: bold;
        padding: 0 0 0 45rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/announcement/#{$item}.png') no-repeat top 2rpx left / 34rpx;
      }
    }

    .info {
      margin-top: 30rpx;
    }

    .message,
    .requirement,
    .other {
      margin-top: 50rpx;
    }

    .text-main {
      margin-top: 20rpx;
      line-height: 2;
      word-break: break-word;
    }

    .base {
      display: flex;
      flex-wrap: wrap;
      font-size: 26rpx;
    }

    .text {
      color: $font-color;
      white-space: pre-wrap;
      line-height: 1.5;
      flex: 50%;
      margin-top: 20rpx;
      word-break: break-word;
      @include utils-ellipsis;
    }

    .establishment {
      display: flex;
      display: block;
      white-space: inherit;
      background: url(#{$assets}/icon/question.png) no-repeat left 110rpx top 5rpx/26rpx;

      view {
        display: inline;
      }

      .title {
        display: inline-block;
        white-space: nowrap;
        margin-right: 40rpx;
      }
    }

    .row {
      flex: 100%;
      white-space: initial;
      line-height: 2;
    }

    .is-special {
      white-space: initial;
    }
  }

  .location {
    padding: 30rpx 50rpx 30rpx 72rpx;
    border-top: 1rpx solid $border-color;
    border-radius: 0 0 $border-radius $border-radius;
    background: $color-white url('//img.gaoxiaojob.com/uploads/mini/icon/location.png') no-repeat center left 30rpx/ 30rpx;
  }

  .head {
    font-size: 36rpx;
    font-weight: bold;
    line-height: 1;
  }

  $file-list: word, excel, pdf, zip, ppt, jpg, txt, common;

  .job-files {
    .file-list {
      position: relative;
      padding: 30rpx 70rpx 30rpx 55rpx;
      border-bottom: 1rpx solid $border-color;
      @include utils-ellipsis;

      &:last-of-type {
        border-bottom: none;
        padding-bottom: 0;
      }

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 30rpx;
        height: 36rpx;
        width: 36rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/download.png') no-repeat center center / 36rpx;
      }
    }

    @each $name in $file-list {
      .#{$name} {
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/#{$name}.png') no-repeat left 36rpx/ 36rpx;
      }
    }
  }

  .company-card {
    position: relative;
    display: flex;
    align-items: center;
    background: url('//img.gaoxiaojob.com/uploads/mini/icon/company-card-background.png') no-repeat center / cover;

    &::after {
      content: '';
      position: absolute;
      right: 30rpx;
      width: 11rpx;
      height: 22rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/into-gray.png') no-repeat center center / 11rpx 22rpx;
    }
  }

  .company-logo {
    width: 80rpx;
    height: 80rpx;
    margin-right: 23rpx;
    border: 1rpx solid $border-color;
    border-radius: 12rpx;
  }

  .vip {
    &::after {
      content: '';
      position: absolute;
      top: 80rpx;
      left: 82rpx;
      width: 28rpx;
      height: 28rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/certification.png') no-repeat center / 28rpx;
    }
  }

  .company-info {
    line-height: 1;
    .company-name {
      max-width: 470rpx;
      font-size: 32rpx;
      font-weight: bold;
      @include utils-ellipsis;
    }

    .company-message {
      max-width: 480rpx;
      font-size: 24rpx;
      color: $font-color-label;
      margin-top: 20rpx;
      @include utils-ellipsis;
      text {
        &::after {
          content: ' · ';
        }
        &:last-child {
          &::after {
            content: '';
          }
        }
      }
    }
  }
}

.fake-guide {
  height: 316rpx;
}

.footer-fixed {
  position: sticky;
  bottom: 0;
  display: flex;
  padding: 20rpx 40rpx 20rpx 0;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 2rpx solid #e9e9e9;

  .btn {
    width: 130rpx;
    text-align: center;
    padding-top: 56rpx;
    position: relative;
    font-size: 28rpx;
  }

  .share {
    background: url(#{$assets}/icon/share.png) no-repeat center top 2rpx/38rpx;
    font-weight: normal;
    height: 95rpx;
  }

  .like {
    background: url(#{$assets}/icon/like.png) no-repeat center top 2rpx/38rpx;

    &.like-primary {
      background: url(#{$assets}/icon/like-primary.png) no-repeat center top 2rpx/38rpx;
    }
  }

  .chat {
    background: url(#{$assets}/icon/chat.png) no-repeat center top / 45rpx;
  }

  .deliver {
    width: 438rpx;
    height: 88rpx;
    background: $color-primary;
    color: #fff;
    border-radius: 44rpx;
  }
}

.block {
  .header {
    text-align: center;
    padding: 35rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    border-bottom: 2rpx solid $border-color;
  }

  .prop-main {
    padding: 30rpx 0;
    .title {
      color: $color-point;
      background: none;
      margin: 0;
    }

    .content {
      line-height: 1.5;
      padding: 36rpx 43rpx;
    }
  }
}

.competitiveness-analysis {
  margin-top: 20rpx;
  padding: 30rpx;
  background-color: $color-white;
  border-radius: $border-radius;

  &.is-show {
    .description {
      .match,
      .percentage,
      .tips {
        filter: blur(0);
      }
    }

    .description {
      margin-bottom: 72rpx;
    }
  }

  .title {
    margin-bottom: 40rpx;
    font-size: 36rpx;
    font-weight: bold;
  }

  .description {
    margin-bottom: 30rpx;
    line-height: 42rpx;

    .match,
    .percentage,
    .tips {
      color: $color-primary;
      font-weight: bold;
      filter: blur(4px);
    }
  }

  .percentage-chart {
    color: $font-color-label;
    font-size: 24rpx;

    .tips {
      margin-bottom: 20rpx;
      text-align: center;
    }

    .chart {
      position: relative;
      display: flex;
    }

    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 25%;

      $height: 10rpx;
      $radius: $height / 2;
      $list: #fdde60, #fcc854, #fda92c, #ff8c00, #ff6600;

      & + .item {
        margin-left: 4rpx;
      }

      &::before {
        content: '';
        margin-bottom: 20rpx;
        width: 100%;
        height: $height;
      }

      @each $item in $list {
        $index: index($list, $item);

        @if ($index < length($list)) {
          &:nth-child(#{$index})::before {
            background: linear-gradient(90deg, $item, nth($list, $index + 1));

            @if ($index == 1) {
              border-top-left-radius: $radius;
              border-bottom-left-radius: $radius;
            }

            @if ($index == length($list) - 1) {
              border-top-right-radius: $radius;
              border-bottom-right-radius: $radius;
            }
          }
        }
      }
    }

    .mark {
      position: absolute;
      top: -30rpx;
      left: 0;
      width: 29rpx;
      height: 51rpx;
      background: url(#{$assets}/icon/mark.png) no-repeat center / contain;
      transform: translateX(calc(-29rpx / 2));
    }
  }

  .button {
    margin-top: 40rpx;
    text-align: center;

    .button-text {
      display: inline-block;
      padding: 0 57rpx;
      color: $color-primary;
      font-weight: bold;
      line-height: 70rpx;
      box-shadow: 0 0 0 2rpx #ffeac7;
      border-radius: 35rpx;
    }
  }
}

.report-dialog-content {
  color: $font-color-basic;
  font-size: 32rpx;
  text-align: center;
  line-height: 48rpx;
}

.report-dialog-description {
  margin-top: 30rpx;
  color: $color-primary;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
}
