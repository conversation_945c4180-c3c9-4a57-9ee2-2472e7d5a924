import { getElClientRect, showToast, showLoading, toWebPage } from '@/utils/util'

import { throttle } from 'throttle-debounce'
import {
  getSpecialDetail,
  getActivityScheduleV2,
  getAllSpecial,
  getCompanySearchParams,
  getActivityCompany
} from '@/api/discover'

import { jump, sceneStringToObj } from '@/utils/url'
import { htmlLinkHandlerMixin } from '@/utils/html-link-handler'

let _this = <any>null

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLogin: false,

    specialActivityId: '',

    welfareVisible: false,
    activeTab: 'session',
    scrollIntoView: '',
    isTabFixed: false,

    showBackTop: false,

    scrollWithAnimation: false,

    headerHeight: 0,
    tabHeight: 0,
    tabOffsetTop: 0,
    latelySessionScrollTop: 0,
    scrollTop: 0,
    scrollViewHeight: 0,

    // 富文本相关
    copyLink: false,
    companyAsideHeight: 0,

    detail: <any>{
      realParticipationActivityAmount: 0,
      realParticipationCompanyAmount: 0
    },
    activityDetailHtml: '',
    hasWelfareDetailHtml: false,
    welfareDetailHtml: '',

    codePopupVisible: false,

    startCountDown: 0,
    sessionTimer: 0,
    sessionStartCountDown: {
      day: 0,
      hours: 0,
      min: 0,
      second: 0
    },
    sessionList: [],
    historySessionList: [],

    activityList: [],
    activityId: '',
    activityDetail: <any>{
      startCountDown: 0
    },
    limit: 5,
    label: {
      areaId: '所在地区',
      majorId: '需求学科',
      categoryId: '职位类型',
      type: '单位类型'
    },
    visible: {
      area: false,
      major: false,
      categoryId: false,
      type: false
    },
    cityParams: [],
    jobTypeList: [],
    majorSelect: [],
    typeParams: [],
    activityStartCountDown: {
      day: 0,
      hours: 0,
      min: 0,
      second: 0
    },
    activityTimer: 0,
    formData: <any>{
      areaId: [],
      type: [],
      majorId: [],
      categoryId: [],
      page: 1
    },
    companyList: [],
    isEnd: false,

    reviewHtml: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(e: any) {
    _this = this
    // 有两种情况,一种是有场景值(也就是二维码扫码进来的)
    // 一种是没有场景值(也就是分享进来的)
    // 有场景值的时候,需要把场景值存到本地,然后在投递的时候带上
    // 没有场景值的时候,需要判断是否有本地存储的场景值,有的话就带上
    // 没有的话就不带

    let id = ''
    if (e.scene) {
      const sceneObj = sceneStringToObj(e.scene)

      id = sceneObj.id
    } else {
      id = e.id
    }

    this.setData({ specialActivityId: id })
    showLoading()

    this.getTabHeight()
    await this.getDetail()
    await this.getSchedule()

    const { realParticipationCompanyAmount } = this.data.detail
    // 参会单位数量大于等于2才获取
    if (realParticipationCompanyAmount > 1) {
      this.getSpecial()
      this.getCompanyParam()
      this.getCompanyList()
    }

    wx.hideLoading()

    await this.getScrollViewHeight()
    await this.setCompanyAsideHeight()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  async getTabHeight() {
    const { height } = await (<any>getElClientRect('.tabs-wrapper'))
    this.setData({ tabHeight: height })
  },

  getTabOffsetTop() {
    setTimeout(async () => {
      const { top } = await (<any>getElClientRect('.tabs-wrapper'))
      this.setData({ tabOffsetTop: top })
    }, 310)
  },

  async setCompanyAsideHeight() {
    const { scrollViewHeight } = this.data
    const { height } = await (<any>getElClientRect('.tabs-wrapper'))

    this.setData({ companyAsideHeight: scrollViewHeight - height })
  },

  async getScrollViewHeight() {
    const { height: fixedBottomHeight } = await (<any>getElClientRect('.fixed-bottom-wrapper'))
    const { headerOffsetHeight, screenHeight } = getApp().globalData
    this.setData({
      headerHeight: headerOffsetHeight,
      scrollViewHeight: screenHeight - headerOffsetHeight - fixedBottomHeight
    })
  },

  async switchSpecial(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { detail }
      }
    } = e

    const { activityId = '' } = detail
    this.setData({
      activityId: activityId
    })

    const resetFormDateKey = ['areaId', 'majorId', 'categoryId', 'type']
    resetFormDateKey.forEach((key) => {
      this.setData({ [`formData.${key}`]: [] })
      this.handleLabel(key)
    })

    await this.getCompanyParam()
    await this.getCompanyList()

    await this.getScrollViewHeight()
    await this.setCompanyAsideHeight()
  },

  showPopup(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { visible }
      }
    } = e
    this.setData({ [`visible.${visible}`]: true })
  },

  handleLabel(key: string, label: string = '', length: number = 0) {
    const labelMap: { [key: string]: string } = {
      areaId: '所在地区',
      majorId: '需求学科',
      categoryId: '职位类型',
      type: '单位类型'
    }
    const defaultLabel = labelMap[key]
    let labelTxt = ''

    if (length === 0) {
      labelTxt = defaultLabel
    } else if (length === 1) {
      labelTxt = label
    } else {
      labelTxt = `${defaultLabel}·${length}`
    }

    this.setData({ [`label.${key}`]: labelTxt })
  },

  handleChange(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { formKey }
      },
      detail: { label, value = [] }
    } = e

    this.setData({
      [`formData.${formKey}`]: value
    })

    const [firstLabel] = label
    const { length } = value

    this.handleLabel(formKey, firstLabel, length)

    this.getCompanyList()
  },

  checkLogin() {
    const { isLogin } = this.data

    if (!isLogin) {
      this.setData({ loginDialogVisible: true })
    }
    return isLogin
  },

  async tabSwitch(e: WechatMiniprogram.CustomEvent) {
    const { type } = e.currentTarget.dataset
    const { activeTab } = this.data
    if (activeTab === type) return
    this.setData({ activeTab: type })

    if (type == 'session') {
      const { latelySessionScrollTop } = this.data
      this.setData({ scrollTop: latelySessionScrollTop })
    } else {
      const { tabOffsetTop, headerHeight } = this.data
      this.setData({ scrollTop: tabOffsetTop - headerHeight })
    }
  },

  openWelfarePopup() {
    const { hasWelfareDetailHtml } = this.data

    this.setData({ welfareVisible: hasWelfareDetailHtml })
  },

  closeWelfarePopup() {
    this.setData({ welfareVisible: false })
  },

  backTop() {
    this.setData({ scrollWithAnimation: true, scrollTop: 0 })
    setTimeout(() => {
      this.setData({ scrollWithAnimation: false })
    }, 300)
  },

  setBackTop: throttle(300, ({ scrollTop }) => {
    const { scrollViewHeight } = _this.data
    const isShow = scrollTop > scrollViewHeight
    _this.setData({
      showBackTop: isShow
    })
  }),

  scroll(event: any) {
    const { scrollTop } = event.detail
    const { tabOffsetTop, headerHeight } = this.data
    const flag = tabOffsetTop - headerHeight <= scrollTop
    this.setData({ isTabFixed: flag })

    this.setBackTop({ scrollTop })
  },

  scrolltolower() {
    const { activeTab, isEnd } = this.data
    if (activeTab == 'company' && !isEnd) {
      const { page } = this.data.formData
      this.setData({ 'formData.page': page + 1 })
      this.getCompanyList(true)
    }
  },

  getDate(time: number) {
    const day = Math.floor(time / (24 * 3600))
    const hours = Math.floor((time % (24 * 3600)) / 3600)
    const min = Math.floor((time % 3600) / 60)
    const second = time % 60

    return { day, hours, min, second }
  },

  sessionCountDow() {
    clearTimeout(this.data.sessionTimer)

    const { startCountDown } = this.data

    const time = Number(startCountDown)

    if (time === 0) {
      this.getSchedule(false)
      return
    }

    const countDownObj = this.getDate(time)

    this.setData({
      sessionStartCountDown: countDownObj,
      startCountDown: time - 1
    })

    this.data.sessionTimer = setTimeout(() => {
      this.sessionCountDow()
    }, 1000)
  },

  activityCountDow() {
    clearTimeout(this.data.activityTimer)

    const { startCountDown } = this.data.activityDetail

    const time = Number(startCountDown)

    if (time === 0) {
      this.getCompanyParam()
      return
    }

    const countDownObj = this.getDate(time)

    this.setData({
      activityStartCountDown: countDownObj,
      'activityDetail.startCountDown': time - 1
    })

    this.data.activityTimer = setTimeout(() => {
      this.activityCountDow()
    }, 1000)
  },

  closeCodePopup() {
    this.setData({ codePopupVisible: false })
  },

  showCodePopup() {
    this.setData({ codePopupVisible: true })
  },

  async scrollLatelySession() {
    const { activeTab } = this.data
    if (activeTab === 'session') {
      const { top = 0 } = await (<any>getElClientRect('#lately-session'))
      const { tabHeight, headerHeight } = this.data
      const scrollTop = top - tabHeight - headerHeight
      this.setData({ scrollTop, latelySessionScrollTop: scrollTop })
    }
  },

  async getDetail() {
    const { specialActivityId } = this.data
    await getSpecialDetail(specialActivityId).then((resp: any) => {
      const {
        activityDetail,
        participationBenefitDetail,
        retrospection,
        realParticipationActivityAmount,
        realParticipationCompanyAmount,
        ...other
      } = resp.detail
      this.setData(
        {
          activityDetailHtml: activityDetail,
          hasWelfareDetailHtml: !!participationBenefitDetail,
          welfareDetailHtml: participationBenefitDetail,
          reviewHtml: retrospection,
          detail: {
            realParticipationActivityAmount: Number(realParticipationActivityAmount),
            realParticipationCompanyAmount: Number(realParticipationCompanyAmount),
            ...other
          }
        },
        () => {
          // 渲染完毕在获取
          this.getTabOffsetTop()
        }
      )
    })
  },

  getSchedule(isScroll = true) {
    const { realParticipationActivityAmount } = this.data.detail
    if (realParticipationActivityAmount < 2) {
      this.setData({ activeTab: 'detail' })
      return
    }

    const { specialActivityId } = this.data
    getActivityScheduleV2(specialActivityId).then((resp: any) => {
      const { startCountDown, effectiveActivityList: sessionList, historyActivityList: historySessionList } = resp

      this.setData({
        startCountDown,
        sessionList,
        historySessionList
      })

      if (startCountDown) {
        this.sessionCountDow()
      }

      if (isScroll) {
        this.scrollLatelySession()
      }
    })
  },

  getSpecial() {
    const { specialActivityId } = this.data
    getAllSpecial(specialActivityId).then((resp: any) => {
      this.setData({ activityList: resp })
    })
  },

  async getCompanyParam() {
    const { specialActivityId, activityId } = this.data
    await getCompanySearchParams({ specialActivityId, activityId }).then((resp: any) => {
      const { companyTabList, cityParams, currentActivityTab = {}, jobTypeList, majorSelect, typeParams } = resp
      this.setData({
        activityList: companyTabList,

        cityParams,
        activityDetail: currentActivityTab,
        jobTypeList,
        majorSelect,
        typeParams
      })

      const { startCountDown = '' } = currentActivityTab
      if (startCountDown) {
        this.activityCountDow()
      }
    })
  },

  async getCompanyList(isAppend: boolean = false) {
    showLoading()

    const { specialActivityId, activityId, formData, activeTab } = this.data
    Object.keys(formData).forEach((key) => {
      const value = formData[key]
      const isArray = Array.isArray(value)
      if (isArray) {
        formData[key] = value.join()
      }
    })
    if (!isAppend) {
      formData.page = 1
    }
    await getActivityCompany({ specialActivityId, activityId, ...formData })
      .then((resp: any) => {
        const { list, isEnd } = resp
        const { companyList } = this.data
        this.setData({ companyList: isAppend ? companyList.concat(list) : list, isEnd: isEnd == 1 }, () => {
          if (!isAppend && activeTab === 'company') {
            this.setData({ scrollIntoView: 'company-wrapper' })
          }
        })

        wx.hideLoading()
      })
      .catch(() => {
        wx.hideLoading()
      })
  },

  companyClick(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { item }
      }
    } = e

    const { targetUrl, targetType } = item
    jump(targetUrl, targetType)
  },

  handleApply(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { type }
      }
    } = e
    // type 1单位报名，2人才报名
    const {
      activityDetail,
      activityId,
      activeTab,
      detail: { applyLinkType, applyLink, applyLinkCompanyType, applyLinkCompany }
    } = this.data

    const isSingle = activeTab == 'company' && activityId != ''

    let openType = ''
    let openLink = ''

    if (type === '1') {
      openType = isSingle ? activityDetail.applyLinkCompanyType : applyLinkCompanyType
      openLink = isSingle ? activityDetail.applyLinkCompany : applyLinkCompany
    } else {
      openType = isSingle ? activityDetail.applyLinkType : applyLinkType
      openLink = isSingle ? activityDetail.applyLink : applyLink
    }

    // 报名链接类型；1:小程序内部；2:内部链接(系统内部，非小程序内部)；0:第三方链接
    if (openType == '2') {
      toWebPage(openLink)
    } else if (openType == '1') {
      jump(openLink, openType)
    } else {
      wx.setClipboardData({
        data: openLink,
        success() {
          // showToast('已复制报名链接，请至浏览器打开报名')
          showToast('该报名链接暂不支持通过小程序直接访问（已自动复制报名链接，请至浏览器打开报名）')
        }
      })
    }
  },

  openDetail(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { id }
      }
    } = e
    wx.navigateTo({ url: `/packages/discover/activity/index?id=${id}` })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { detail } = this.data
    return {
      title: detail.name
    }
  },
  onShareTimeline() {
    const { detail } = this.data
    return {
      title: detail.name
    }
  },

  // 使用富文本链接处理mixin
  ...htmlLinkHandlerMixin
})
