@use 'styles/variables' as *;

$color-primary: #296aff;

@mixin common-wrapper-rich {
  .common-wrapper {
    padding-top: 60rpx;
    display: flex;
    flex-direction: column;

    .common-title-content {
      align-self: center;
      display: flex;
      padding-bottom: 35rpx;
      transform: translateX(-30rpx);

      &::before {
        position: relative;
        right: -80rpx;
        top: -8rpx;
        content: '';
        width: 114rpx;
        height: 32rpx;
        background: url(#{$assets}/activity/special/common-title-left.png) no-repeat center/contain;
      }

      &::after {
        position: relative;
        z-index: 2;
        align-self: flex-end;
        bottom: -8rpx;
        right: 20rpx;
        content: '';
        width: 47rpx;
        height: 26rpx;
        background: url(#{$assets}/activity/special/common-title-right.png) no-repeat center/contain;
      }

      .common-title {
        position: relative;
        max-width: 70%;
        z-index: 1;
        line-height: 46rpx;
        border-radius: 8rpx;
        color: $color-white;
        background: linear-gradient(90deg, #2a6bff, #25c9e6);
        padding: 7rpx 30rpx;
        transform: skewX(-12deg);
        text-align: center;

        .title {
          display: block;
          font-weight: bold;
          font-size: 36rpx;
          transform: skewX(12deg);
        }
      }
    }

    .common-content {
      line-height: 2;
      font-size: 30rpx;

      .common-second-wrapper {
        display: flex;
        flex-direction: column;
        background-color: #f4f6fb;
        border-radius: 16rpx;
        padding: 20rpx 30rpx;
        margin: 10rpx 0 30rpx;

        .common-second-title {
          font-size: 32rpx;
          padding-top: 4rpx;
          color: #814e33;
          line-height: 1.5;
          font-weight: bold;
          padding-left: 15rpx;
          background: url(#{$assets}/activity/special/common-second-title.png) no-repeat left center/42rpx 42rpx;
          margin-bottom: 8rpx;
        }

        .common-second-content {
          display: block;
          font-size: 28rpx;
        }
      }
    }
  }
}

.cover-wrapper {
  position: relative;

  .status {
    position: absolute;
    top: -0;
    left: 0;
    color: $color-white;
    font-size: 24rpx;
    padding-top: 3rpx;
    min-width: 128rpx;
    height: 53rpx;
    text-align: center;

    // $status: await, await-start, start, ended;
    // 状态；1:待举办；2：报名中；3:进行中；4:已结束

    $status: (
        name: status-1,
        img: await
      ),
      (
        name: status-2,
        img: await-start
      ),
      (
        name: status-3,
        img: start
      ),
      (
        name: status-4,
        img: ended
      );

    @each $item in $status {
      $name: map-get(
        $map: $item,
        $key: 'name'
      );
      $img: map-get(
        $map: $item,
        $key: 'img'
      );

      @if $name == status-3 {
        &.#{$name} {
          text-align: left;
          padding-left: 43rpx;
          background: url(#{$assets}/activity/common/play.gif) no-repeat 13rpx 9rpx/20rpx 20rpx,
            url(#{$assets}/activity/special/#{$img}-bg.png) no-repeat left top/128rpx 53rpx;
        }
      } @else {
        &.#{$name} {
          background: url(#{$assets}/activity/special/#{$img}-bg.png) no-repeat left top/128rpx 53rpx;
        }
      }
    }
  }

  .img {
    display: block;
    width: 100%;
    height: auto;
  }
}

.transform-wrapper {
  // transform: translateY(-30rpx);
  position: relative;
  z-index: 1;
  margin-top: -30rpx;
  background-color: $color-white;
  padding: 0 30rpx;
  border-radius: 30rpx 30rpx 0 0;
}

.detail-wrapper {
  .title {
    padding-top: 30rpx;
    font-size: 36rpx;
    font-weight: bold;
    line-height: 54rpx;
    margin-bottom: 10rpx;
  }

  .tag-content {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    margin-bottom: 14rpx;
    margin-right: -30rpx;

    &::-webkit-scrollbar {
      display: none;
    }

    .tag {
      & + .tag {
        margin-left: 10rpx;
      }

      &.type {
        color: #ffa000;
        background-color: #fff3e0;
      }

      font-size: 20rpx;
      white-space: nowrap;
      padding: 0 13rpx;
      line-height: 34rpx;
      border-radius: 4rpx;
      background-color: #e5edff;
      color: $color-primary;
    }
  }

  .detail {
    margin-bottom: 22rpx;

    .list {
      display: flex;
      line-height: 36rpx;
      font-size: 24rpx;

      & + .list {
        margin-top: 6rpx;
      }

      $icons: organization, time, address, type, session;
      @each $name in $icons {
        &.#{$name} {
          .label {
            font-weight: bold;
            padding-left: 37rpx;
            background: url(#{$assets}/activity/common/#{$name}.png) no-repeat left 6rpx/26rpx 26rpx;
          }
        }
      }

      &.address {
        display: block;
        overflow: hidden;
        white-space: wrap;
        padding-left: 37rpx;
        background: url(#{$assets}/activity/common/address.png) no-repeat left 6rpx/26rpx 26rpx;

        .label {
          padding-left: 0rpx;
          background: none;
          display: inline-block;
        }
      }

      .label {
        flex-shrink: 0;
      }

      .ellipsis {
        @include utils-ellipsis-lines(1, 36rpx, 24rpx);
      }

      &.session {
        .content {
          display: flex;

          .total {
            color: #ffa000;
            font-size: 32rpx;
            font-weight: bold;
            margin-right: 4rpx;
          }

          .item {
            display: flex;
            align-items: baseline;
            margin-right: 32rpx;

            &.company {
              position: relative;
              padding-left: 35rpx;
              background: url(#{$assets}/activity/common/company.png) no-repeat left 6rpx/26rpx 26rpx;

              .label {
                background: none;
                padding: 0;
              }

              .tips {
                position: absolute;
                left: calc(100% - 22rpx);
                top: -27rpx;
                padding: 0 11rpx;
                line-height: 32rpx;
                color: #ffa000;
                font-size: 20rpx;
                background: #fff8ec;
                white-space: nowrap;
                border-radius: 16rpx 16rpx 16rpx 0rpx;
              }
            }
          }
        }
      }
    }
  }

  .welfare {
    line-height: 46rpx;
    padding-left: 65rpx;
    padding-right: 32rpx;
    margin-bottom: 22rpx;
    background: url(#{$assets}/activity/special/welfare.png) no-repeat 20rpx 6rpx/34rpx 34rpx, #fff8ec;
    border-radius: 12rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.has-detail {
      padding-right: 21rpx;

      .arrow {
        display: initial;
      }
    }

    .content {
      color: #814e33;
      @include utils-ellipsis-lines(1, 46rpx, 24rpx);
    }

    .arrow {
      display: none;
      width: 18rpx;
      height: 18rpx;
      background: url(#{$assets}/activity/special/arrow.png) no-repeat center/ contain;
    }
  }
}

.tabs-wrapper {
  position: sticky;
  top: -2rpx;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  border-bottom: 2rpx solid $border-color;
  background-color: $color-white;

  &.is-fixed {
    margin: 0 -30rpx;
    padding: 0 30rpx;
    border-bottom-color: transparent;
    box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(51, 51, 51, 0.02);
  }

  &.is-company {
    box-shadow: none;
  }

  .tabs-nav {
    display: flex;
    justify-content: center;

    .text {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      line-height: 88rpx;
      font-size: 30rpx;
      color: $font-color-basic;
    }

    &.active {
      .text {
        color: $color-primary;
        font-weight: bold;

        &::before {
          content: '';
          width: 50rpx;
          height: 8rpx;
          position: absolute;
          left: 50%;
          bottom: 10rpx;
          transform: translateX(-50%);
          border-radius: 4rpx;
          background: linear-gradient(90deg, #2a6bff, #25c9e6);
        }
      }
    }
  }
}

.tabs-pane-wrapper {
  .detail-pane {
    @include common-wrapper-rich;
  }

  .session-pane {
    padding: 32rpx 0rpx 40rpx 7rpx;

    .wrapper-name {
      font-size: 20rpx;
      color: $font-color-label;
      margin-bottom: 20rpx;
    }

    .session-order-wrapper {
      position: relative;
      padding-left: 8rpx;

      $aside-width: 70rpx;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 42rpx;
        height: calc(100% - 74rpx);
        border-left: 2rpx dashed #d7d7d7;
        border-right: 2rpx dashed #d7d7d7;
        transform: scaleX(0.5);
        z-index: 1;
      }

      &.is-end-wrapper {
        &::before {
          top: 130rpx;
          height: calc(100% - 204rpx);
        }
      }

      .end-title {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: $font-color-label;
        line-height: 36rpx;
        margin-top: 24rpx;
        text-align: center;
        margin-bottom: 4rpx;

        &::after,
        &::before {
          content: '';
          width: 86rpx;
          height: 2rpx;
          flex-shrink: 0;
          background: #efefef;
        }

        &::before {
          margin-right: 22rpx;
        }

        &::after {
          margin-left: 22rpx;
        }
      }

      .month-list {
        position: relative;
        z-index: 9;

        & + .month-list {
          margin-top: 34rpx;
        }

        &.has-history {
          .month-title-content {
            .month-title {
              .month {
                background: url(#{$assets}/activity/special/month-gray-bg.png) no-repeat center/68rpx 31rpx;
              }
            }
          }
        }
      }

      .day-session {
        & + .day-session {
          margin-top: 30rpx;
        }
      }

      .month-title-content {
        display: flex;

        .month-title {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: $aside-width;

          .month {
            background-color: $color-white;
            font-size: 24rpx;
            text-align: center;
            width: 68rpx;
            color: $color-white;
            line-height: 31rpx;
            background: url(#{$assets}/activity/special/month-bg.png) no-repeat center/68rpx 31rpx;
          }

          .year {
            background-color: $color-white;
            font-size: 20rpx;
            color: $font-color-label;
            line-height: 28rpx;
            height: 28rpx;

            &.is-opacity {
              background-color: transparent;
            }
          }
        }

        .month-total {
          line-height: 30rpx;
          margin-left: 14rpx;
          display: flex;
          padding-top: 4rpx;

          .total {
            color: #ffa000;
            margin: 0 4rpx;
          }
        }
      }

      .session-item {
        position: relative;

        & + .session-item {
          margin-top: 30rpx;
        }

        &.is-end {
          .line {
            background-color: #d7d7d7;
          }
        }

        .start-info {
          margin-left: 87rpx;
          font-size: 24rpx;
          margin-top: -8rpx;
          margin-bottom: 22rpx;

          .title {
            display: flex;
            color: #fa635c;
            white-space: nowrap;
            justify-content: center;
            margin-bottom: 17rpx;

            .site {
              max-width: 264rpx;
              @include utils-ellipsis;
            }
          }

          .countdown {
            display: flex;
            justify-content: center;
            align-items: center;

            .box {
              box-sizing: border-box;
              min-width: 40rpx;
              padding: 0 5rpx;
              background: #ffffff;
              border-radius: 8rpx;
              border: 2rpx solid #ffc5c2;
              font-weight: bold;
              color: #fa635c;
              text-align: center;
              line-height: 36rpx;
              margin: 0 10rpx;
            }
          }
        }

        .session-box {
          display: flex;
          justify-content: space-between;
        }

        .aside-date {
          width: $aside-width;
          background-color: $color-white;
          align-self: center;

          .date {
            display: flex;
            flex-direction: column;
            text-align: center;
            font-size: 24rpx;
            font-weight: bold;

            &.is-end {
              color: $font-color-label;
            }
          }

          .number {
            text-align: center;
            font-size: 20rpx;
            color: #ffa000;
            line-height: 1;
            padding-bottom: 4rpx;
          }
        }

        .line {
          width: 17rpx;
          height: 2rpx;
          align-self: center;
          background-color: #ffa000;
        }

        .session-detail {
          background-color: #f4f6fb;
          width: 593rpx;
          border-radius: 8rpx;
          box-sizing: border-box;
          padding: 14rpx 20rpx 14rpx 10rpx;
          display: flex;
          align-items: center;

          .status {
            width: 30rpx;
            padding: 12rpx 5rpx;
            border-radius: 8rpx;
            line-height: 1.3;
            min-height: 120rpx;
            font-size: 22rpx;
            margin-right: 16rpx;
            align-self: flex-start;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;

            $status: (
              'status-1': (
                'font-color': #5386ff,
                'bg-color': #e5edff
              ),
              'status-2': (
                'font-color': #fa635c,
                'bg-color': #fde9e8
              ),
              'status-3': (
                'font-color': #ffa000,
                'bg-color': #fdf1dc
              ),
              'status-4': (
                'font-color': #fff,
                'bg-color': #d7d7d7
              )
            );

            @each $name, $value in $status {
              &.#{$name} {
                color: map-get($map: $value, $key: 'font-color');
                background-color: map-get($map: $value, $key: 'bg-color');
              }
            }
          }

          .info {
            overflow: hidden;
            padding-top: 4rpx;

            .name {
              font-size: 28rpx;
              font-weight: bold;
              margin-bottom: 14rpx;
              @include utils-ellipsis;
            }

            .time {
              color: $font-color-label;
              padding-left: 32rpx;
              font-size: 24rpx;
              background: url(#{$assets}/activity/common/time-opacity.png) no-repeat left/24rpx 24rpx;
              margin-bottom: 15rpx;
            }

            .address {
              font-size: 24rpx;
              color: $font-color-label;
              @include utils-ellipsis;
              padding-left: 32rpx;
              background: url(#{$assets}/activity/common/address-opacity.png) no-repeat left/24rpx 24rpx;
            }
          }
        }
      }
    }
  }

  .company-pane {
    .wrapper-tips {
      padding: 22rpx 0;
      padding-left: 26rpx;
      background: url(#{$assets}/activity/common/warn-gray.png) no-repeat left/20rpx 20rpx;
      font-size: 20rpx;
      color: $font-color-label;
    }

    .company-wrapper {
      margin: 0 -30rpx;
      white-space: nowrap;
      text-align: right;

      .aside {
        float: left;
        position: sticky;
        top: 88rpx;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        width: 206rpx;
        font-size: 24rpx;
        overflow-y: scroll;

        &::after {
          content: '';
          flex-grow: 1;
          background-color: #f6f6f6;
        }

        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          box-sizing: border-box;
          padding: 24rpx 0 24rpx;
          background-color: #f6f6f6;
          position: relative;

          .name {
            font-size: 24rpx;
            width: 100%;
            text-align: center;
            box-sizing: border-box;
            padding: 0 30rpx;
            position: relative;
            @include utils-ellipsis-lines(2, 36rpx, 24rpx);
          }

          .status {
            padding: 0 7rpx;
            font-size: 18rpx;
            line-height: 26rpx;
            border-radius: 13rpx 13rpx 13rpx 0rpx;
            color: $font-color-tips;
            background-color: #eaeaea;
          }

          &.active {
            background-color: $color-white;

            &::before {
              content: '';
              display: block;
              z-index: 1;
              position: absolute;
              bottom: 100%;
              width: 100%;
              height: 30rpx;
              left: 0;
              background: url(#{$assets}/activity/special/slide-bar-pre.png) no-repeat left bottom/cover;
            }

            &::after {
              content: '';
              display: block;
              z-index: 1;
              position: absolute;
              top: 100%;
              width: 100%;
              height: 30rpx;
              left: 0;
              background: url(#{$assets}/activity/special/slide-bar-next.png) no-repeat left top/cover;
            }

            .name {
              color: #ffa000;
              font-weight: bold;

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 8rpx;
                height: 40rpx;
                background: #ffa000;
                border-radius: 4rpx;
              }
            }
          }
        }
      }

      .company-box {
        white-space: initial;
        text-align: initial;
        display: inline-block;
        width: 544rpx;

        .filter-content {
          padding-top: 2rpx;
          padding-bottom: 4rpx;
          background-color: $color-white;
          margin-bottom: 26rpx;
          display: flex;
          flex-wrap: nowrap;
          overflow-x: auto;
          padding-left: 12rpx;
          position: sticky;
          top: 86rpx;
          z-index: 5;
          padding-right: 20rpx;

          &::-webkit-scrollbar {
            display: none;
          }

          .filter-item {
            padding: 0 35rpx 0 20rpx;
            background-color: #f7f7f7;
            line-height: 52rpx;
            font-size: 24rpx;
            color: $font-color-basic;
            white-space: nowrap;
            border-radius: 8rpx;
            position: relative;
            border: 2rpx solid #f7f7f7;

            &::after {
              position: absolute;
              content: '';
              border-left: 6rpx solid #c7c7c7;
              border-right: 6rpx solid transparent;
              border-top: 6rpx solid transparent;
              border-bottom: 6rpx solid transparent;
              transform: rotate(45deg);
              right: 10rpx;
              bottom: 10rpx;
            }

            &.has-select {
              border-color: #ffa000;
              color: #ffa000;
              background-color: #fffaf1;

              &::after {
                border-left-color: #ffa000;
              }
            }

            & + .filter-item {
              margin-left: 10rpx;
            }
          }
        }

        .session-detail {
          display: flex;
          flex-direction: column;
          align-items: center;
          box-sizing: border-box;
          margin: 0 20rpx 8rpx;
          overflow: hidden;
          background-color: #f4f6fb;
          border-radius: 12rpx;
          padding: 20rpx 20rpx;

          .name {
            font-weight: bold;
            max-width: 100%;
            margin-bottom: 17rpx;
            @include utils-ellipsis;
          }

          .countdown {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20rpx;
            margin-bottom: 18rpx;

            .box {
              min-width: 28rpx;
              padding: 0 6rpx;
              box-sizing: border-box;
              line-height: 28rpx;
              border-radius: 6rpx;
              border: 2rpx solid #ffc5c2;
              color: #fa635c;
              margin: 0 6rpx;
              background-color: $color-white;
              text-align: center;
            }

            .await {
              font-weight: bold;
            }
          }

          .time {
            font-size: 18rpx;
            margin-bottom: 15rpx;
          }

          .address {
            display: flex;
            font-size: 18rpx;
            line-height: 28rpx;

            .label {
              flex-shrink: 0;
            }
          }
        }

        .company-list {
          padding: 0 20rpx;

          .item {
            width: 100%;
            overflow: hidden;
            padding: 12px 0 22rpx;
            border-bottom: 2rpx solid #ebebeb;

            .detail {
              display: flex;
              align-items: center;

              .logo {
                flex-shrink: 0;
                position: relative;
                width: 70rpx;
                height: 70rpx;
                margin-right: 12rpx;

                &.is-vip {
                  &::after {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    content: '';
                    width: 22rpx;
                    height: 17rpx;
                    background: url(#{$assets}/activity/special/vip.png) no-repeat center/contain;
                  }
                }

                .img {
                  width: 100%;
                  height: 100%;
                }
              }

              .info {
                flex-grow: 1;
                overflow: hidden;
                padding-bottom: 14rpx;

                .name {
                  font-weight: bold;
                  margin-bottom: 6rpx;
                  @include utils-ellipsis-lines(2, 39rpx, 26rpx);
                }

                .middle {
                  margin-bottom: 12rpx;
                  font-size: 20rpx;
                }

                .bottom {
                  font-size: 20rpx;
                  @include utils-ellipsis;
                }
              }
            }

            $name: welfare, notice, major, update;

            @each $key in $name {
              .#{$key} {
                font-size: 20rpx;
                color: $font-color-label;
                @include utils-ellipsis;
              }
            }

            .welfare {
              padding-left: 35rpx;
              background: url(#{$assets}/activity/special/praise.png) no-repeat left center/26rpx 24rpx;
            }

            .notice {
              padding-left: 28rpx;
              background: url(#{$assets}/activity/special/link.png) no-repeat left center/20rpx 20rpx;
            }

            .major {
              &::before {
                display: inline-block;
                content: '';
                width: 10rpx;
                height: 10rpx;
                background: #fdc15c;
                border-radius: 50%;
                margin-right: 8rpx;
              }
            }

            .update {
              padding-left: 28rpx;
              background: url(#{$assets}/activity/special/warn.png) no-repeat left center/20rpx 20rpx;
            }
          }

          .tips {
            font-size: 24rpx;
            color: $font-color-label;
            text-align: center;
            padding: 30rpx 0;
          }
        }

        .empty {
          padding-top: 370rpx;
          text-align: center;
          padding-bottom: 30rpx;
          font-size: 24rpx;
          color: $font-color-basic;
          background: url(#{$assets}/activity/common/empty.png) no-repeat center 50rpx/400rpx 290rpx;
        }
      }
    }
  }

  .review-pane {
    @include common-wrapper-rich;
  }
}

.code-popup {
  background-color: transparent !important;

  .code-popup-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .code-popup-content {
    width: 400rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 16rpx;

    .img {
      width: 100%;
      border-radius: 16rpx;
    }
  }

  .close {
    width: 58rpx;
    height: 58rpx;
    margin-top: 40rpx;
    background: url(#{$assets}/activity/common/close-white.png) no-repeat center/cover;
  }
}

.fixed-code {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  width: 78rpx;
  height: 78rpx;
  right: 30rpx;
  bottom: 394rpx;
  border-radius: 37rpx;
  border: 2rpx solid #ffe9c3;
  background: #fff3df;
  color: #ffa000;
  text-align: center;
  padding: 0 8rpx;
  box-sizing: border-box;
  line-height: 28rpx;
  z-index: 9;
  font-size: 24rpx;
}

page {
  .back-top-trigger {
    bottom: 300rpx;
    z-index: 99;
  }
}

.fixed-bottom-wrapper {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: $color-white;

  .fixed-bottom-content {
    .content {
      display: flex;
      box-sizing: border-box;
      align-items: center;
      padding: 20rpx 30rpx 30rpx;
      border-top: 2rpx solid #e9e9e9;
    }
  }

  .share {
    padding: 54rpx 10rpx 0;
    margin-right: 20rpx;
    background: url(#{$assets}/activity/common/share.png) no-repeat center top/38rpx 38rpx;
    font-size: 26rpx;
    align-self: center;
    line-height: 1;
  }

  .apply-btn {
    margin-left: 20rpx;
    flex-grow: 1;
    padding: 0;
    line-height: 88rpx;
    border: none;
    background-color: #ffa000;
    color: $color-white;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 88rpx;

    &.is-disabled {
      color: $color-white;
      background-color: #ffd080;
    }
  }
}

.welfare-popup {
  padding: 50rpx 20rpx 30rpx 30rpx;
  background: linear-gradient(180deg, #fff1d8, #ffffff, #ffffff);
  border-radius: 24rpx 24rpx 0rpx 0rpx;

  .welfare-header {
    color: #dc833f;
    font-weight: bold;
    font-size: 36rpx;
    text-align: center;
    margin-bottom: 30rpx;
    position: relative;

    .close {
      position: absolute;
      right: 10rpx;
      top: -20rpx;
      width: 30rpx;
      height: 30rpx;
      background: url(#{$assets}/activity/common/close.png) no-repeat center/contain;
    }
  }

  .welfare-content {
    max-height: 800rpx;

    .welfare-common {
      display: flex;
      flex-direction: column;

      .welfare-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 12rpx;
        line-height: 48rpx;
        padding-left: 41rpx;
        background: url(#{$assets}/activity/special/welfare-item.png) no-repeat left 10rpx/28rpx 28rpx;
      }

      .welfare-body {
        font-size: 28rpx;
        line-height: 42rpx;
      }

      & + .welfare-common {
        margin-top: 34rpx;
      }
    }
  }

  .close-btn {
    width: 642rpx;
    height: 88rpx;
    background: #ffa000;
    border-radius: 44rpx;
    color: $color-white;
    margin-top: 30rpx;
  }
}

.area-popup-tips {
  color: $font-color-label;
  font-size: 24rpx;
  padding: 0 30rpx;
  margin-top: 20rpx;
  margin-bottom: -8rpx;
}
