<!-- 调用此组件务必在引用处添加id="new-nav-bar"，因为在chat-socket.ts文件需要调用其方法 -->
<new-nav-bar id="new-nav-bar" title="{{ detail.name }}" type="1" bind:clickNav="checkLogin"></new-nav-bar>

<scroll-view class="scroll-view" scroll-into-view="{{ scrollIntoView }}" scroll-into-view-offset="{{ -30 }}" scroll-top="{{ scrollTop }}" scroll-with-animation="{{ scrollWithAnimation }}" scroll-y style="height:{{ scrollViewHeight }}px;" enhanced enable-passive show-scrollbar="{{ false }}" bind:scroll="scroll" bind:scrolltolower="scrolltolower" using-sticky>
  <view class="cover-wrapper">
    <!-- 待举办：await
    即将进行：await-start
    进行中：start
    已结束：ended -->
    <!-- 状态；1:待举办；2：报名中；3:进行中；4:已结束 -->
    <view class="status status-{{ detail.status }}">{{ detail.statusTxt }}</view>
    <image class="img" src="{{ detail.imageMiniBannerUrl }}" mode="widthFix" lazy-load="false" />
  </view>

  <view class="transform-wrapper">
    <view class="detail-wrapper">
      <view class="title">{{ detail.name }}</view>

      <view class="tag-content">
        <!--线下： type  -->
        <view class="tag {{ item.type == 1 ? 'type' : '' }}" wx:for="{{ detail.tagList }}" wx:key="index">{{ item.value }}</view>
      </view>

      <view class="detail">
        <view class="list organization" wx:if="{{ detail.eventOrganization }}">
          <view class="label">活动组织：</view>
          <view class="content ellipsis">{{ detail.eventOrganization }}</view>
        </view>
        <view class="list time">
          <view class="label">活动时间：</view>
          <view class="content">{{ detail.activityDateText }}</view>
        </view>
        <view class="list address"> <view class="label">活动地点：</view>{{ detail.addressText }}</view>
        <view class="list type" wx:if="{{ detail.typeText }}">
          <view class="label">活动系列：</view>
          <view class="content">{{ detail.typeText }}</view>
        </view>
        <view class="list session">
          <view class="label">活动场次：</view>
          <view class="content">
            <view class="item">
              <view class="total">{{ detail.realParticipationActivityAmount }} </view>场
            </view>
            <view class="item company">
              <view class="label">参会单位：</view>
              <block wx:if="{{ detail.realParticipationCompanyAmount > 10 }}">
                <view class="total">{{ detail.realParticipationCompanyAmount }}</view
                >家
                <view class="tips">持续更新中</view>
              </block>
              <block wx:else>更新中</block>
            </view>
          </view>
        </view>
      </view>

      <view wx:if="{{ detail.participationBenefit }}" class="welfare {{ hasWelfareDetailHtml ? 'has-detail' : '' }}" bind:tap="openWelfarePopup">
        <view class="content">{{ detail.participationBenefit }}</view>

        <i class="arrow" wx:if="{{ hasWelfareDetailHtml }}"></i>
      </view>
    </view>

    <view class="tabs-wrapper {{ isTabFixed ? 'is-fixed' : '' }} {{ activeTab === 'company' ? 'is-company' : '' }}" id="tabs-wrapper">
      <view class="tabs-nav {{ activeTab === 'detail' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="detail">
        <view class="text">活动详情</view>
      </view>
      <view wx:if="{{ detail.realParticipationActivityAmount > 1 }}" class="tabs-nav {{ activeTab === 'session' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="session">
        <view class="text">场次安排</view>
      </view>
      <view wx:if="{{ detail.realParticipationCompanyAmount > 1 }}" class="tabs-nav {{ activeTab === 'company' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="company">
        <view class="text">参会单位</view>
      </view>
      <view class="tabs-nav {{ activeTab === 'review' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="review" wx:if="{{ reviewHtml }}">
        <view class="text">往届回顾</view>
      </view>
    </view>

    <view class="tabs-pane-wrapper" id="tabs-pane-wrapper">
      <view class="tabs-pane detail-pane" wx:if="{{ activeTab === 'detail' }}">
        <mp-html container-style="word-break: break-word" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ activityDetailHtml }}" bind:linktap="handleHtmlTap" copy-link="{{ copyLink }}" />

        <!-- <view class="common-wrapper">
          <view class="common-title-content">
            <view class="common-title">
              <text class="title">活动背景</text>
            </view>
          </view>

          <view class="common-content"> 上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅，为帮助更多海内外高层次人才与优质单位广泛对接，下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。步履不停，载梦前行，更多招聘场次“职”等您来解锁！等您来解锁 </view>
        </view>

        <view class="common-wrapper">
          <view class="common-title-content">
            <view class="common-title">
              <text class="title">参会对象</text>
            </view>
          </view>

          <view class="common-content">
            <view class="common-second-wrapper">
              <view class="common-second-title">01、参会人才</view>
              <view class="common-second-content"> 海内外应往届硕士/博士毕业生、具有硕士/博士学位的社会在职人才、博士后出站人员、中高级职称人才等。</view>
            </view>

            <view class="common-second-wrapper">
              <view class="common-second-title">02、参会单位</view>
              <view class="common-second-content"> 全国各人社厅/局、高校、科研院所、医院、企事业单位等有高层次人才引进需求的单位。</view>
            </view>
          </view>
        </view> -->
      </view>

      <view class="tabs-pane session-pane" wx:if="{{ activeTab === 'session' }}">
        <view class="wrapper-name">活动时间</view>
        <view class="session-order-wrapper">
          <!-- 月份维度 -->
          <!-- monthStatus: 当前月份解释；1:历史月；2:当前月；3:未来月;4:时间待定 -->
          <view class="month-list {{ month.monthStatus == 1 ? 'has-history' : '' }}" wx:for="{{ sessionList }}" wx:key="index" wx:for-item="month">
            <view class="month-title-content">
              <view class="month-title">
                <view class="month">{{ month.monthStatus == 4 ? '待定' : month.month + '月' }}</view>
                <view class="year {{ month.year ? '' : 'is-opacity' }}">{{ month.year }}</view>
              </view>

              <view class="month-total">
                <block wx:if="{{ month.monthStatus == 1 }}">
                  共计 <view class="total">{{ month.activityAmount }}</view> 场；
                  <view wx:if="{{ month.statusMap.inProgress != 0 }}">{{ month.statusMap.inProgress }} 场进行中 </view>
                </block>
                <block wx:elif="{{ month.monthStatus == 2 }}">
                  <block wx:if="{{ month.statusMap.toBeHeld != 0 }}">
                    共计 <view class="total">{{ month.statusMap.toBeHeld }}</view
                    >场待举办
                  </block>
                  <block wx:else>
                    共计 <view class="total">{{ month.activityAmount }}</view> 场；
                    <view wx:if="{{ month.statusMap.inProgress != 0 }}">{{ month.statusMap.inProgress }} 场进行中 </view>
                  </block>
                </block>
                <block wx:else>
                  共计 <view class="total">{{ month.statusMap.toBeHeld }}</view
                  >场待举办
                </block>
              </view>
            </view>

            <!-- 日维度 -->
            <view class="day-session" wx:for="{{ month.activityDate }}" wx:for-item="day" wx:key="indexDay" wx:for-index="indexDay">
              <!-- 场次维度 -->
              <!-- 已结束：is-end -->
              <view class="session-item {{ item.activityChildStatus == 4 ? 'is-end' : '' }}" wx:for="{{ day.activityList }}" wx:key="index" id="{{ item.scrollView == 1 ? 'lately-session' : '' }}" bind:tap="openDetail" data-id="{{ item.activityId }}">
                <view class="start-info" wx:if="{{ item.startCountDown }}">
                  <view class="title">
                    距离下一站【<view class="site">{{ item.activityShort || item.name }}</view
                    >】开始还有
                  </view>
                  <view class="countdown">
                    <view class="box">{{ sessionStartCountDown.day }}</view
                    >天 <view class="box">{{ sessionStartCountDown.hours }}</view
                    >时 <view class="box">{{ sessionStartCountDown.min }}</view
                    >分 <view class="box">{{ sessionStartCountDown.second }}</view
                    >秒
                  </view>
                </view>

                <view class="session-box">
                  <view class="aside-date">
                    <block wx:if="{{ index === 0 }}">
                      <view class="date {{ item.activityChildStatus == 4 ? 'is-end' : '' }}">{{ day.dateText }}</view>
                      <view class="number" wx:if="{{ day.activityList.length > 1 }}">{{ day.activityList.length }}场</view>
                    </block>
                  </view>

                  <view class="line" wx:if="{{ index === 0 }}"></view>

                  <view class="session-content">
                    <view class="session-detail">
                      <!-- 活动子状态；1:待举办；2:即将开始；3:进行中；4:已结束 -->
                      <view class="status status-{{ item.activityChildStatus }}">{{ item.activityChildStatusText }}</view>

                      <view class="info">
                        <view class="name">{{ item.activityShort || item.name }}</view>
                        <view class="time">{{ item.dateText }}</view>
                        <view class="address">{{ item.addressText }}</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="session-order-wrapper is-end-wrapper" wx:if="{{ historySessionList.length }}">
          <!-- 已结束 -->
          <view class="end-title"> 以下场次已结束，还有更多精彩场次等着你 快来一起参与吧！ </view>
          <view class="month-list {{ month.monthStatus == 1 ? 'has-history' : '' }}" wx:for="{{ historySessionList }}" wx:key="index" wx:for-item="month">
            <view class="month-title-content">
              <view class="month-title">
                <view class="month">{{ month.monthStatus == 4 ? '待定' : month.month + '月' }}</view>
                <view class="year {{ month.year ? '' : 'is-opacity' }}">{{ month.year }}</view>
              </view>
            </view>

            <!-- 日维度 -->
            <view class="day-session" wx:for="{{ month.activityDate }}" wx:for-item="day" wx:key="indexDay" wx:for-index="indexDay">
              <!-- 场次维度 -->
              <!-- 已结束：is-end -->
              <view class="session-item is-end" wx:for="{{ day.activityList }}" wx:key="index" bind:tap="openDetail" data-id="{{ item.activityId }}">
                <view class="session-box">
                  <view class="aside-date">
                    <block wx:if="{{ index === 0 }}">
                      <view class="date {{ item.activityChildStatus == 4 ? 'is-end' : '' }}">{{ day.dateText }}</view>
                      <view class="number" wx:if="{{ day.activityList.length > 1 }}">{{ day.activityList.length }}场</view>
                    </block>
                  </view>

                  <view class="line" wx:if="{{ index === 0 }}"></view>

                  <view class="session-content">
                    <view class="session-detail">
                      <!-- 活动子状态；1:待举办；2:即将开始；3:进行中；4:已结束 -->
                      <view class="status status-{{ item.activityChildStatus }}">{{ item.activityChildStatusText }}</view>

                      <view class="info">
                        <view class="name">{{ item.activityShort || item.name }}</view>
                        <view class="time">{{ item.dateText }}</view>
                        <view class="address">{{ item.addressText }}</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="tabs-pane company-pane" wx:if="{{ activeTab === 'company' }}">
        <view class="wrapper-tips">仅展示部分单位及招聘需求，欲知更多单位详情，请持续关注或现场了解</view>

        <view class="company-wrapper" id="company-wrapper">
          <view class="aside" style="height: {{ companyAsideHeight }}px">
            <view class="item {{ (item.activityId ? item.activityId : '') == activityId ? 'active' : '' }}" wx:for="{{ activityList }}" wx:key="index" bind:tap="switchSpecial" data-detail="{{ item }}">
              <view class="name">{{ item.activityName }}</view>
              <view class="status" wx:if="{{ item.activityChildStatus == 4 }}">已结束</view>
            </view>
          </view>

          <view class="company-box">
            <view class="filter-content">
              <view class="filter-item  {{ formData.areaId.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="area">{{ label.areaId }}</view>
              <view class="filter-item {{ formData.majorId.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="major">{{ label.majorId }}</view>
              <view class="filter-item {{ formData.categoryId.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="categoryId">{{ label.categoryId }}</view>
              <view class="filter-item {{ formData.type.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="type">{{ label.type }}</view>
            </view>

            <view class="company-content">
              <view class="session-detail" wx:if="{{ activityDetail.activityId }}">
                <view class="name">{{ activityDetail.name }}</view>

                <view class="countdown">
                  <block wx:if="{{ activityDetail.startCountDown }}">
                    距活动开始：
                    <view class="box">{{ activityStartCountDown.day }}</view
                    >天 <view class="box">{{ activityStartCountDown.hours }}</view
                    >时 <view class="box">{{ activityStartCountDown.min }}</view
                    >分 <view class="box">{{ activityStartCountDown.second }}</view
                    >秒
                  </block>
                  <block wx:else>
                    <view class="await" wx:if="{{ activityDetail.activityChildStatus == 1 }}"> — 待举办，敬请关注 — </view>
                    <view class="await" wx:if="{{ activityDetail.activityChildStatus == 3 }}"> — 正在进行中 — </view>
                    <view class="await" wx:if="{{ activityDetail.activityChildStatus == 4 }}"> — 已结束 — </view>
                  </block>
                </view>
                <view class="time">活动时间：{{ activityDetail.dateText }}</view>
                <view class="address">
                  <view class="label">活动地址：</view>
                  <view> {{ activityDetail.addressText }} </view>
                </view>
              </view>

              <view class="company-list">
                <view class="item" wx:for="{{ companyList }}" wx:key="index" bind:tap="companyClick" data-item="{{ item }}">
                  <view class="detail">
                    <view class="logo {{ item.isTop == 1 ? 'is-vip' : '' }}">
                      <image class="img" src="{{ item.logoUrl }}" mode="aspectFit" />
                    </view>

                    <view class="info">
                      <view class="name">{{ item.name }}</view>
                      <view class="middle">{{ item.cardTag }}</view>
                      <view class="bottom" wx:if="{{ item.cardTag2 }}">招 | {{ item.cardTag2 }}</view>
                    </view>
                  </view>

                  <view class="welfare" wx:if="{{ item.cardTag3.type == 1 }}">{{ item.cardTag3.value }}</view>
                  <view class="notice" wx:if="{{ item.cardTag3.type == 3 }}">{{ item.cardTag3.value }} </view>
                  <view class="major" wx:if="{{ item.cardTag3.type == 2 }}">{{ item.cardTag3.value }} </view>
                  <view class="update" wx:if="{{ item.cardTag3.type == 4 }}">{{ item.cardTag3.value }} </view>
                </view>

                <view class="tips" wx:if="{{ isEnd && companyList.length }}">参会单位持续更新中...</view>
              </view>

              <view class="empty" wx:if="{{ !companyList.length }}"> 更多单位持续更新中，敬请关注... </view>
            </view>
          </view>
        </view>
      </view>

      <view class="tabs-pane review-pane" wx:if="{{ activeTab === 'review' }}">
        <mp-html container-style="word-break: break-word" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ reviewHtml }}" bind:linktap="handleHtmlTap" copy-link="{{ copyLink }}" />
      </view>
    </view>
  </view>
</scroll-view>

<view wx:if="{{ showBackTop }}" class="back-top-trigger" bind:tap="backTop"></view>

<t-popup visible="{{ codePopupVisible }}" usingCustomNavbar placement="center" t-class="code-popup">
  <view class="code-popup-wrapper">
    <view class="code-popup-content">
      <image class="img" show-menu-by-longpress src="{{ detail.imageServiceCodeUrl }}" mode="widthFix" lazy-load />
    </view>
    <view class="close" bind:tap="closeCodePopup"></view>
  </view>
</t-popup>
<view wx:if="{{ detail.imageServiceCodeUrl }}" class="fixed-code" bind:tap="showCodePopup">活动进群</view>

<view class="fixed-bottom-wrapper">
  <!-- 参会单位单场逻辑---和活动详情逻辑一致 -->
  <view class="fixed-bottom-content" wx:if="{{ activeTab === 'company' && activityId != '' }}">
    <view class="content" wx:if="{{ activityDetail.companyCanApply == 1 || activityDetail.applyStatus != 3 }}">
      <button class="share" open-type="share">分享</button>
      <block wx:if="{{ activityDetail.activityChildStatus == 4 }}">
        <button class="apply-btn is-disabled" disabled>已结束</button>
      </block>
      <block wx:else>
        <button wx:if="{{ activityDetail.companyCanApply == 1 }}" class="apply-btn" bind:tap="handleApply" data-type="1">单位报名</button>
        <button wx:if="{{ activityDetail.applyStatus != 3 }}" class="apply-btn {{ activityDetail.applyStatus == 1 ? 'is-disabled' : '' }}" disabled="{{ activityDetail.applyStatus == 1 }}" bind:tap="handleApply" data-type="2">{{ activityDetail.applyStatus == 1 ? '已报名' : '人才报名' }}</button>
      </block>
    </view>
  </view>

  <view class="fixed-bottom-content" wx:else>
    <view class="content" wx:if="{{ detail.companyCanApply == 1 || detail.personCanApply == 1 }}">
      <button class="share" open-type="share">分享</button>
      <block wx:if="{{ detail.status == 4 }}">
        <button class="apply-btn is-disabled" disabled>已结束</button>
      </block>
      <block wx:else>
        <button class="apply-btn" wx:if="{{ detail.companyCanApply == 1 }}" bind:tap="handleApply" data-type="1">单位报名</button>
        <button class="apply-btn" wx:if="{{ detail.personCanApply == 1 }}" bind:tap="handleApply" data-type="2">人才报名</button>
      </block>
    </view>
  </view>
</view>

<t-popup visible="{{ welfareVisible }}" using-custom-navbar placement="bottom">
  <view class="welfare-popup">
    <view class="welfare-header">
      参会福利

      <i class="close" bind:tap="closeWelfarePopup"></i>
    </view>

    <scroll-view scroll-y class="welfare-content">
      <mp-html container-style="word-break: break-word" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ welfareDetailHtml }}" bind:linktap="handleHtmlTap" copy-link="{{ copyLink }}" />

      <!-- <view class="welfare-common">
        <view class="welfare-title">参会享交通福利</view>
        <view class="welfare-body">人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）</view>
      </view> -->
    </scroll-view>

    <button class="close-btn" bind:tap="closeWelfarePopup">我知道了</button>
  </view>
</t-popup>

<!-- 所在地区 -->
<basic-popup visible="{{ visible.area }}" model="{{ formData.areaId }}" title="所在地区" limit="{{ limit }}" column="3" options="{{ cityParams }}" options-label="name" options-value="id" bind:change="handleChange" data-form-key="areaId">
  <view slot="tips" class="area-popup-tips"> * 仅展示参会单位所在省份及部分热门城市 </view>
</basic-popup>

<!-- 需求学科 -->
<picker-popup visible="{{ visible.major }}" model="{{ formData.majorId }}" title="选择学科" limit="{{ limit }}" column="{{ 2 }}" options="{{ majorSelect }}" options-label="v" options-value="k" bind:change="handleChange" data-form-key="majorId" />

<!-- 职位类型 -->
<picker-popup visible="{{ visible.categoryId }}" model="{{ formData.categoryId }}" title="职位类型" limit="{{ limit }}" column="{{ 2 }}" options="{{ jobTypeList }}" options-label="v" options-value="k" bind:change="handleChange" data-form-key="categoryId" />

<!-- 单位类型 -->
<basic-popup visible="{{ visible.type }}" model="{{ formData.type }}" title="单位类型" limit="{{ limit }}" column="3" options="{{ typeParams }}" options-label="name" options-value="code" bind:change="handleChange" data-form-key="type" />
