import { handleTabBar } from '@/utils/tabBar'
import { getHomeShowcase } from '@/api/discover'
import { jump } from '@/utils/url'
import { getConfig, getLiveConfig } from '@/api/channel'
import { getChannelSphid, getCommonShareInfo } from '@/utils/store'
import { isShowDiscoverLive } from '@/api/config'
import { showLoading } from '@/utils/util'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    headerStyle: getApp().globalData.headerStyle,
    swiperInterval: 5000,
    bannerSwiper: {
      originalList: [],
      // 这个list是给图片显示的
      list: []
    },

    recommendSwiper: {
      originalList: [],
      // 这个list是给图片显示的
      list: []
    },

    recommendJobSwiper: {
      current: 1,
      originalList: [],
      list: []
    },

    recommendJobMore: {},
    hotCourseMore: {},

    // 热门课程
    hotCourse: <any>[],
    liveList: <any>[],
    noticeList: <any>[],
    navTab: <any>[],
    sphid: ''
  },

  jump(e: any) {
    const {
      currentTarget: {
        dataset: { item }
      }
    } = e
    jump(item.url, item.targetLinkType, item.id)
  },

  onTapBanner(e: any) {
    const { index } = e.detail
    const { originalList } = this.data.bannerSwiper
    const { url, targetLinkType, id } = originalList[index]
    jump(url, targetLinkType, id)
  },

  onTapRecommend(e: any) {
    const { index } = e.detail
    const { originalList } = this.data.recommendSwiper
    const { url, targetLinkType, id } = originalList[index]
    jump(url, targetLinkType, id)
  },

  onTapRecommendJob(e: any) {
    const { index } = e.detail
    const { originalList } = this.data.recommendJobSwiper
    const { url, targetLinkType, id } = originalList[index]
    jump(url, targetLinkType, id)
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 出loading
    this.initData()
  },

  async initData() {
    showLoading()

    let sphid = getChannelSphid()
    if (!sphid) {
      const rs = await getConfig()
      sphid = rs.sphid
    }
    this.setData({ sphid: sphid })
    const _this = this
    const config = await getLiveConfig()
    const isShowRs = await isShowDiscoverLive()

    if (isShowRs.isShow == 1) {
      wx.getChannelsLiveInfo({
        finderUserName: sphid,
        startTime: config.startTime,
        endTime: config.endTime,
        success(res: any) {
          const { otherInfos, ...lately } = res
          const arr = [lately, ...otherInfos]

          const onLiveList = arr.filter((item: any) => {
            return item.status === 2
          })

          const replayList = arr.filter((item: any) => {
            return item.replayStatus === 1
          })

          // 非直播中并且没有回放的
          const noReplayList = arr.filter((item: any) => {
            return item.status !== 2 && item.replayStatus !== 1
          })

          // 合并起来称为新的数组
          const liveList = [...onLiveList, ...replayList, ...noReplayList]

          _this.setData({
            liveList: liveList
          })
          wx.hideLoading()
        },
        fail() {
          wx.hideLoading()
        }
      })
    } else {
      wx.hideLoading()
    }

    // 经常性会出不来视频,所以做一个定时关闭loading,5s
    setTimeout(() => {
      wx.hideLoading()
    }, 5000)

    // this.getChannelsLiveInfo()
    // 奢装广告位信息
    this.setShowcase()
    // 关闭loading
  },

  // 设置广告位信息
  setShowcase() {
    const _this = this
    getHomeShowcase().then((r: any) => {
      _this.setData({ navTab: r.quickLink })
      _this.setData({ recommendJobMore: r.recommendJobMore })
      _this.setData({ hotCourseMore: r.hotCourseMore })

      _this.setData({ ['bannerSwiper.originalList']: r.banner })
      _this.setData({ ['recommendSwiper.originalList']: r.recommend })
      _this.setData({ ['recommendJobSwiper.originalList']: r.recommendJob })
      const banner = r.banner.reduce((acc: any, cur: any) => {
        if (cur.imageUrl) {
          acc.push(cur.imageUrl)
        }
        return acc
      }, [])
      const recommend = r.recommend.reduce((acc: any, cur: any) => {
        if (cur.imageUrl) {
          acc.push(cur.imageUrl)
        }
        return acc
      }, [])

      const recommendJob = r.recommendJob.reduce((acc: any, cur: any) => {
        if (cur.imageUrl) {
          acc.push(cur.imageUrl)
        }
        return acc
      }, [])

      _this.setData({ ['bannerSwiper.list']: banner })
      _this.setData({ ['recommendSwiper.list']: recommend })
      _this.setData({ ['recommendJobSwiper.list']: recommendJob })
      _this.setData({ ['hotCourse']: r.hotCourse })
    })
  },

  recommendJobChange(e: any) {
    const {
      detail: { current }
    } = e
    this.setData({
      ['recommendJobSwiper.current']: current
    })
  },

  jumpUrl(e: any) {
    const {
      currentTarget: {
        dataset: { url }
      }
    } = e
    wx.navigateTo({
      url
    })
  },

  handleBack() {
    wx.navigateBack()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    handleTabBar(this, { value: 'discover' })
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.initData()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage() {
    const rs = await getCommonShareInfo()
    return {
      title: rs.title,
      path: rs.path,
      imageUrl: rs.imageUrl
    }
  }
})
