@use 'styles/variables' as *;

.header {
  --td-spacer-1: 30rpx;

  .t-navbar__content {
    background: url('#{$assets}/nav/primary.png') no-repeat center top/cover;
  }
}

.discover-container {
  padding: 0 30rpx 30rpx;
  background: url('#{$assets}/nav/primary.png') no-repeat left -136rpx/100% auto;

  --td-swiper-nav-dot-size: 10rpx;
  --td-swiper-nav-dots-bar-active-width: 20rpx;
  --td-swiper-radius: $border-radius;

  .t-swiper-nav__dots-bar-item {
    margin: 0 5rpx;
  }

  .ad-swiper {
    margin-bottom: 20rpx;

    .t-image {
      border-radius: $border-radius;
    }

    .t-swiper-nav--bottom {
      bottom: 10rpx;
    }
  }

  .card {
    margin-top: 20rpx;
    background-color: #fff;
    padding: 0rpx 30rpx 20rpx;
    border-radius: $border-radius;

    .wrapper-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 22rpx 0;

      .label {
        font-size: 36rpx;
        font-weight: bold;
        padding-left: 42rpx;
        background: url('#{$assets}/icon/title.png') no-repeat left/28rpx;
      }

      .more {
        display: inline-flex;
        align-items: center;
        color: $font-color-label;

        .icon {
          margin-left: -4rpx;
        }
      }
    }
  }

  // 五大金刚
  .nav-tools {
    border-radius: $border-radius;
    background-color: #fff;
    padding: 0 8rpx;
    display: flex;
    margin-bottom: 20rpx;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30rpx 0 26rpx;
      font-size: 24rpx;

      .icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 16rpx;
      }
    }
  }

  .recommend {
    padding-bottom: 20rpx;

    .recommend-swiper {
      --td-swiper-nav-dot-color: #c7c7c7;
      --td-swiper-nav-dot-active-color: #{$color-primary};
      --td-image-round-radius: #{$border-radius};

      padding-bottom: 30rpx;

      .t-swiper-nav--bottom {
        bottom: 0rpx;
      }
    }
  }

  // 热门课程
  .hot-course {
    padding-bottom: 0;

    .hot-course {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .hot-course-list {
        margin-bottom: 30rpx;
        width: calc(50% - 20rpx);
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .cover-content {
          border-radius: $border-radius;
          overflow: hidden;
          height: 160rpx;
          width: 100%;

          .cover {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .title {
          height: 72rpx;
          margin-top: 12rpx;
          font-weight: bold;
          font-size: 28rpx;
          line-height: 36rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .sub-title {
          height: 48rpx;
          line-height: 48rpx;
          color: $font-color-label;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
        }

        .price-info {
          display: flex;
          font-size: 24rpx;
          align-items: center;
          overflow: hidden;
          width: 100%;

          .price {
            color: $color-primary;
            font-size: 36rpx;
            font-weight: bold;
            max-width: 166rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .discount {
            color: $color-primary;
            background-color: $tag-primary-background;
            font-size: 22rpx;
            max-width: 88rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 30rpx;
            padding: 0 12rpx;
            margin-left: 12rpx;
            border-radius: 4rpx;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  // 直播
  .hot-live {
    padding-right: 0;
    .more {
      padding-right: 30rpx;
    }
    .live-content {
      display: flex;
      overflow-x: scroll;

      &::-webkit-scrollbar {
        width: 0rpx;
      }

      .live-list {
        flex-shrink: 0;
        width: 335rpx;
        margin-right: 30rpx;

        .title {
          display: flex;
          font-size: 36rpx;
          margin-left: 0;
          justify-content: flex-start;
        }

        .desc {
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          margin-top: 10rpx;
        }
      }
    }
  }

  .direct-push {
    padding-bottom: 50rpx;

    .direct-push-content {
      position: relative;
      --td-swiper-nav-dot-color: #c7c7c7;
      --td-swiper-nav-dot-active-color: #{$color-primary};

      .direct-push-swiper {
        height: 150rpx;
      }

      .t-swiper-nav--bottom {
        bottom: -30rpx;
      }

      .t-swiper-nav__dots-bar-item {
        margin: 0 4rpx;
      }

      .swiper-list {
        display: flex;

        .detail {
          width: 400rpx;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 0 20rpx;
          box-sizing: border-box;
        }

        .cover {
          width: 230rpx;
          height: 150rpx;
          border-radius: $border-radius;
          margin-right: 24rpx;
          flex-shrink: 0;
        }

        .name {
          font-size: 32rpx;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 12rpx;
        }

        .tag-content {
          display: flex;
          flex-wrap: nowrap;
          margin-bottom: 12rpx;

          .tag {
            flex-shrink: 0;
            font-size: 24rpx;
            border-radius: 4rpx;
            line-height: 44rpx;
            margin-right: 14rpx;
            padding: 0 12rpx;
            background-color: $tag-primary-background;
            color: $color-primary;
          }
        }

        // 超过一行显示...
        .look {
          color: $font-color-basic;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}
