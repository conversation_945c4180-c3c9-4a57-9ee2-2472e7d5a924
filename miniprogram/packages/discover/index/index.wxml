<layout>
  <t-navbar style="{{ headerStyle }}" title="职场发现" left-arrow class="header" bind:go-back="handleBack" />

  <view class="discover-container">
    <block wx:if="{{ bannerSwiper.list.length }}">
      <t-swiper t-class="ad-swiper swiper" height="140rpx" autoplay interval="{{ swiperInterval }}" navigation="{{ { type: 'dots-bar' } }}" list="{{ bannerSwiper.list }}" bind:click="onTapBanner"></t-swiper>
    </block>

    <view class="nav-tools">
      <view class="nav-item" wx:for="{{ navTab }}" wx:key="key" bind:tap="jump" data-item="{{ item }}">
        <image class="icon" src="{{ item.imageUrl }}" mode="aspectFit" />
        <view>{{ item.title }}</view>
      </view>
    </view>

    <view class="card recommend" wx:if="{{ recommendSwiper.list.length }}">
      <view class="wrapper-title">
        <view class="label">优先推荐</view>
      </view>
      <t-swiper t-class="recommend-swiper swiper" height="250rpx" autoplay interval="{{ swiperInterval }}" image-props="{{ { mode: 'aspectFit', shape: 'round' } }}" navigation="{{ { type: 'dots-bar' } }}" list="{{ recommendSwiper.list }}" bind:click="onTapRecommend"> </t-swiper>
    </view>

    <view class="card hot-course" wx:if="{{ hotCourse.length }}">
      <view class="wrapper-title">
        <view class="label">热门课程</view>
        <view class="more" bind:tap="jump" data-item="{{ hotCourseMore }}">
          更多
          <t-icon t-class="icon" name="chevron-right" size="30rpx" />
        </view>
      </view>
      <view class="hot-course">
        <view class="hot-course-list" wx:for="{{ hotCourse }}" wx:key="key" bind:tap="jump" data-item="{{ item }}" wx:key="key">
          <view class="cover-content">
            <image class="cover" src="{{ item.imageUrl }}" mode="aspectFit" />
          </view>
          <view class="title">{{ item.title }}</view>
          <view class="sub-title">{{ item.subTitle }}</view>
          <view class="price-info">
            <view wx:if="{{ item.secondTitle != '' }}" class="price"> ￥{{ item.secondTitle }} </view>
            <view class="discount">{{ item.imageAlt }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- <view class="card hot-live" wx:if="{{ liveList.length }}">
      <view class="wrapper-title">
        <view class="label">热门直播</view>
        <view class="more" bind:tap="jumpUrl" data-url="/packages/discover/live/index">
          更多
          <t-icon t-class="icon" name="chevron-right" size="30rpx" />
        </view>
      </view>
      <view class="live-content">
        <view class="live-list" wx:for="{{ liveList }}" wx:key="feedId">
          <channel-live feed-id="{{ item.feedId }}" finder-user-name="{{ sphid }}" mode="playback"></channel-live>
          <view class="desc">{{ item.description }}</view>
        </view>
      </view>
    </view> -->

    <view class="card direct-push" wx:if="{{ recommendJobSwiper.originalList.length }}">
      <view class="wrapper-title">
        <view class="label">英才直推</view>
        <view class="more" bind:tap="jump" data-item="{{ recommendJobMore }}">
          更多
          <t-icon t-class="icon" name="chevron-right" size="30rpx" />
        </view>
      </view>
      <view class="direct-push-content">
        <swiper class="direct-push-swiper" bindchange="recommendJobChange" autoplay circular interval="{{ swiperInterval }}">
          <swiper-item class="swiper-list" wx:for="{{ recommendJobSwiper.originalList }}" wx:for-item="item" wx:for-index="index" wx:key="index" bindtap="jump" data-item="{{ item }}">
            <image class="cover" src="{{ item.imageUrl }}" mode="aspectFill" />
            <view class="detail">
              <view class="name">{{ item.title }}</view>
              <view class="tag-content">
                <view class="tag" wx:for="{{ item.tag }}" wx:for-item="tag" wx:key="tag">#{{ tag }}</view>
              </view>
              <view class="look">{{ item.secondTitle }}</view>
            </view>
          </swiper-item>
        </swiper>
        <t-swiper-nav total="{{ recommendJobSwiper.list.length }}" current="{{ recommendJobSwiper.current }}" type="dots-bar"></t-swiper-nav>
      </view>
    </view>

    <!-- <view class="card">
      <view class="wrapper-title">
        <view class="label">求职干货</view>
      </view>
    </view> -->
  </view>
  <login-dialog />
</layout>
