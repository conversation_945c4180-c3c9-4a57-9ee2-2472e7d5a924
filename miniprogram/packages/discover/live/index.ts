import { getConfig, getLiveConfig } from '@/api/channel'
import { isShowDiscoverLive } from '@/api/config'
import { getCommonShareInfo } from '@/utils/store'
import { getHotShowCase } from '@/api/discover'
import { toChineseTime } from '@/utils/day'
import { getChannelSphid } from '@/utils/store'
import { jump } from '@/utils/url'
import { showLoading } from '@/utils/util'
Page({
  /**featuredVideosTag
   * 页面的初始数据
   */
  data: <any>{
    headerStyle: getApp().globalData.headerStyle,
    swiperInterval: 5000,
    adSwiper: {
      current: 1,
      list: []
    },
    liveList: [],
    isShowAllAdvance: false,
    noticeList: [],
    sphid: '',
    channelImage: '',
    featuredVideos: [],
    showFeaturedVideos: [],
    featuredVideosTag: [],
    featuredVideosSelectIndex: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    let sphid = getChannelSphid()
    if (!sphid) {
      const rs = await getConfig()
      sphid = rs.sphid
    }
    this.setData({ sphid })
    this.getChannelsLiveInfo()
    this.getChannelsLiveNoticeInfo()
    this.setShowcase()
  },

  getChannelsLiveNoticeInfo() {
    const _this = this
    // 出loading
    showLoading()
    wx.getChannelsLiveNoticeInfo({
      finderUserName: _this.data.sphid,
      success(r) {
        const first = {
          headUrl: r.headUrl,
          nickname: r.nickname,
          noticeId: r.noticeId,
          reservable: r.reservable,
          startTime: r.startTime,
          status: r.status
        }

        const arr = [first, ...r.otherInfos]

        arr.forEach((item: any) => {
          // 时间戳
          const startTime = item.startTime

          item.time = toChineseTime(startTime)

          return item
        })

        // 过滤掉已经开始的
        let tmpArr = arr.filter((item: any) => {
          return item.startTime * 1000 > Date.now()
        })

        // 最后再截取前15个
        tmpArr = tmpArr.slice(0, 15)

        _this.setData({ noticeList: tmpArr })
        wx.hideLoading()
      },
      fail() {
        wx.hideLoading()
      }
    })
  },

  async getChannelsLiveInfo() {
    // 获取直播信息
    const sphid = this.data.sphid
    const _this = this

    const config = await getLiveConfig()
    const isShowRs = await isShowDiscoverLive()

    if (isShowRs.isShow == 1) {
      wx.getChannelsLiveInfo({
        finderUserName: sphid,
        startTime: config.startTime,
        endTime: config.endTime,
        success(res: any) {
          const { otherInfos, ...lately } = res
          const arr = [lately, ...otherInfos]

          const onLiveList = arr.filter((item: any) => {
            return item.status === 2
          })

          const replayList = arr.filter((item: any) => {
            return item.replayStatus === 1
          })

          // 非直播中并且没有回放的
          const noReplayList = arr.filter((item: any) => {
            return item.status !== 2 && item.replayStatus !== 1
          })

          // 合并起来称为新的数组
          const liveList = [...onLiveList, ...replayList, ...noReplayList]

          _this.setData({
            liveList: liveList
          })
          wx.hideLoading()
        },
        fail() {
          wx.hideLoading()
        }
      })

      // 经常性会出不来视频,所以做一个定时关闭loading,5s
      setTimeout(() => {
        wx.hideLoading()
      }, 5000)
    } else {
      wx.hideLoading()
    }
  },

  handleSubscribe(e: any) {
    const noticeId = e.currentTarget.dataset.noticeid
    // 弹出预告
    wx.reserveChannelsLive({ noticeId })
  },

  adChange(e: any) {
    const { current } = e.detail
    this.setData({
      'adSwiper.current': current
    })
  },

  handlewitchAdvance() {
    const { isShowAllAdvance } = this.data
    this.setData({ isShowAllAdvance: !isShowAllAdvance })
  },

  jumpChannelMain() {
    // 跳转到视频号主页
    const sphid = this.data.sphid
    wx.openChannelsUserProfile({ finderUserName: sphid })
  },

  setShowcase() {
    getHotShowCase().then((res: any) => {
      this.setData({
        'adSwiper.list': res.banner,
        channelImage: res.channelImage
      })
      const featuredVideosTag = res.featuredVideos.reduce((acc: any, cur: any) => {
        if (cur.list.length > 0) {
          return [...acc, { class: 'tag', name: cur.name }]
        }
        return acc
      }, [])
      const showFeaturedVideos = res.featuredVideos.reduce((acc: any, cur: any) => {
        return [...acc, cur.list]
      }, [])
      // 设置第一个为选中
      if (featuredVideosTag.length > 0) {
        featuredVideosTag[0].class = 'tag select'
      }
      this.setData({
        featuredVideosTag: featuredVideosTag,
        featuredVideos: showFeaturedVideos,
        showFeaturedVideos: showFeaturedVideos[0]
      })
    })
  },

  // tag点击切换
  handleTagChange(e: any) {
    const { index } = e.currentTarget.dataset
    // 切换class
    const { featuredVideosTag } = this.data
    featuredVideosTag.forEach((item: any, i: number) => {
      if (i === index) {
        item.class = 'tag select'
      } else {
        item.class = 'tag'
      }
    })

    this.setData({ ['featuredVideosTag']: featuredVideosTag })
    this.setData({ ['showFeaturedVideos']: this.data.featuredVideos[index] })
  },

  jump(e: any) {
    const item = e.currentTarget.dataset.item
    jump(item.url, item.targetLinkType, item.id)
  },

  onBack() {
    wx.navigateBack()
  },

  goHome() {
    wx.switchTab({ url: '/pages/home/<USER>' })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // this.setShowcase()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage() {
    const rs = await getCommonShareInfo()
    return {
      title: rs.title,
      path: rs.path,
      imageUrl: rs.imageUrl
    }
  }
})
