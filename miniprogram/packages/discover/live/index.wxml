<view class="live-container">
  <t-navbar class="header" title="热门直播" style="{{ headerStyle }}">
    <view slot="capsule" class="custom-capsule">
      <t-icon size="20" bind:tap="onBack" aria-role="button" aria-label="返回" name="chevron-left" class="custom-capsule__icon" />
      <view class="home-icon custom-capsule__icon" bind:tap="goHome"></view>
    </view>
  </t-navbar>

  <view class="ad-banner" wx:if="{{ adSwiper.list.length }}">
    <swiper class="swiper" circular autoplay interval="{{ swiperInterval }}" previous-margin="74rpx" next-margin="74rpx" bindchange="adChange">
      <swiper-item class="swiper-item" wx:for="{{ adSwiper.list }}" wx:key="key" data-item="{{ item }}" bind:tap="jump">
        <view class="cover-content">
          <image class="cover" src="{{ item.imageUrl }}" mode="aspectFit" />
        </view>
      </swiper-item>
    </swiper>
    <t-swiper-nav current="{{ adSwiper.current }}" total="{{ adSwiper.list.length }}" type="dots-bar"></t-swiper-nav>
  </view>

  <view class="live-content">
    <view class="wrapper channel-wrapper">
      <view class="channel" bindtap="jumpChannelMain">
        <image class="" src="{{ channelImage }}" mode="widthFix" />
      </view>

      <view class="content" wx:if="{{ noticeList.length }}">
        <view class="advance-title">直播预告</view>
        <view class="advance-content">
          <view class="advance-list {{ isShowAllAdvance ? 'show' : '' }}">
            <view class="list" wx:for="{{ noticeList }}" wx:for-item="item" wx:for-index="index" bindtap="handleSubscribe" wx:key="key" data-noticeId="{{ item.noticeId }}">
              <view class="title">{{ item.time }}直播</view>
              <view class="subscribe">预约</view>
            </view>
          </view>
          <view wx:if="{{ noticeList.length > 3 }}" class="switch-all" bindtap="handlewitchAdvance">{{ isShowAllAdvance ? '收起' : '全部直播预告' }}<t-icon name="{{ isShowAllAdvance ? 'chevron-up' : 'chevron-down' }}"></t-icon></view>
        </view>
      </view>
    </view>

    <view class="wrapper hot-wrapper" wx:if="{{ liveList.length }}">
      <view class="wrapper-title">
        <view class="label">热门直播</view>
      </view>
      <view class="content">
        <view class="live-list" wx:for="{{ liveList }}" wx:for-item="item" wx:for-index="index" wx:key="feedId">
          <channel-live feed-id="{{ item.feedId }}" finder-user-name="{{ sphid }}"></channel-live>
          <view class="desc">{{ item.description }}</view>
        </view>
      </view>
    </view>

    <view class="wrapper choiceness-wrapper" wx:if="{{ showFeaturedVideos.length }}">
      <view class="wrapper-title">
        <view class="label">精选视频</view>
      </view>
      <view class="tag-content">
        <view class="{{ tag.class }}" bindtap="handleTagChange" data-index="{{ index }}" wx:for="{{ featuredVideosTag }}" wx:for-item="tag" wx:for-index="index" wx:key="tag">#{{ tag.name }}</view>
      </view>
      <view class="content">
        <view class="list" bind:tap="jump" data-item="{{ item }}" wx:for="{{ showFeaturedVideos }}" wx:for-item="item" wx:for-index="index" wx:key="index">
          <image class="cover" mode="aspectFit" src="{{ item.imageUrl }}"></image>
          <view class="desc">{{ item.title }}</view>
        </view>
      </view>
    </view>
  </view>
</view>
