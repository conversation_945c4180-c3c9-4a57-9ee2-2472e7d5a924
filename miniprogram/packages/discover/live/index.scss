@use 'styles/variables' as *;

.live-container {
  min-height: 100vh;
  background-color: $page-background;
}

.header {
  .home-icon {
    width: 30rpx;
    height: 30rpx;
    background: url(#{$assets}/icon/home.png) no-repeat center/contain;
  }
}

.ad-banner {
  position: relative;
  --td-swiper-nav-dot-size: 10rpx;
  --td-swiper-nav-dots-bar-active-width: 20rpx;
  --td-swiper-radius: $border-radius;
  --td-swiper-nav-dot-color: #c7c7c7;
  --td-swiper-nav-dot-color: rgba(255, 255, 255, 0.5);

  .swiper {
    margin: 20rpx 0 0;
    height: 260rpx;

    .swiper-item {
      width: 100%;
      height: 100%;
      padding: 0 10rpx;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
    }

    .cover-content {
      display: flex;
      align-items: center;
      border-radius: $border-radius;
      overflow: hidden;
      width: 580rpx;
      height: 260rpx;

      .cover {
        height: 100%;
      }
    }
  }

  .t-swiper-nav--bottom {
    bottom: 10rpx;
  }

  .t-swiper-nav__dots-bar-item {
    margin: 0 5rpx;
  }
}

.live-content {
  padding: 20rpx 30rpx 60rpx;

  .wrapper {
    padding-left: 30rpx;
    padding-right: 30rpx;
    padding-bottom: 20rpx;
    background-color: #fff;
    border-radius: $border-radius;
    margin-bottom: 20rpx;

    .wrapper-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 22rpx 0;

      .label {
        font-size: 36rpx;
        font-weight: bold;
        padding-left: 42rpx;
        background: url('https://img.gaoxiaojob.com/uploads/mini/icon/title.png') no-repeat left/28rpx;
      }

      .more {
        display: inline-flex;
        align-items: center;
        color: var(--font-color-label);

        .icon {
          margin-left: -4rpx;
        }
      }
    }
  }
}

.channel-wrapper {
  padding-bottom: 0 !important;

  .channel {
    padding: 30rpx 0;

    image {
      width: 100%;
      height: auto;
    }
  }

  .content {
    background: linear-gradient(180deg, #fff5e3, #ffffff);
    border-radius: 16rpx 16rpx 0rpx 0rpx;
    padding: 0 20rpx 0;
    margin-top: 8rpx;

    .advance-title {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 70rpx;
    }
  }

  .advance-content {
    background-color: #fff;
    padding: 0 20rpx;
    border-radius: $border-radius;
  }

  .advance-list {
    max-height: 282rpx;
    overflow: hidden;

    &.show {
      max-height: none;
    }
  }
  .list {
    display: flex;
    font-size: 26rpx;
    align-items: center;
    padding: 28rpx 0;
    border-bottom: 2rpx solid var(--border-color);
    padding-left: 40rpx;
    background: url('https://img.gaoxiaojob.com/uploads/mini/discover/live-dot.png') no-repeat left/26rpx;

    &:last-child {
      border-bottom: none;
    }

    .title {
      flex-grow: 1;
      font-size: 26rpx;
      font-weight: bold;
    }

    .subscribe {
      flex-shrink: 0;
      color: var(--color-primary);
    }
  }

  .switch-all {
    color: var(--color-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16rpx 0;
  }
}

.hot-wrapper {
  padding-right: 0;
  .content {
    display: flex;
    overflow-x: scroll;

    &::-webkit-scrollbar {
      width: 0rpx;
    }

    .live-list {
      flex-shrink: 0;
      width: 335rpx;
      margin-right: 30rpx;

      .title {
        display: flex;
        font-size: 36rpx;
        margin-left: 0;
        justify-content: flex-start;
      }

      .desc {
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-top: 10rpx;
      }
    }
  }
}

.choiceness-wrapper {
  padding-right: 0 !important;

  // 可以左右滑动
  .tag-content {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: scroll;
    margin-bottom: 30rpx;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      width: 0rpx;
      display: none;
    }

    .tag {
      flex-shrink: 0;
      font-size: 24rpx;
      border-radius: 4rpx;
      line-height: 52rpx;
      height: 52rpx;
      margin-right: 20rpx;
      padding: 0 20rpx;
      color: #333333;
      background-color: #f7f7f7;

      &.select {
        color: var(--color-primary);
        background-color: var(--tag-primary-background);
      }
    }
  }

  .content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .list {
    width: 300rpx;
    flex-shrink: 0;
    margin-bottom: 26rpx;
    margin-right: 30rpx;

    .cover {
      width: 300rpx;
      height: 400rpx;
      overflow: hidden;
      border-radius: 12rpx;
    }

    .desc {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: bold;
    }
  }
}
