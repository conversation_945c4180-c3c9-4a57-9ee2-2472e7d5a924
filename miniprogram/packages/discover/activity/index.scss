@use 'styles/variables' as *;

$color-primary: #296aff;

@mixin common-wrapper-rich {
  .common-wrapper {
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 10rpx 0;
    border-radius: 12rpx;

    .not-bg {
      background: transparent !important;
      margin-bottom: 0 !important;
    }

    .pd-30 {
      padding-top: 30rpx !important;
    }

    .pd-50 {
      padding-top: 50rpx !important;
    }

    .common-title-content {
      display: inline-block;
      position: relative;
      left: 50%;
      top: 0;
      z-index: 10;
      transform: translate(-50%, 28rpx) skewX(-20deg);

      // &::before {
      //   position: relative;
      //   right: -80rpx;
      //   top: -8rpx;
      //   content: '';
      //   width: 114rpx;
      //   height: 32rpx;
      // }

      // &::after {
      //   position: relative;
      //   z-index: 2;
      //   align-self: flex-end;
      //   bottom: -8rpx;
      //   right: 20rpx;
      //   content: '';
      //   width: 47rpx;
      //   height: 26rpx;
      // }

      .common-title {
        display: inline-block;
        position: relative;
        left: 50%;
        z-index: 10;
        max-width: 380rpx;
        line-height: 56rpx;
        color: $color-white;
        transform: translate(-50%);
        padding-top: 10rpx;
        background: linear-gradient(to right, transparent 0, transparent 10rpx, #2cc6af 10rpx, #f7faff 100%);

        &::after {
          content: '';
          display: block;
          position: absolute;
          top: 10rpx;
          bottom: 0;
          left: -14rpx;
          width: 8rpx;
          // height: 100%;
          background: #ffa000;
        }

        &::before {
          content: '';
          // width: 100%;
          // height: 100%;
          top: 10rpx;
          bottom: 0;
          left: 0;
          right: 10rpx;
          position: absolute;
          // z-index: -1;
          // transform: translate(10rpx, 0rpx);
          background: linear-gradient(90deg, #2a6bff, #25c9e6);
        }

        .title {
          display: block;
          padding: 0 30rpx;
          font-weight: bold;
          font-size: 36rpx;
          transform: skewX(20deg);
        }
      }
    }

    .common-content {
      line-height: 2;
      font-size: 30rpx;
      // background: #f4f6fb;

      .common-content-text {
        display: block;
        background: #f4f6fb;
        // padding-top: 50rpx;
        border-radius: 16rpx;
        padding: 20rpx 30rpx;
        margin-bottom: 30rpx;
      }

      .common-second-wrapper {
        display: flex;
        flex-direction: column;
        background: #f4f6fb;
        border-radius: 16rpx;
        padding: 20rpx 30rpx;
        margin-bottom: 30rpx;
        // &:first-child {
        //   padding-top: 50rpx;
        // }

        .common-second-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 32rpx;
          line-height: 1.5;
          font-weight: bold;
          margin-bottom: 8rpx;

          &::before {
            content: '';
            display: block;
            width: 16rpx;
            height: 16rpx;
            margin-right: 12rpx;
            background: linear-gradient(45deg, #ffa000, #ff5a00);
            border-radius: 2rpx;
            transform: rotate(45deg);
          }

          &::after {
            content: '';
            display: block;
            width: 16rpx;
            height: 16rpx;
            margin-left: 12rpx;
            background: linear-gradient(45deg, #ffa000, #ff5a00);
            border-radius: 2rpx;
            transform: rotate(45deg);
          }
        }

        .common-second-content {
          display: block;
          font-size: 28rpx;
        }
      }
    }
  }
}

.cover-wrapper {
  position: relative;

  .status {
    position: absolute;
    top: -0;
    left: 0;
    color: $color-white;
    font-size: 24rpx;
    padding-top: 3rpx;
    min-width: 128rpx;
    height: 53rpx;
    text-align: center;

    // $status: await, await-start, start, ended;
    // 状态；1:待举办；2：报名中；3:进行中；4:已结束

    $status: (
        name: status-1,
        img: await
      ),
      (
        name: status-2,
        img: await-start
      ),
      (
        name: status-3,
        img: start
      ),
      (
        name: status-4,
        img: ended
      );

    // @each $name in $status {
    @each $item in $status {
      $name: map-get(
        $map: $item,
        $key: 'name'
      );
      $img: map-get(
        $map: $item,
        $key: 'img'
      );

      @if $name == status-3 {
        &.#{$name} {
          text-align: left;
          padding-left: 43rpx;
          background: url(#{$assets}/activity/common/play.gif) no-repeat 13rpx 9rpx/22rpx 22rpx,
            url(#{$assets}/activity/special/#{$img}-bg.png) no-repeat left top/128rpx 53rpx;
        }
      } @else {
        &.#{$name} {
          background: url(#{$assets}/activity/special/#{$img}-bg.png) no-repeat left top/128rpx 53rpx;
        }
      }
    }
  }

  .img {
    display: block;
    width: 100%;
    height: auto;
  }
}

.placeholder {
  height: 78rpx;
  background-color: #f4f6fb;
}

.transform-wrapper {
  // transform: translateY(-30rpx);
  position: relative;
  z-index: 1;
  margin-top: -30rpx;
  padding-bottom: 20rpx;
  background-color: #f4f6fb;
  border-radius: 30rpx 30rpx 0 0;
}

.activity-content {
  position: absolute;
  top: -23rpx;
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-left: 30rpx;
  height: 46rpx;
  line-height: 46rpx;
  font-size: 24rpx;
  color: #814e33;

  &::-webkit-scrollbar {
    display: none;
  }

  .activity-list {
    display: inline-flex;
    align-items: center;
    margin-right: 12rpx;
    padding: 0 16rpx;
    border-radius: 23rpx;
    background: #fff8ec;
    white-space: nowrap;
    .arrow {
      display: block;
      width: 18rpx;
      height: 18rpx;
      margin-left: 6rpx;
      background: url(#{$assets}/activity/special/arrow.png) no-repeat center/ contain;
    }
  }
}

.detail-wrapper {
  padding: 0 30rpx 20rpx;
  background-color: $color-white;
  .title {
    padding-top: 30rpx;
    font-size: 36rpx;
    font-weight: bold;
    line-height: 54rpx;
    margin-bottom: 10rpx;
  }

  .tag-content {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    margin-bottom: 14rpx;
    margin-right: -30rpx;

    &::-webkit-scrollbar {
      display: none;
    }

    .tag {
      & + .tag {
        margin-left: 10rpx;
      }

      &.type {
        color: #ffa000;
        background-color: #fff3e0;
      }

      font-size: 20rpx;
      white-space: nowrap;
      padding: 0 13rpx;
      line-height: 34rpx;
      border-radius: 4rpx;
      background-color: #e5edff;
      color: $color-primary;
    }
  }

  .detail {
    margin-bottom: 22rpx;

    .list {
      display: flex;
      line-height: 36rpx;
      font-size: 24rpx;
      align-items: baseline;
      margin-bottom: 6rpx;

      &:last-child {
        margin-bottom: 0;
      }

      $icons: organization, time, address, type, company;
      @each $name in $icons {
        &.#{$name} {
          .label {
            padding-left: 37rpx;
            background: url(#{$assets}/activity/common/#{$name}.png) no-repeat left 5rpx/26rpx 26rpx;
          }
        }
      }

      .label {
        flex-shrink: 0;
        font-weight: bold;
      }

      .ellipsis {
        @include utils-ellipsis-lines(1, 36rpx, 24rpx);
      }

      &.address {
        .label {
          flex: 1;
          .address {
            display: inline;
            font-weight: 400;
          }
        }
        .content {
          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80rpx;
            // height: 38rpx;
            background: #ffffff;
            border-radius: 20rpx;
            border: 1px solid #ebebeb;
            font-size: 24rpx;
            color: rgba($color: #333, $alpha: 0.6);
          }
        }
      }

      &.company {
        .content {
          display: flex;
          align-items: baseline;

          .total {
            color: #ffa000;
            font-size: 32rpx;
            font-weight: bold;
          }

          .item {
            display: flex;
            align-items: baseline;
            margin-right: 32rpx;
            position: relative;

            .tips {
              position: absolute;
              right: -120rpx;
              top: -13rpx;
              padding: 0 11rpx;
              line-height: 32rpx;
              color: #ffa000;
              font-size: 20rpx;
              background: #fff8ec;
              white-space: nowrap;
              border-radius: 16rpx 16rpx 16rpx 0rpx;
            }
            &.left {
              margin-right: 130rpx;
            }

            &.right {
              .label {
                background: url(#{$assets}/activity/common/session.png) no-repeat left 5rpx/26rpx 26rpx;
              }
            }
          }
        }
      }
    }
  }

  .welfare {
    line-height: 46rpx;
    padding-left: 65rpx;
    padding-right: 32rpx;
    background: url(#{$assets}/activity/special/welfare.png) no-repeat 20rpx 6rpx/34rpx 34rpx, #fff8ec;
    border-radius: 12rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.has-detail {
      padding-right: 21rpx;

      .arrow {
        display: initial;
      }
    }

    .content {
      color: #814e33;
      @include utils-ellipsis-lines(1, 46rpx, 24rpx);
    }

    .arrow {
      display: none;
      width: 18rpx;
      height: 18rpx;
      background: url(#{$assets}/activity/special/arrow.png) no-repeat center/ contain;
    }
  }
}

.session {
  margin-top: 20rpx;
  background: #ffffff;
  padding: 30rpx 0 20rpx 30rpx;

  .session-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      padding-left: 37rpx;
      font-weight: bold;
      font-size: 30rpx;
      color: #333333;
      background: url(#{$assets}/activity/activity/arrow.png) no-repeat left center/ 28rpx 29rpx;
    }

    .right {
      padding-right: 20rpx;
      margin-right: 30rpx;
      font-size: 24rpx;
      color: rgba($color: #333, $alpha: 0.6);
      background: url(#{$assets}/activity/activity/small-arrow.png) no-repeat right center/ 12rpx;
    }
  }
  .session-content {
    margin-top: 14rpx;

    .scroll {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      padding-top: 14rpx;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .item {
      position: relative;
      width: 164rpx;
      flex-shrink: 0;
      margin-right: 20rpx;
      padding: 20rpx 18rpx;
      background: #f4f6fb;
      border-radius: 10rpx;

      &.offline-mark {
        .step {
          opacity: 0.4;
        }
      }

      .step {
        position: absolute;
        top: -14rpx;
        right: 0;
        padding: 0 10rpx;
        height: 28rpx;
        color: $color-white;
        font-size: 18rpx;
        line-height: 28rpx;
        border-radius: 14rpx 14rpx 0rpx 14rpx;

        $effect: (1, #5386ff, #e5edff), (2, #ffa000, #fff3e0), (3, #51bd69, #f0ffdc), (4, #51bd69, #f0ffdc),
          (5, #fa857f, #fff4f3), (6, #786afb, #f4f3fc);

        @each $index, $color, $bg in $effect {
          &.tag#{$index} {
            color: $color;
            background: $bg;
          }
        }
        $font-colors: (-1, #ffa000, #fff3e0), (1, #5386ff, #e5edff), (2, #fa635c, #fff4f3), (3, #ffa000, #fff3e0),
          (4, #999999, #f8f8f8);

        @each $label, $value, $bg in $font-colors {
          &.step#{$label} {
            color: $value;
            background-color: $bg;
          }
        }
      }

      .name {
        font-size: 22rpx;
        color: #333333;
        font-weight: bold;
        @include utils-ellipsis;
      }

      .time {
        margin-top: 6rpx;
        font-size: 20rpx;
        color: rgba($color: #333, $alpha: 0.8);
        @include utils-ellipsis;
      }
    }
  }
}

.hot-company {
  margin-top: 14rpx;
  padding: 0 30rpx;
  overflow: hidden;
  background-color: $color-white;

  .hd {
    padding-left: 38rpx;
    margin: 30rpx 0;
    font-weight: bold;
    font-size: 30rpx;
    color: #333333;
    background: url(#{$assets}/activity/activity/arrow.png) no-repeat left center/ 28rpx 29rpx;
  }

  .bd {
    .swiper {
      height: 210rpx;

      &.swiperHeight {
        height: 120rpx;
      }
    }

    .swiper-item {
      display: grid;
      grid-template-columns: repeat(2, 335rpx); // 两列
      // grid-template-rows: repeat(2, 96rpx); // 两行，每行高度为 96rpx
      grid-auto-rows: 96rpx; /* 自动添加的行高为100px */
      grid-gap: 20rpx; // 网格间距

      .img {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
      }

      .item {
        display: flex;
        padding: 20rpx 16rpx;
        background: #f4f6fb;
        border-radius: 16rpx;

        .left {
          margin-right: 12rpx;
        }

        .right {
          flex: 1;
          overflow: hidden;
          align-items: flex-end;
          .tit {
            width: 100%;
            font-weight: bold;
            font-size: 24rpx;
            line-height: 28rpx;
            color: #333333;
            @include utils-ellipsis;
          }

          .desc {
            width: 100%;
            margin-top: 4rpx;
            font-size: 20rpx;
            color: rgba($color: #333, $alpha: 0.6);
            @include utils-ellipsis;
          }
        }
      }
    }
    .swiper-paging {
      height: 60rpx;
      position: relative;

      --td-swiper-nav-dot-size: 10rpx;
      --td-swiper-nav-dots-bar-active-width: 20rpx;
      --td-swiper-radius: var(--border-radius);
      --td-swiper-nav-dot-color: #c7c7c7;
      --td-swiper-nav-dot-active-color: var(--color-primary);

      .t-swiper-nav__dots-bar-item {
        margin: 0 5rpx;
      }
    }
  }
}

.meeting-tips {
  display: flex;
  height: 56rpx;
  margin-top: 14rpx;
  line-height: 56rpx;
  font-size: 24rpx;
  background-color: $color-white;
  .left {
    position: relative;
    z-index: 2;
    padding-left: 60rpx;
    color: #ffa000;
    background: url(#{$assets}/activity/activity/bugle.png) no-repeat 30rpx center/ 24rpx, #fff;
  }
  .right {
    position: relative;
    z-index: 1;
    flex: 1;
    color: #333333;
    overflow: hidden;
    position: relative;
    white-space: nowrap;

    .p {
      position: absolute;
      display: inline-block;
      white-space: nowrap;
      animation: marquee linear infinite;

      &.move {
        right: 0;
        transform: translateX(100%);
      }
    }
  }
}

.tabs-wrapper {
  position: sticky;
  top: 0;
  z-index: 99;
  margin-top: 14rpx;
  display: flex;
  // justify-content: space-between;
  border-bottom: 2rpx solid $border-color;
  background-color: $color-white;

  &.is-fixed {
    border-bottom-color: transparent;
    box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(51, 51, 51, 0.02);
  }

  &.ts-fixed {
    box-shadow: none;
  }

  .tabs-nav {
    position: relative;
    flex: 1;
    line-height: 88rpx;
    font-size: 30rpx;
    text-align: center;
    color: $font-color-basic;

    &.active {
      color: $color-primary;
      font-weight: bold;
      background: url(#{$assets}/activity/activity/tab-active.png) no-repeat center 66rpx / 52rpx 13rpx;
    }
  }
}

.tabs-pane-wrapper {
  background-color: $color-white;
  padding: 0 30rpx;
  .detail-pane {
    padding-top: 38rpx;
    @include common-wrapper-rich;
  }

  .apply-way-pane {
    padding-top: 38rpx;
    @include common-wrapper-rich;
  }

  .company-pane {
    .wrapper-tips {
      padding: 22rpx 0;
      padding-left: 26rpx;
      background: url(#{$assets}/activity/common/warn-gray.png) no-repeat left/20rpx 20rpx;
      font-size: 20rpx;
      color: $font-color-label;
    }

    .company-wrapper {
      .company-box {
        .filter-content {
          padding-top: 2rpx;
          padding-bottom: 20rpx;
          background-color: $color-white;
          display: flex;
          flex-wrap: nowrap;
          overflow-x: auto;
          position: sticky;
          top: 88rpx;
          z-index: 5;

          &.is-fixed {
            width: 100vw;
            margin-left: -30rpx;
            padding-left: 30rpx;
            box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(51, 51, 51, 0.02);
          }

          &::-webkit-scrollbar {
            display: none;
          }

          .filter-item {
            padding: 0 35rpx 0 20rpx;
            background-color: #f7f7f7;
            line-height: 52rpx;
            font-size: 24rpx;
            color: $font-color-basic;
            white-space: nowrap;
            border-radius: 8rpx;
            position: relative;
            border: 2rpx solid #f7f7f7;

            &::after {
              position: absolute;
              content: '';
              border-left: 6rpx solid #c7c7c7;
              border-right: 6rpx solid transparent;
              border-top: 6rpx solid transparent;
              border-bottom: 6rpx solid transparent;
              transform: rotate(45deg);
              right: 10rpx;
              bottom: 10rpx;
            }

            &.has-select {
              border-color: #ffa000;
              color: #ffa000;
              background-color: #fffaf1;

              &::after {
                border-left-color: #ffa000;
              }
            }

            & + .filter-item {
              margin-left: 10rpx;
            }
          }
        }

        .session-detail {
          display: flex;
          flex-direction: column;
          align-items: center;
          box-sizing: border-box;
          margin: 0 20rpx 8rpx;
          overflow: hidden;
          background-color: #f4f6fb;
          border-radius: 12rpx;
          padding: 20rpx 20rpx;

          .name {
            max-width: 100%;
            margin-bottom: 17rpx;
            @include utils-ellipsis;
          }

          .countdown {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20rpx;
            margin-bottom: 18rpx;

            .box {
              width: 28rpx;
              line-height: 28rpx;
              border-radius: 6rpx;
              border: 2rpx solid #ffc5c2;
              color: #fa635c;
              margin: 0 6rpx;
              text-align: center;
            }
          }

          .time {
            font-size: 18rpx;
            margin-bottom: 15rpx;
          }

          .address {
            display: flex;
            font-size: 18rpx;
            line-height: 28rpx;

            .label {
              flex-shrink: 0;
            }
          }
        }

        .company-list {
          .item {
            width: 100%;
            overflow: hidden;
            padding: 22rpx 0;
            border-bottom: 2rpx solid #ebebeb;

            &:first-child {
              padding-top: 0;
            }

            .detail {
              display: flex;
              align-items: center;

              .logo {
                flex-shrink: 0;
                position: relative;
                width: 100rpx;
                height: 100rpx;
                margin-right: 12rpx;

                &.is-vip {
                  &::after {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    content: '';
                    width: 29rpx;
                    height: 22rpx;
                    background: url(#{$assets}/activity/special/vip.png) no-repeat center/contain;
                  }
                }

                .img {
                  width: 100%;
                  height: 100%;
                  border-radius: 50%;
                }
              }

              .info {
                flex-grow: 1;
                overflow: hidden;

                .name {
                  line-height: 39rpx;
                  font-size: 32rpx;
                  font-weight: bold;
                  margin-bottom: 6rpx;
                  @include utils-ellipsis;
                }

                .middle {
                  margin-bottom: 6rpx;
                  font-size: 24rpx;
                }

                .bottom {
                  margin-bottom: 6rpx;
                  font-size: 24rpx;
                  @include utils-ellipsis;
                }
                view {
                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }

            $name: welfare, notice, major, update;

            @each $key in $name {
              .#{$key} {
                margin-bottom: 6rpx;
                font-size: 24rpx;
                color: $font-color-label;
                @include utils-ellipsis;
              }
            }

            .welfare {
              padding-left: 35rpx;
              background: url(#{$assets}/activity/special/praise.png) no-repeat left center/26rpx 24rpx;
            }

            .notice {
              padding-left: 28rpx;
              background: url(#{$assets}/activity/special/link.png) no-repeat left center/20rpx 20rpx;
            }

            .major {
              &::before {
                display: inline-block;
                content: '';
                width: 10rpx;
                height: 10rpx;
                background: #fdc15c;
                border-radius: 50%;
                margin-right: 8rpx;
              }
            }

            .update {
              padding-left: 28rpx;
              background: url(#{$assets}/activity/special/warn.png) no-repeat left center/20rpx 20rpx;
            }
          }

          .tips {
            font-size: 24rpx;
            color: $font-color-label;
            text-align: center;
            padding: 30rpx 0;
          }
        }

        .empty {
          padding-top: 370rpx;
          text-align: center;
          padding-bottom: 30rpx;
          font-size: 24rpx;
          color: rgba($color: #333, $alpha: 0.8);
          background: url(#{$assets}/activity/common/empty.png) no-repeat center 50rpx/400rpx 290rpx;
        }
      }
    }
  }

  .review-pane {
    padding-top: 38rpx;
    @include common-wrapper-rich;
  }
}

.code-popup {
  background-color: transparent !important;

  .code-popup-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .code-popup-content {
    width: 400rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 16rpx;

    .img {
      width: 100%;
      border-radius: 16rpx;
    }
  }

  .close {
    width: 58rpx;
    height: 58rpx;
    margin-top: 40rpx;
    background: url(#{$assets}/activity/common/close-white.png) no-repeat center/cover;
  }
}

.fixed-code {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  width: 78rpx;
  height: 78rpx;
  right: 30rpx;
  bottom: 394rpx;
  border-radius: 37rpx;
  border: 2rpx solid #ffe9c3;
  background: #fff3df;
  color: #ffa000;
  text-align: center;
  padding: 0 8rpx;
  box-sizing: border-box;
  line-height: 28rpx;
  z-index: 9;
  font-size: 24rpx;
}

page {
  .back-top-trigger {
    bottom: 300rpx;
    z-index: 99;
  }
}

.fixed-bottom-wrapper {
  display: flex;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 99;
  width: 100%;
  background-color: $color-white;
  box-sizing: border-box;
  padding: 0rpx 30rpx 0rpx;
  align-items: center;
  border-top: 2rpx solid #e9e9e9;

  .share {
    padding: 74rpx 10rpx 30rpx;
    margin-right: 20rpx;
    background: url(#{$assets}/activity/common/share.png) no-repeat center 20rpx/38rpx 38rpx;
    font-size: 26rpx;
    align-self: center;
    line-height: 1;
  }

  .apply-btn {
    margin-left: 20rpx;
    flex-grow: 1;
    padding: 0;
    line-height: 88rpx;
    border: none;
    background-color: #ffa000;
    color: $color-white;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 88rpx;

    &.is-disabled {
      color: $color-white;
      background-color: #ffd080;
    }
  }

  .countdown {
    position: absolute;
    top: -1rpx;
    right: 30rpx;
    height: 78rpx;
    padding: 0 25rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    font-weight: bold;
    font-size: 28rpx;
    color: #333333;
    box-shadow: 0rpx 3rpx 18rpx 0rpx rgba(51, 51, 51, 0.1);
    border-radius: 10rpx;
    transform: translateY(-100%);
    background: url(#{$assets}/activity/activity/bg-clock.png) no-repeat left bottom/44rpx,
      linear-gradient(180deg, #fff7e8, #ffffff);

    &.style-first {
      top: -35rpx;
    }

    &::after {
      position: absolute;
      bottom: -20rpx;
      right: 110rpx;
      width: 0;
      height: 0;
      content: '';
      border-left: 10rpx solid transparent;
      border-right: 10rpx solid transparent;
      border-top: 20rpx solid white; /* 设置三角形的颜色 */
    }
    .box {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 42rpx;
      height: 42rpx;
      padding: 0 6rpx;
      margin: 0 10rpx;
      color: #ffffff;
      border-radius: 4rpx;
      background: #ffa000;
      box-sizing: border-box;
    }
  }
}

.welfare-popup {
  padding: 50rpx 30rpx 30rpx 30rpx;
  background: linear-gradient(180deg, #fff1d8, #ffffff, #ffffff);
  border-radius: 24rpx 24rpx 0rpx 0rpx;

  .welfare-header {
    color: #dc833f;
    font-weight: bold;
    font-size: 36rpx;
    text-align: center;
    margin-bottom: 30rpx;
    position: relative;

    .close {
      position: absolute;
      right: 10rpx;
      top: -20rpx;
      width: 30rpx;
      height: 30rpx;
      background: url(#{$assets}/activity/common/close.png) no-repeat center/contain;
    }
  }

  .welfare-content {
    max-height: 800rpx;

    .welfare-common {
      display: flex;
      flex-direction: column;

      .welfare-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 12rpx;
        line-height: 48rpx;
        padding-left: 41rpx;
        background: url(#{$assets}/activity/special/welfare-item.png) no-repeat left 10rpx/28rpx 28rpx;
      }

      .welfare-body {
        font-size: 28rpx;
        line-height: 42rpx;

        .activity {
          padding: 25rpx 0;
          border-bottom: 1rpx solid #eaeaea;

          &:last-child {
            padding-bottom: 0;
            border-bottom: none;
          }

          &.offline-mark {
            .hd {
              .step {
                opacity: 0.4;
              }
            }
          }
          .hd {
            font-size: 24rpx;

            .step {
              display: inline;
              position: relative;
              top: -2rpx;
              padding-left: 15rpx;
              font-size: 24rpx;

              &::before {
                position: absolute;
                left: 0;
                top: 50%;
                content: '';
                width: 8rpx;
                height: 8rpx;
                border-radius: 50%;
                transform: translateY(-50%);
              }

              $font-colors: (-1: #ffa000, 1: #5386ff, 2: #fa635c, 3: #ffa000, 4: #333333);

              @each $label, $value in $font-colors {
                &.step#{$label} {
                  color: $value;

                  &::before {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    content: '';
                    width: 8rpx;
                    height: 8rpx;
                    border-radius: 50%;
                    transform: translateY(-50%);
                    background-color: $value;
                  }
                }
              }
            }

            .title {
              display: inline;
              font-weight: bold;
              font-size: 32rpx;
              line-height: 48rpx;
              margin-left: 20rpx;
              // @include utils-ellipsis;
            }
          }
          .ft {
            display: flex;
            justify-content: space-between;
            $items: address date session;
            font-size: 24rpx;

            @each $var in $items {
              .#{$var} {
                margin-top: 4rpx;
                padding-left: 30rpx;
                color: rgba($color: #333, $alpha: 0.4);
                background: url(#{$assets}/icon/#{$var}.png) no-repeat left / 24rpx 24rpx;
              }
            }

            .date {
              flex: 1;
              @include utils-ellipsis;
            }

            .address {
              max-width: 260rpx;
              @include utils-ellipsis;
            }
          }
        }
      }

      & + .welfare-common {
        margin-top: 34rpx;
      }
    }
  }

  .close-btn {
    width: 642rpx;
    height: 88rpx;
    background: #ffa000;
    border-radius: 44rpx;
    color: $color-white;
    margin-top: 30rpx;
  }
}

.area-popup-tips {
  color: $font-color-label;
  font-size: 24rpx;
  padding: 0 30rpx;
  margin-top: 20rpx;
  margin-bottom: -8rpx;
}
