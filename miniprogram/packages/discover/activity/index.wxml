<!-- 调用此组件务必在引用处添加id="new-nav-bar"，因为在chat-socket.ts文件需要调用其方法 -->
<new-nav-bar id="new-nav-bar" title="{{ activityInfo.name }}" type="1" bind:clickNav="checkLogin"></new-nav-bar>

<scroll-view class="scroll-view" scroll-into-view="{{ scrollIntoView }}" scroll-top="{{ scrollTop }}" scroll-with-animation="{{ scrollWithAnimation }}" scroll-y style="height:{{ scrollViewHeight }}px;" enhanced show-scrollbar="{{ false }}" bind:scroll="scroll" bind:scrolltolower="scrolltolower" using-sticky>
  <view class="cover-wrapper">
    <!-- 待举办：await
    即将进行：await-start
    进行中：start
    已结束：ended -->
    <!-- 状态；1:待举办；2：报名中；3:进行中；4:已结束 -->
    <view class="status status-{{ activityInfo.activityChildStatus }}">{{ activityInfo.activityChildStatusText }}</view>
    <image class="img" src="{{ activityInfo.imageMiniBannerUrl }}" mode="widthFix" lazy-load="false" />
  </view>

  <view class="transform-wrapper">
    <view class="activity-content" wx:if="{{ activityInfo.specialName.length }}">
      <view bind:tap="handleJump" data-item="{{ item }}" wx:for="{{ activityInfo.specialName }}" wx:key="key" wx:item="item" class="activity-list">{{ item.name }}<i class="arrow"></i></view>
    </view>

    <view class="detail-wrapper">
      <view class="title">{{ activityInfo.name }}</view>

      <view class="tag-content">
        <!--线下： type  -->
        <view class="tag {{ item.type == 1 ? 'type' : '' }}" wx:for="{{ activityInfo.tagList }}" wx:key="index">{{ item.value }}</view>
      </view>

      <view class="detail">
        <view class="list organization" wx:if="{{ activityInfo.activityOrganization }}">
          <view class="label">活动组织：</view>
          <view class="content ellipsis">{{ activityInfo.activityOrganization }}</view>
        </view>
        <view class="list time" wx:if="{{ activityInfo.activityDateTime }}">
          <view class="label">活动时间：</view>
          <view class="content">{{ activityInfo.activityDateTime }}</view>
        </view>
        <view class="list address" wx:if="{{ activityInfo.activityArea }}">
          <view class="label"
            >活动地点：<view class="address">{{ activityInfo.activityArea }}</view></view
          >
          <view class="content" wx:if="{{ (activityInfo.latitude && activityInfo.longitude) || activityInfo.activityDetailAddress }}">
            <view class="btn" bind:tap="handleNavOrCopy">
              {{ activityInfo.latitude && activityInfo.longitude ? '导航' : '复制' }}
            </view>
          </view>
        </view>
        <view class="list type" wx:if="{{ activityInfo.typeText }}">
          <view class="label">活动系列：</view>
          <view class="content">{{ activityInfo.typeText }}</view>
        </view>
        <view class="list company">
          <view class="label">参会单位：</view>
          <view class="content">
            <view class="item left">
              <view wx:if="{{ activityInfo.participationCompanyAmount > 10 }}" class="total">{{ activityInfo.participationCompanyAmount }}</view
              >{{ activityInfo.participationCompanyAmount > 10 ? '家' : '更新中' }}
              <view class="tips">持续更新中</view>
            </view>
            <view class="item right">
              <view class="label">需求人数：</view>
              <view wx:if="{{ activityInfo.activityNumber > 100 }}" class="total">{{ activityInfo.activityNumber }}</view
              >{{ activityInfo.activityNumber > 100 ? '人' : '更新中' }}
            </view>
          </view>
        </view>
      </view>

      <view wx:if="{{ activityInfo.activityBenefits }}" class="welfare  {{ hasWelfareDetailHtml ? 'has-detail' : '' }}" bind:tap="openWelfarePopup">
        <view class="content">{{ activityInfo.activityBenefits }}</view>

        <i class="arrow"></i>
      </view>
    </view>

    <view class="hot-company" wx:if="{{ companyHotList.length }}">
      <view class="hd"> 热门单位推荐 </view>
      <view class="bd">
        <swiper class="swiper {{ swiperHeightClass ? 'swiperHeight' : '' }}" bindchange="swiperChange">
          <swiper-item class="swiper-item" wx:for="{{ companyHotList }}" wx:key="key" wx:for-index="key" wx:for-item="item">
            <view class="item" wx:for="{{ item }}" wx:key="k" wx:for-index="k" wx:for-item="v" bind:tap="handleJump" data-item="{{ v }}" data-type="hotCompany">
              <view class="left">
                <view class="logo">
                  <image class="img" src="{{ v.companyLogo }}" mode="aspectFit" />
                </view>
              </view>
              <view class="right">
                <view class="tit">{{ v.companyName }}</view>
                <view class="desc">{{ v.companyTypeText }} {{ v.companyNatureText && ' | ' }} {{ v.companyNatureText }}</view>
              </view>
            </view>
          </swiper-item>
        </swiper>
        <view class="swiper-paging" wx:if="{{ companyHotList.length > 1 }}">
          <t-swiper-nav total="{{ companyHotList.length }}" current="{{ hotCompanySwiper.current }}" type="dots-bar"> </t-swiper-nav>
        </view>
      </view>
    </view>

    <view class="meeting-tips" hidden="{{ !activityInfo.attendanceNotes }}" bind:tap="openMettingTipsPopup">
      <view class="left">参会须知：</view>
      <view class="right">
        <view class="p {{ isMarquee ? 'move' : '' }}" style="right: {{ isMarquee ? marqueeDuration : null }}px">{{ activityInfo.attendanceNotes }}</view>
      </view>
    </view>

    <view class="tabs-wrapper {{ isTabFixed ? 'is-fixed' : '' }} {{ activeTab === 'company' ? 'ts-fixed' : '' }}" id="tabs-wrapper">
      <view class="tabs-nav {{ activeTab === 'detail' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="detail" wx:if="{{ activityDetailHtml }}">活动详情</view>
      <view class="tabs-nav {{ activeTab === 'company' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="company" wx:if="{{ activityInfo.participationCompanyAmount - 0 > 1 }}">参会单位</view>
      <view class="tabs-nav {{ activeTab === 'apply-way' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="apply-way" wx:if="{{ participationMethodHtml }}">参会方式</view>
      <view class="tabs-nav {{ activeTab === 'review' ? 'active' : '' }}" bind:tap="tabSwitch" data-type="review" wx:if="{{ reviewHtml }}">精彩回顾</view>
    </view>

    <view class="tabs-pane-wrapper">
      <view class="tabs-pane detail-pane" wx:if="{{ activeTab === 'detail' && activityDetailHtml }}">
        <mp-html container-style="word-break: break-word; overflow: initial;" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ activityDetailHtml }}" bind:linktap="handleHtmlTap" copy-link="{{ copyLink }}" />
        <!-- <view class="common-wrapper">
          <view class="common-content">
            <view class="common-content-text"> 上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅，为帮助更多海内外高层次人才与优质单位广泛对接，下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。步履不停，载梦前行，更多招聘场次“职”等您来解锁！等您来解锁 </view>
          </view>
        </view>

        <view class="common-wrapper">
          <view class="common-title-content">
            <view class="common-title">
              <text class="title">活动背景</text>
            </view>
          </view>

          <view class="common-content">
            <view class="common-content-text pd-50"> 上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅，为帮助更多海内外高层次人才与优质单位广泛对接，下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。步履不停，载梦前行，更多招聘场次“职”等您来解锁！等您来解锁 </view>
          </view>
        </view>

        <view class="common-wrapper">
          <view class="common-title-content">
            <view class="common-title">
              <text class="title">参会对象</text>
            </view>
          </view>

          <view class="common-content">
            <view class="common-second-wrapper not-bg pd-50"> 海内外应往届硕士/博士毕业生、具有硕士/博士学位的社会在职人才、博士后出站人员、中高级职称人才等。</view>
            <view class="common-second-wrapper">
              <view class="common-second-title pd-30">01、参会人才</view>
              <view class="common-second-content"> 海内外应往届硕士/博士毕业生、具有硕士/博士学位的社会在职人才、博士后出站人员、中高级职称人才等。</view>
            </view>

            <view class="common-second-wrapper">
              <view class="common-second-title">02、参会单位</view>
              <view class="common-second-content"> 全国各人社厅/局、高校、科研院所、医院、企事业单位等有高层次人才引进需求的单位。</view>
            </view>
          </view>
        </view> -->
      </view>

      <view class="tabs-pane apply-way-pane" wx:if="{{ activeTab === 'apply-way' && participationMethodHtml }}">
        <mp-html container-style="word-break: break-word; overflow: initial;" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ participationMethodHtml }}" bind:linktap="handleHtmlTap" copy-link="{{ copyLink }}" />
      </view>

      <view class="tabs-pane company-pane" wx:if="{{ activeTab === 'company' && activityInfo.participationCompanyAmount - 0 > 1 }}">
        <view class="wrapper-tips">仅展示部分单位及招聘需求，欲知更多单位详情，请持续关注或现场了解</view>

        <view class="company-wrapper">
          <view class="company-box">
            <view class="filter-content {{ isTabFixed ? 'is-fixed' : '' }}">
              <view class="filter-item  {{ formData.areaId.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="area">{{ label.areaId }}</view>
              <view class="filter-item {{ formData.majorId.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="major">{{ label.majorId }}</view>
              <view class="filter-item {{ formData.categoryId.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="categoryId">{{ label.categoryId }}</view>
              <view class="filter-item {{ formData.type.length ? 'has-select' : '' }}" bind:tap="showPopup" data-visible="type">{{ label.type }}</view>
            </view>

            <view class="company-content">
              <view class="company-list">
                <view class="item" wx:for="{{ companyList }}" wx:key="index" bind:tap="handleJump" data-item="{{ item }}" data-type="company">
                  <view class="detail">
                    <view class="logo {{ item.isTop == 1 ? 'is-vip' : '' }}">
                      <image class="img" src="{{ item.logoUrl }}" mode="aspectFit" />
                    </view>

                    <view class="info">
                      <view class="name">{{ item.name }}</view>
                      <view class="middle">{{ item.cardTag }}</view>
                      <view class="bottom" wx:if="{{ item.cardTag2 }}">招 | {{ item.cardTag2 }}</view>
                      <view class="welfare" wx:if="{{ item.cardTag3.type == 1 }}">{{ item.cardTag3.value }}</view>
                      <view class="notice" wx:if="{{ item.cardTag3.type == 3 }}">{{ item.cardTag3.value }} </view>
                      <view class="major" wx:if="{{ item.cardTag3.type == 2 }}">{{ item.cardTag3.value }} </view>
                      <view class="update" wx:if="{{ item.cardTag3.type == 4 }}">{{ item.cardTag3.value }} </view>
                    </view>
                  </view>
                </view>

                <view class="tips" wx:if="{{ isEnd && companyList.length }}">参会单位持续更新中...</view>
              </view>
              <view class="empty" wx:if="{{ !companyList.length }}"> 更多单位持续更新中，敬请关注... </view>
            </view>
          </view>
        </view>
      </view>

      <view class="tabs-pane review-pane" wx:if="{{ activeTab === 'review' && reviewHtml }}">
        <mp-html container-style="word-break: break-word" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ reviewHtml }}" bind:linktap="handleHtmlTap" copy-link="{{ copyLink }}" />
      </view>
    </view>

    <view class="session" wx:if="{{ isRecommendActivity }}">
      <view class="session-title">
        <view class="left">相关场次</view>
        <view class="right" wx:if="{{ recommendActivityList.length > 6 }}" bind:tap="openSessionPopup">全部</view>
      </view>
      <view class="session-content">
        <view class="scroll">
          <view class="item {{ item.activityChildStatus === '4' ? 'offline-mark' : '' }}" data-item="{{ item }}" data-type="session" wx:for="{{ recommendActivityList }}" wx:key="key" wx:item="item" bindtap="handleJump">
            <view class="step {{ 'step' + item.activityChildStatus }}">{{ item.activityChildStatusText }}</view>
            <view class="name">{{ item.name }}</view>
            <view class="time">{{ item.activityTime }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{ activityInfo.activityChildStatus === '2' }}" class="placeholder"></view>
</scroll-view>

<view wx:if="{{ showBackTop }}" class="back-top-trigger" bind:tap="backTop"></view>

<t-popup visible="{{ codePopupVisible }}" usingCustomNavbar placement="center" t-class="code-popup">
  <view class="code-popup-wrapper">
    <view class="code-popup-content">
      <image class="img" src="{{ activityInfo.imageServiceCodeUrl }}" mode="widthFix" show-menu-by-longpress="{{ true }}" lazy-load />
    </view>
    <view class="close" bind:tap="closeCodePopup"></view>
  </view>
</t-popup>
<view wx:if="{{ activityInfo.imageServiceCodeUrl }}" class="fixed-code" bind:tap="showCodePopup">活动进群</view>

<view class="fixed-bottom-wrapper">
  <block wx:if="{{ activityInfo.applyLinkCompanyIsShow == 1 || activityInfo.applyStatus != 3 }}">
    <button class="share" open-type="share">分享</button>
    <block wx:if="{{ activityInfo.activityChildStatus === '4' }}">
      <button class="apply-btn is-disabled" disabled>已结束</button>
    </block>
    <block wx:else>
      <button class="apply-btn" wx:if="{{ activityInfo.applyLinkCompanyIsShow == 1 }}" bind:tap="handleApply" data-type="1">单位报名</button>
      <button class="apply-btn {{ activityInfo.applyStatus === 1 ? 'is-disabled' : '' }}" wx:if="{{ activityInfo.applyStatus != 3 }}" disabled="{{ activityInfo.applyStatus === 1 }}" bind:tap="handleApply" data-type="2">{{ activityInfo.applyStatus === 1 ? '已报名' : '人才报名' }}</button>
    </block>
  </block>

  <view wx:if="{{ activityInfo.activityChildStatus === '2' }}" class="countdown {{ activityInfo.applyLinkCompanyIsShow == 1 || activityInfo.applyStatus != 3 ? '' : 'style-first' }}">
    距活动开始:
    <view class="box">{{ activityStartCountDown.day }}</view
    >天 <view class="box">{{ activityStartCountDown.hours }}</view
    >时 <view class="box">{{ activityStartCountDown.min }}</view
    >分 <view class="box">{{ activityStartCountDown.second }}</view
    >秒
  </view>
</view>

<t-popup visible="{{ welfareVisible }}" using-custom-navbar placement="bottom">
  <view class="welfare-popup">
    <view class="welfare-header">
      参会福利

      <i class="close" bind:tap="closeWelfarePopup"></i>
    </view>

    <scroll-view scroll-y class="welfare-content">
      <mp-html container-style="word-break: break-word" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ welfareDetailHtml }}" />

      <!-- <view class="welfare-common">
        <view class="welfare-title">参会享交通福利</view>
        <view class="welfare-body">人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）</view>
      </view> -->
    </scroll-view>

    <button class="close-btn" bind:tap="closeWelfarePopup">我知道了</button>
  </view>
</t-popup>

<t-popup visible="{{ sessionVisible }}" using-custom-navbar placement="bottom">
  <view class="welfare-popup" style="padding-bottom: 0">
    <view class="welfare-header" style="margin-bottom: 0">
      相关场次

      <i class="close" bind:tap="closeSessionPopup"></i>
    </view>

    <scroll-view scroll-y class="welfare-content">
      <view class="welfare-common">
        <view class="welfare-body">
          <view class="item activity {{ item.activityChildStatus === '4' ? 'offline-mark' : '' }}" data-item="{{ item }}" wx:for="{{ recommendActivityList }}" wx:key="key" wx:item="item" bindtap="handleView" data-type="session" bindtap="handleJump">
            <view class="hd">
              <view class="step {{ 'step' + item.activityChildStatus }}">{{ item.activityChildStatusText }}</view>
              <view class="title">{{ item.name }}</view>
            </view>
            <view class="ft">
              <view class="date">{{ item.activityTime }}</view>
              <view class="address">活动地点:{{ item.area }}</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</t-popup>

<t-popup visible="{{ mettingTipsVisible }}" using-custom-navbar placement="bottom">
  <view class="welfare-popup">
    <view class="welfare-header">
      提示

      <i class="close" bind:tap="closeMettingTipsPopup"></i>
    </view>

    <scroll-view scroll-y class="welfare-content">
      <view class="welfare-common">
        <view class="welfare-body">
          <mp-html container-style="word-break: break-word" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ activityInfo.activityHighlights }}" />
        </view>
      </view>
    </scroll-view>
  </view>
</t-popup>

<!-- 所在地区 -->
<basic-popup visible="{{ visible.area }}" model="{{ formData.areaId }}" title="所在地区" limit="{{ limit }}" column="3" options="{{ cityParams }}" options-label="name" options-value="id" bind:change="handleChange" data-form-key="areaId">
  <view slot="tips" class="area-popup-tips"> * 仅展示参会单位所在省份及部分热门城市 </view>
</basic-popup>

<!-- 需求学科 -->
<picker-popup visible="{{ visible.major }}" model="{{ formData.majorId }}" title="选择学科" limit="{{ limit }}" column="{{ 2 }}" options="{{ majorSelect }}" options-label="v" options-value="k" bind:change="handleChange" data-form-key="majorId" />

<!-- 职位类型 -->
<picker-popup visible="{{ visible.categoryId }}" model="{{ formData.categoryId }}" title="职位类型" limit="{{ limit }}" column="{{ 2 }}" options="{{ jobTypeList }}" options-label="v" options-value="k" bind:change="handleChange" data-form-key="categoryId" />

<!-- 单位类型 -->
<basic-popup visible="{{ visible.type }}" model="{{ formData.type }}" title="单位类型" limit="{{ limit }}" column="3" options="{{ companyTypeParams }}" options-label="name" options-value="code" bind:change="handleChange" data-form-key="type" />
