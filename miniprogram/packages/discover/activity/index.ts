import { getElClientRect, showToast, showLoading, toWebPage } from '@/utils/util'

import { jump, sceneStringToObj } from '@/utils/url'

import { throttle } from 'throttle-debounce'

import { getActivityInfo, getActivityCompany } from '@/api/activity'
import { htmlLinkHandlerMixin } from '@/utils/html-link-handler'

let _this = <any>null

Page({
  /**
   * 页面的初始数据
   */
  data: {
    queryParams: {
      showTab: ''
    },

    isLogin: false,

    welfareVisible: false,
    sessionVisible: false,
    mettingTipsVisible: false,
    codePopupVisible: false,

    activeTab: 'detail',
    scrollIntoView: '',
    isTabFixed: false,

    showBackTop: false,

    scrollWithAnimation: false,

    headerHeight: 0,
    tabOffsetTop: 0,
    scrollTop: 0,
    scrollViewHeight: 0,

    formData: <any>{
      areaId: [],
      type: [],
      majorId: [],
      categoryId: [],
      page: 1
    },

    activityList: [],
    activityId: '',

    limit: 5,

    label: {
      areaId: '所在地区',
      majorId: '需求学科',
      categoryId: '职位类型',
      type: '单位类型'
    },

    visible: {
      area: false,
      major: false,
      categoryId: false,
      type: false
    },

    isRecommendActivity: false,

    hotCompanySwiper: {
      current: 0
    },
    companyHotList: [],

    activityInfo: <any>{
      activityCountDownTime: 0
    },

    cityParams: [],
    jobTypeList: [],
    majorSelect: [],
    companyTypeParams: [],

    activityStartCountDown: {
      day: 0,
      hours: 0,
      min: 0,
      second: 0
    },
    activityTimer: 0,

    hasWelfareDetailHtml: false,
    welfareDetailHtml: '',
    activityDetailHtml: '',
    participationMethodHtml: '',
    reviewHtml: '',

    companyList: [],
    isEnd: false,

    recommendActivityList: [],

    isMarquee: false,
    marqueeDuration: 0,

    swiperHeightClass: false,
    copyLink: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(query: any) {
    _this = this
    // 有两种情况,一种是有场景值(也就是二维码扫码进来的)
    // 一种是没有场景值(也就是分享进来的)
    // 有场景值的时候,需要把场景值存到本地,然后在投递的时候带上
    // 没有场景值的时候,需要判断是否有本地存储的场景值,有的话就带上
    // 没有的话就不带

    let id = ''
    if (query.scene) {
      const sceneObj = sceneStringToObj(query.scene)

      id = sceneObj.id
      this.setData({
        queryParams: sceneObj
      })
    } else {
      id = query.id
      this.setData({
        queryParams: query
      })
    }
    this.setData({ activityId: id })
    await this.getDetail()
    this.getActivityCompanyList()
    await this.getScrollViewHeight()
  },

  checkMarquee() {
    const query = this.createSelectorQuery()
    query.select('.meeting-tips .right').boundingClientRect()
    query.select('.meeting-tips .right .p').boundingClientRect()
    query.exec((res) => {
      if (res[0] && res[1]) {
        const containerWidth = res[0].width
        const textWidth = res[1].width
        if (textWidth > containerWidth) {
          this.setData({
            isMarquee: true
          })
          setInterval(() => {
            if (this.data.marqueeDuration < textWidth + containerWidth) {
              this.setData({
                marqueeDuration: this.data.marqueeDuration + 1
              })
            } else {
              this.setData({
                marqueeDuration: 0
              })
            }
          }, 30)
        } else {
          this.setData({
            isMarquee: false
          })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  getTabOffsetTop() {
    setTimeout(async () => {
      const { top } = await (<any>getElClientRect('.tabs-wrapper'))
      this.setData({ tabOffsetTop: top })
    }, 310)
  },

  async getScrollViewHeight() {
    const { height: fixedBottomHeight } = await (<any>getElClientRect('.fixed-bottom-wrapper'))
    const { headerOffsetHeight, screenHeight } = getApp().globalData
    this.setData({
      headerHeight: headerOffsetHeight,
      scrollViewHeight: screenHeight - headerOffsetHeight - fixedBottomHeight
    })
  },

  checkLogin() {
    const { isLogin } = this.data

    if (!isLogin) {
      this.setData({ loginDialogVisible: true })
    }
    return isLogin
  },

  tabSwitch(e: WechatMiniprogram.CustomEvent) {
    const { type } = e.currentTarget.dataset
    const { activeTab } = this.data
    if (activeTab === type) return
    this.setData({ activeTab: type })
    const { tabOffsetTop, headerHeight } = this.data
    this.setData({ scrollTop: tabOffsetTop - headerHeight })
    // if (type !== 'apply-way') {
    // this.setData({ scrollIntoView: 'tabs-wrapper' })
    // }
  },

  openWelfarePopup() {
    const { hasWelfareDetailHtml } = this.data
    this.setData({ welfareVisible: hasWelfareDetailHtml })
  },

  closeWelfarePopup() {
    this.setData({ welfareVisible: false })
  },

  openSessionPopup() {
    this.setData({ sessionVisible: true })
  },

  closeSessionPopup() {
    this.setData({ sessionVisible: false })
  },

  openMettingTipsPopup() {
    this.setData({ mettingTipsVisible: true })
  },

  closeMettingTipsPopup() {
    this.setData({ mettingTipsVisible: false })
  },

  closeCodePopup() {
    this.setData({ codePopupVisible: false })
  },

  showCodePopup() {
    this.setData({ codePopupVisible: true })
  },

  getDate(time: number) {
    const day = Math.floor(time / (24 * 3600))
    const hours = Math.floor((time % (24 * 3600)) / 3600)
    const min = Math.floor((time % 3600) / 60)
    const second = time % 60

    return { day, hours, min, second }
  },

  activityCountDow() {
    clearTimeout(this.data.activityTimer)

    const { activityCountDownTime } = this.data.activityInfo

    const time = Number(activityCountDownTime)
    const countDownObj = this.getDate(time)

    this.setData({
      activityStartCountDown: countDownObj,
      'activityInfo.activityCountDownTime': time - 1
    })

    if (activityCountDownTime === 0) {
      this.getDetail()
      // wx.redirectTo({ url: `/packages/discover/activity/index?id=${this.data.activityId}` })
      return
    }

    this.data.activityTimer = setTimeout(() => {
      this.activityCountDow()
    }, 1000)
  },

  async getDetail() {
    showLoading()
    const { activityId, queryParams } = this.data
    await getActivityInfo({ id: activityId })
      .then((resp: any) => {
        const { activityInfo, companyHotList, companyParticipatingList, isRecommendActivity, recommendActivityList } =
          resp

        const {
          activityDetail,
          activityBenefitsContentFormat,
          activityCountDownTime,
          wonderfulReview,
          participationMethod
        } = activityInfo
        const {
          searchList: { jobTypeList, majorSelect, cityParams, companyTypeParams }
        } = companyParticipatingList

        const companyHotListChunked = []
        for (let i = 0; i < companyHotList.length; i += 4) {
          companyHotListChunked.push(companyHotList.slice(i, i + 4))
        }

        this.setData(
          {
            companyHotList: companyHotListChunked,
            activityInfo,
            isRecommendActivity,
            cityParams,
            jobTypeList,
            majorSelect,
            companyTypeParams,
            hasWelfareDetailHtml: !!activityBenefitsContentFormat,
            welfareDetailHtml: activityBenefitsContentFormat,
            activityDetailHtml: activityDetail,
            participationMethodHtml: participationMethod,
            reviewHtml: wonderfulReview,
            recommendActivityList,
            swiperHeightClass: companyHotList.length <= 2 ? true : false
          },
          () => {
            // 渲染完毕在获取
            this.getTabOffsetTop()
          }
        )

        if (wonderfulReview) {
          // 如果有精彩回顾tab才进行tab定位
          this.setData({
            activeTab: queryParams.showTab || 'detail'
          })
        } else {
          this.setData({
            activeTab: activityDetail ? 'detail' : 'company'
          })
        }

        if (activityCountDownTime) {
          this.activityCountDow()
        }

        this.checkMarquee()
        wx.hideLoading()
      })
      .catch(() => {
        wx.hideLoading()
      })
  },

  handleJump(e: WechatMiniprogram.CustomEvent) {
    const { type = 'default', item } = e.currentTarget.dataset
    if (type === 'default') {
      wx.navigateTo({ url: `/packages/discover/special/index?id=${item.id}` })
    } else if (type === 'session') {
      wx.navigateTo({ url: `/packages/discover/activity/index?id=${item.id}` })
    } else if (type === 'company') {
      const { targetUrl, targetType } = item
      jump(targetUrl, targetType)
    } else if (type === 'hotCompany') {
      jump(item.linkUrl, item.targetLinkType)
    }
  },

  handleNavOrCopy() {
    const { activityInfo } = this.data
    const { latitude, longitude, activityArea, activityDetailAddress } = activityInfo
    if (latitude && longitude) {
      wx.openLocation({
        latitude: latitude - 0,
        longitude: longitude - 0,
        scale: 18,
        address: activityArea,
        success: function () {
          // console.log('succress')
        },
        fail: function () {
          // console.log('fail')
        }
      })
    } else if (activityDetailAddress) {
      wx.setClipboardData({
        data: activityDetailAddress,
        success() {
          showToast('复制成功！')
        }
      })
    }
  },

  swiperChange(e: any) {
    const {
      detail: { current }
    } = e
    this.setData({
      'hotCompanySwiper.current': current
    })
  },

  backTop() {
    this.setData({ scrollWithAnimation: true, scrollTop: 0 })
    setTimeout(() => {
      this.setData({ scrollWithAnimation: false })
    }, 300)
  },

  setBackTop: throttle(300, ({ scrollTop }) => {
    const { scrollViewHeight } = _this.data
    const isShow = scrollTop > scrollViewHeight
    _this.setData({
      showBackTop: isShow
    })
  }),

  scroll(event: any) {
    const { scrollTop } = event.detail
    const { tabOffsetTop, headerHeight } = this.data
    const flag = tabOffsetTop - headerHeight <= scrollTop

    this.setData({ isTabFixed: flag })

    this.setBackTop({ scrollTop })
  },

  scrolltolower() {
    const { activeTab, isEnd } = this.data
    if (activeTab == 'company' && !isEnd) {
      const { page } = this.data.formData
      this.setData({ 'formData.page': page + 1 })
      this.getActivityCompanyList(true)
    }
  },

  showPopup(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { visible }
      }
    } = e
    this.setData({ [`visible.${visible}`]: true })
  },

  handleLabel(key: string, label: string = '', length: number = 0) {
    const labelMap: { [key: string]: string } = {
      areaId: '所在地区',
      majorId: '需求学科',
      categoryId: '职位类型',
      type: '单位类型'
    }
    const defaultLabel = labelMap[key]
    let labelTxt = ''

    if (length === 0) {
      labelTxt = defaultLabel
    } else if (length === 1) {
      labelTxt = label
    } else {
      labelTxt = `${defaultLabel}·${length}`
    }

    this.setData({ [`label.${key}`]: labelTxt })
  },

  handleChange(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { formKey }
      },
      detail: { label, value = [] }
    } = e

    this.setData({
      [`formData.${formKey}`]: value
    })

    const [firstLabel] = label
    const { length } = value

    this.handleLabel(formKey, firstLabel, length)

    this.getActivityCompanyList()
  },

  getActivityCompanyList(isAppend: boolean = false) {
    const { activityId, formData } = this.data
    Object.keys(formData).forEach((key) => {
      const value = formData[key]
      const isArray = Array.isArray(value)
      if (isArray) {
        formData[key] = value.join()
      }
    })
    if (!isAppend) {
      formData.page = 1
    }
    getActivityCompany({ activityId, ...formData })
      .then((resp: any) => {
        const { list, isEnd } = resp
        const { companyList } = this.data
        this.setData({ companyList: isAppend ? companyList.concat(list) : list, isEnd: isEnd == 1 })
      })
      .catch(() => {})
  },

  handleApply(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { type }
      }
    } = e
    const { applyLinkType, applyLink, applyLinkCompanyType, applyLinkCompany } = this.data.activityInfo

    const openType = type === '1' ? applyLinkCompanyType : applyLinkType
    const openLink = type === '1' ? applyLinkCompany : applyLink

    // 报名链接类型；2:内部链接(系统内部，非小程序内部)；0:第三方链接
    if (openType == '2') {
      toWebPage(openLink)
    } else if (openType == '1') {
      jump(openLink, openType)
    } else {
      wx.setClipboardData({
        data: openLink,
        success() {
          showToast('该链接暂不支持小程序直接访问，已复制可浏览器打开')
          // showToast('已复制报名链接，请至浏览器打开报名')
        }
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { activityInfo } = this.data
    return {
      title: activityInfo.name
    }
  },
  onShareTimeline() {
    const { activityInfo } = this.data
    return {
      title: activityInfo.name
    }
  },

  // 使用富文本链接处理mixin
  ...htmlLinkHandlerMixin
})
