@use 'styles/variables' as *;

page {
  display: flex;
  flex-direction: column;
  background-color: #f4f6fb;
}

.gather-container {
  flex-grow: 1;
  box-sizing: border-box;
  min-height: 80vh;

  .container {
    padding-top: 30rpx;
    background: url(#{$assets}/nav/primary.png) no-repeat left -136rpx/100% auto, #f4f6fb;
  }
}

#new-nav-bar {
  .t-navbar__content {
    background: url(#{$assets}/nav/primary.png) no-repeat center top/cover, #f4f6fb;
  }
}

.swiper-wrapper {
  margin: 0 30rpx 58rpx;
}

.hot-wrapper {
  margin-bottom: 60rpx;

  .wrapper-title {
    margin-left: 30rpx;
    width: 148rpx;
    height: 36rpx;
    background: url(#{$assets}/activity/gather/hot.png) no-repeat left/contain;
    margin-bottom: 30rpx;
  }

  .scroll-content {
    margin-left: 30rpx;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-right: 10rpx;

    &::-webkit-scrollbar {
      display: none;
    }

    .list {
      flex-shrink: 0;
      width: 280rpx;
      border-radius: 12rpx;
      overflow: hidden;
      background-color: $color-white;
      padding-bottom: 20rpx;
      margin-right: 20rpx;
      position: relative;

      .time {
        position: absolute;
        font-size: 20rpx;
        right: 0;
        top: 0;
        box-sizing: border-box;
        padding: 0 9rpx;
        line-height: 30rpx;
        max-width: 258rpx;
        @include utils-ellipsis;
        color: $color-white;
        background-color: $font-color;
        border-radius: 0rpx 12rpx 0rpx 12rpx;
      }

      .cover {
        width: 100%;
        height: 160rpx;
        margin-bottom: 12rpx;
      }

      .title {
        font-weight: bold;
        margin-bottom: 24rpx;
        padding: 0 17rpx 0;
        height: 72rpx;
        @include utils-ellipsis-lines(2, 36rpx, 24rpx);
      }

      .bottom {
        position: relative;
        padding: 0 17rpx 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .total {
          font-size: 20rpx;
          color: $font-color-label;
          @include utils-ellipsis;
        }

        .apply {
          position: absolute;
          right: 17rpx;
          padding: 0 13rpx;
          font-size: 24rpx;
          line-height: 40rpx;
          border-radius: 40rpx;
          color: $color-white;
          background-color: #ffa000;

          &.disabled {
            background-color: #ffd080;
          }
        }
      }
    }
  }
}

.special-wrapper {
  margin-bottom: 60rpx;

  .wrapper-title {
    margin-left: 30rpx;
    width: 148rpx;
    height: 36rpx;
    background: url(#{$assets}/activity/gather/special.png) no-repeat left/contain;
    margin-bottom: 30rpx;
  }

  .scroll-content {
    margin-left: 30rpx;
    overflow-x: auto;
    display: flex;
    flex-wrap: nowrap;
    padding-right: 10rpx;

    &::-webkit-scrollbar {
      display: none;
    }

    .list {
      flex-shrink: 0;
      width: 400rpx;
      border-radius: 12rpx;
      overflow: hidden;
      background-color: $color-white;
      padding-bottom: 14rpx;
      margin-right: 20rpx;

      .cover {
        width: 100%;
        height: 160rpx;
        margin-bottom: 12rpx;
      }

      .title {
        font-weight: bold;
        padding: 0 17rpx 0;
        height: 72rpx;
        @include utils-ellipsis-lines(2, 36rpx, 24rpx);
      }
    }
  }
}

.activity-wrapper {
  margin-bottom: 60rpx;

  .wrapper-title {
    margin-left: 30rpx;
    width: 148rpx;
    height: 36rpx;
    background: url(#{$assets}/activity/gather/all-activity.png) no-repeat left/contain;
    margin-bottom: 10rpx;
  }

  .filter-content {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    position: sticky;
    top: -2rpx;
    z-index: 5;
    overflow-x: scroll;
    padding: 20rpx 30rpx 20rpx 30rpx;

    &::-webkit-scrollbar {
      display: none;
    }

    &.fixed {
      background-color: $color-white;
      box-shadow: 0rpx 9rpx 9rpx 0rpx rgba(51, 51, 51, 0.02);

      .filter-item {
        background-color: #f7f7f7;
      }
    }

    &::-webkit-scrollbar {
      display: none;
    }

    .filter-item {
      padding: 0 35rpx 0 20rpx;
      background-color: $color-white;
      line-height: 52rpx;
      font-size: 24rpx;
      color: $font-color-basic;
      white-space: nowrap;
      border-radius: 8rpx;
      position: relative;
      border: 2rpx solid #f7f7f7;

      &::after {
        position: absolute;
        content: '';
        border-left: 6rpx solid #c7c7c7;
        border-right: 6rpx solid transparent;
        border-top: 6rpx solid transparent;
        border-bottom: 6rpx solid transparent;
        transform: rotate(45deg);
        right: 10rpx;
        bottom: 10rpx;
      }

      &.has-select {
        border-color: #ffa000;
        color: #ffa000;
        background-color: #fffaf1;

        &::after {
          border-left-color: #ffa000;
        }
      }

      & + .filter-item {
        margin-left: 10rpx;
      }
    }
  }

  .list-content {
    padding: 0 30rpx 40rpx;

    .list {
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;
      background-color: $color-white;

      & + .list {
        margin-top: 20rpx;
      }

      .abs {
        font-size: 20rpx;
        color: $color-white;
        position: absolute;
        display: flex;
        left: 0;
        top: 0;
        line-height: 34rpx;
        background-color: rgba($color: $font-color, $alpha: 0.8);
        border-radius: 12rpx 0rpx 12rpx 0rpx;
        overflow: hidden;

        .type {
          padding: 0 13rpx;
          background-color: #ffa000;

          &.on {
            background-color: #51bd69;
          }
        }

        .text {
          padding: 0 13rpx;
        }
      }

      .cover {
        width: 690rpx;
        height: 310rpx;
        margin-bottom: 11rpx;
      }

      .title {
        font-weight: bold;
        padding: 0 20rpx;
        margin-bottom: 13rpx;
        @include utils-ellipsis-lines(2, 46rpx, 30rpx);

        .status {
          display: inline-flex;
          align-items: center;
          position: relative;
          font-size: 28rpx;
          font-weight: normal;
          line-height: 1;
          vertical-align: middle;
          transform: translateY(-2rpx);
          margin-right: 10rpx;

          &::before {
            content: '';
            display: inline-block;
            width: 8rpx;
            height: 8rpx;
            border-radius: 50%;
            margin-right: 15rpx;
          }

          // 状态；-1报名中；1:待举办；2：即将开始；3:进行中；4:已结束
          $status: (
              name: status-1,
              color: #5386ff
            ),
            (
              name: status-2,
              color: #fa635c
            ),
            (
              name: status--1,
              color: #ffa000
            ),
            (
              name: status-3,
              color: #ffa000
            ),
            (
              name: status-4,
              color: $font-color-label
            );

          @each $item in $status {
            $name: map-get(
              $map: $item,
              $key: 'name'
            );
            $color: map-get(
              $map: $item,
              $key: 'color'
            );

            &.#{$name} {
              color: $color;

              &::before {
                background-color: $color;
              }
            }
          }
        }
      }

      .middle {
        font-size: 24rpx;
        color: $font-color-basic;
        padding: 0 20rpx;
        margin-bottom: 20rpx;

        .time {
          display: flex;
          align-items: center;

          .local {
            font-size: 20rpx;
          }
        }

        .address {
          @include utils-ellipsis;
        }
      }

      .tag-content {
        display: flex;
        flex-wrap: nowrap;
        margin: 0 20rpx;
        overflow-x: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        .tag {
          margin-bottom: 20rpx;
          flex-shrink: 0;
          font-size: 20rpx;
          padding: 0 12rpx;
          line-height: 34rpx;
          color: #296aff;
          background-color: #e5edff;
          border-radius: 4rpx;

          & + .tag {
            margin-left: 10rpx;
          }
        }
      }

      .bottom {
        margin: 0 20rpx;
        display: flex;
        padding: 20rpx 0;
        border-top: 2rpx solid #ebebeb;
        justify-content: flex-end;

        .company {
          font-size: 24rpx;
          flex-grow: 1;
          @include utils-ellipsis;

          .label {
            display: inline-block;
            color: $font-color-basic;
          }

          .total {
            display: inline-block;
            color: #ffa000;
          }
        }

        .status {
          flex-shrink: 0;
          font-size: 28rpx;
          font-weight: bold;
          color: $color-white;
          padding: 0;
          line-height: 48rpx;
          width: 152rpx;
          height: 48rpx;
          background-color: #ffa000;
          border-radius: 24rpx;

          &.disabled {
            background-color: #ffd080;
          }
        }
      }
    }

    .tips {
      margin-top: 40rpx;
      text-align: center;
      color: $font-color-basic;
    }
  }

  .empty {
    padding-top: 360rpx;
    text-align: center;
    padding-bottom: 30rpx;
    font-size: 28rpx;
    color: $font-color-basic;
    background: url(#{$assets}/activity/common/empty.png) no-repeat center 50rpx/400rpx 290rpx;
  }
}
