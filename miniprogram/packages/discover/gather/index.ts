import { getElClientRect, showToast, showLoading } from '@/utils/util'
import { throttle } from 'throttle-debounce'

import { jump } from '@/utils/url'

import { getActivityGather, getActivityList } from '@/api/discover'

const { windowHeight, screenHeight, headerOffsetHeight } = getApp().globalData

let _this = <any>null

Page({
  /**
   * 页面的初始数据
   */
  data: {
    showBackTop: false,
    scrollTop: 0,

    activityScrollHeight: screenHeight - headerOffsetHeight,
    defaultInterval: 5000,
    showcaseHF: [],
    showcaseHFImg: [],

    isFilterFixed: false,
    filterOffsetTop: 0,

    activityHot: [],
    showcaseTSZC: [],
    filterOptions: {
      areaId: [],
      activityStatus: [],
      featureTag: [],
      activityType: [],
      activityToHold: []
    },
    filterDefaultLabel: {
      areaId: '热门地区',
      activityType: '活动系列',
      activityStatus: '活动状态',
      activityToHold: '举办方式',
      featureTag: '特色标签'
    },

    filterLabel: {
      areaId: '热门地区',
      activityType: '活动系列',
      activityStatus: '活动状态',
      activityToHold: '举办方式',
      featureTag: '特色标签'
    },
    activityEnd: false,
    activityList: [],

    limit: 5,
    popupVisible: false,
    popupTitle: '热门地区',
    popupKey: 'areaId',

    formData: {
      areaId: [],
      activityStatus: [],
      featureTag: [],
      activityType: [],
      activityToHold: [],
      page: 1
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    _this = this

    showLoading('加载中...')
    await this.getDetail()
    wx.hideLoading()

    this.getFilterOffsetTop()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  getFilterOffsetTop() {
    setTimeout(async () => {
      const { top } = await (<any>getElClientRect('.filter-content'))
      this.setData({ filterOffsetTop: top })
    }, 310)
  },

  scroll(e: WechatMiniprogram.CustomEvent) {
    const _this = this
    const {
      detail: { scrollTop }
    } = e

    const throttleFn = throttle(300, ({ scrollTop }) => {
      const { filterOffsetTop } = _this.data

      const isFixed = scrollTop + headerOffsetHeight > filterOffsetTop
      const isShow = scrollTop > windowHeight
      _this.setData({
        isFilterFixed: isFixed,
        showBackTop: isShow
      })
    })

    throttleFn({ scrollTop })
  },

  handleFilterOptions(options: { [key: string]: any[] } = {}) {
    const optionsOjb: any = {}

    const { filterOptions } = this.data

    Object.keys(filterOptions).forEach((key) => {
      const value = options[key]
      const { length } = value
      if (length) {
        optionsOjb[key] = value
      }
    })
    this.setData({ filterOptions: optionsOjb })
  },

  async getDetail() {
    const resp = await getActivityGather()
    const { showcaseHF, activityHot, showcaseTSZC, activitySearch, activityList } = resp

    const showcaseHFImg = showcaseHF.map((item: any) => item.imageUrl)
    this.setData({
      showcaseHF,
      showcaseHFImg,
      activityHot,
      showcaseTSZC,
      activityList: activityList.list
    })

    this.handleFilterOptions(activitySearch)
  },

  getList(isAppend = false) {
    showLoading('加载中...')

    const { filterOptions, formData, activityList, isFilterFixed, filterOffsetTop } = this.data

    let query: { [key: string]: any } = {}

    Object.keys(filterOptions).forEach((key: string) => {
      const value = formData[key as keyof typeof formData]
      query[key] = Array.isArray(value) ? value.join() : value
    })

    getActivityList({ page: formData.page, ...query })
      .then((resp: any) => {
        const {
          limit,
          list,
          list: { length }
        } = resp
        this.setData({
          activityEnd: length < limit,
          activityList: isAppend ? activityList.concat(list) : list
        })
        if (isFilterFixed && !isAppend) {
          const scrollTop = filterOffsetTop - headerOffsetHeight
          this.setData({ scrollTop: scrollTop })
        }

        wx.hideLoading()
      })
      .catch(() => {
        wx.hideLoading()
      })
  },

  openFilterPopup(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { key }
      }
    } = e
    const { filterDefaultLabel } = this.data
    this.setData({
      popupKey: key,
      popupTitle: filterDefaultLabel[key as keyof typeof filterDefaultLabel],
      popupVisible: true
    })
  },

  handleChange(e: WechatMiniprogram.CustomEvent) {
    const { popupKey, filterDefaultLabel } = this.data
    const {
      detail: {
        label,
        value,
        value: { length }
      }
    } = e

    let labelText = length === 1 ? label[0] : filterDefaultLabel[popupKey as keyof typeof filterDefaultLabel]

    this.setData(
      {
        [`formData.${popupKey}`]: value,
        [`formData.page`]: 1,
        [`filterLabel.${popupKey}`]: labelText
      },
      () => {
        this.getList()
      }
    )
  },

  setBackTop: throttle(300, ({ scrollTop }) => {
    const { scrollViewHeight } = _this.data
    const isShow = scrollTop > scrollViewHeight
    _this.setData({
      showBackTop: isShow
    })
  }),

  backTop() {
    this.setData({ scrollTop: 0 })
  },

  scrolltolower() {
    const { activityEnd } = this.data
    if (!activityEnd) {
      const { page } = this.data.formData
      this.setData({ 'formData.page': page + 1 })
      this.getList(true)
    }
  },

  onTapBanner(e: WechatMiniprogram.CustomEvent) {
    const {
      detail: { index }
    } = e

    const { url, targetLinkType, id } = this.data.showcaseHF[index]
    jump(url, targetLinkType, id)
  },

  setClipboardData(content: string, type: number) {
    // type 1活动图片 2活动按钮
    wx.setClipboardData({
      data: content,
      success() {
        // showToast(type == 1 ? '已复制页面链接，请至浏览器打开' : '已复制报名链接，请至浏览器打开报名')
        showToast('该链接暂不支持小程序直接访问，已复制可浏览器打开')
      }
    })
  },

  sessionTab(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { item, type }
      }
    } = e

    const { isZhaopinhui, activityUrl, activityTargetLinkType, btnTargetLinkType, btnLink } = item
    // type 1点击主图，2点击按钮
    // activityTargetLinkType 图片跳转类型 0 复制 其他走广告位一套
    if (type == 1) {
      if (isZhaopinhui) {
        jump(activityUrl, activityTargetLinkType)
      } else {
        if (activityTargetLinkType > 0) {
          jump(activityUrl, activityTargetLinkType)
        } else {
          if (activityUrl) {
            this.setClipboardData(activityUrl, 1)
          }
        }
      }
    } else {
      if (btnTargetLinkType == 0) {
        this.setClipboardData(btnLink, 2)
      } else {
        jump(btnLink, btnTargetLinkType)
      }
    }
  },

  specialTap(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: {
          item: { url, targetLinkType, id }
        }
      }
    } = e
    jump(url, targetLinkType, id)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
