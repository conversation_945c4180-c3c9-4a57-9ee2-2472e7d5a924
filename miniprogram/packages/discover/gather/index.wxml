<!-- 调用此组件务必在引用处添加id="new-nav-bar"，因为在chat-socket.ts文件需要调用其方法 -->
<new-nav-bar id="new-nav-bar" title="招聘会" type="1" bind:clickNav="checkLogin"></new-nav-bar>

<scroll-view class="gather-container" enable-passive scroll-y using-sticky style="height: {{ activityScrollHeight }}px;" scroll-top="{{ scrollTop }}" scroll-with-animation="{{ scrollWithAnimation }}" scroll-with-animation bind:scroll="scroll" bind:scrolltolower="scrolltolower">
  <view class="container">
    <t-swiper wx:if="{{ showcaseHFImg.length }}" t-class="swiper-wrapper" height="300rpx" autoplay interval="{{ defaultInterval }}" navigation="{{ { type: 'dots-bar' } }}" list="{{ showcaseHFImg }}" bind:click="onTapBanner"> </t-swiper>

    <view class="hot-wrapper" wx:if="{{ activityHot.length }}">
      <view class="wrapper-title"></view>

      <view class="scroll-content">
        <view class="list" wx:for="{{ activityHot }}" bind:tap="sessionTab" data-type="1" wx:key="index" data-item="{{ item }}">
          <view class="time">{{ item.activityTime }}</view>

          <image class="cover" src="{{ item.image }}" mode="aspectFill" />

          <view class="title">{{ item.name }}</view>

          <view class="bottom">
            <view class="total">{{ item.click }}</view>
            <button class="apply {{ item.btnType == 2 ? 'disabled' : '' }}" disabled="{{ item.btnIsClick == 2 }}" catch:tap="sessionTab" data-type="2" data-item="{{ item }}">{{ item.btnText }}</button>
          </view>
        </view>
      </view>
    </view>

    <view class="special-wrapper" wx:if="{{ showcaseTSZC.length }}">
      <view class="wrapper-title"></view>

      <view class="scroll-content">
        <view class="list" wx:for="{{ showcaseTSZC }}" wx:key="index" bind:tap="specialTap" data-item="{{ item }}">
          <image class="cover" src="{{ item.imageUrl }}" mode="aspectFill" />

          <view class="title">{{ item.title }}</view>
        </view>
      </view>
    </view>

    <view class="activity-wrapper">
      <view class="wrapper-title"></view>

      <view class="filter-content {{ isFilterFixed ? 'fixed' : '' }}">
        <view class="filter-item {{ formData[key].length ? 'has-select' : '' }}" wx:for="{{ filterOptions }}" wx:key="key" wx:for-item="value" wx:for-index="key" wx:key="key" data-key="{{ key }}" bind:tap="openFilterPopup">{{ filterLabel[key] }}</view>
      </view>

      <view class="list-content" wx:if="{{ activityList.length }}">
        <view class="list" wx:for="{{ activityList }}" wx:key="key" bind:tap="sessionTab" data-type="1" data-item="{{ item }}">
          <image class="cover" src="{{ item.imageMiniMasterUrl || item.mainImgFileUrl }}" mode="aspectFill" />

          <view class="abs">
            <view class="type {{ item.toHoldType == 1 ? 'on' : '' }}">{{ item.toHoldTypeText }}</view>
            <view class="text">{{ item.typeText }}</view>
          </view>

          <view class="title">
            <view class="status status-{{ item.activityChildStatus }}">{{ item.activityChildStatusText }}</view>
            {{ item.name }}
          </view>

          <view class="middle">
            <view class="time"
              >活动时间：{{ item.activityTime }}
              <!-- <view class="local">（当地时间）</view> -->
            </view>
            <view class="address"> {{ item.seriesType == 2 ? '活动场次：' : item.toHoldType == 1 ? '活动平台：' : '活动地点：' }}{{ item.area }}</view>
          </view>

          <view class="tag-content">
            <view class="tag" wx:for="{{ item.tag }}" wx:key="key">{{ item }}</view>
          </view>

          <view class="bottom">
            <view class="company">
              <view class="label">参会单位：</view>
              <view class="total">{{ item.participationCompanyAmount }}</view>
              {{ item.participationText }}
            </view>
            <button class="status {{ item.btnType == 2 ? 'disabled' : '' }}" disabled="{{ item.btnIsClick == 2 }}" catch:tap="sessionTab" data-type="2" data-item="{{ item }}">{{ item.btnText }}</button>
          </view>
        </view>

        <view class="tips" wx:if="{{ activityEnd }}">更多场次持续更新中，敬请关注…</view>
      </view>

      <view class="empty" wx:else>暂无相关活动，请修改筛选条件试试</view>
    </view>
  </view>
</scroll-view>

<view wx:if="{{ showBackTop }}" class="back-top-trigger" bind:tap="backTop"></view>

<!-- 筛选项 -->
<basic-popup visible="{{ popupVisible }}" model="{{ formData[popupKey] }}" title="{{ popupTitle }}" limit="{{ limit }}" column="3" options="{{ filterOptions[popupKey] }}" options-label="name" options-value="id" bind:change="handleChange"></basic-popup>
