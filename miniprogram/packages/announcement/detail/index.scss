@use 'styles/variables' as *;

$label-color: #4fbc67;

.details-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $page-background;
}

@mixin sticky {
  .root-partal {
    display: block;
  }

  .tabs-sticky {
    width: 100%;
    display: flex;
    background: url(#{$assets}/announcement/tab-bg.png) no-repeat center/cover;
    left: 0;
    right: 0;
    z-index: 99;

    .item {
      box-sizing: border-box;
      flex-grow: 1;
      font-size: 28rpx;

      .label {
        position: relative;
        line-height: 88rpx;
        display: inline-flex;
        justify-content: center;
      }

      &.active {
        flex-shrink: 0;
        flex-grow: 0;
        width: 56.8%;
        background-image: url(#{$assets}/announcement/tab-active-bg.png);
        background-repeat: no-repeat;
        background-size: auto 100%;
        color: $color-primary;

        .label {
          &::after {
            content: '';
            position: absolute;
            width: 110rpx;
            height: 4rpx;
            background-color: $color-primary;
            bottom: 6rpx;
          }
        }
      }

      &:first-child {
        padding-left: 116rpx;

        &.active {
          background-position: right 0 top 0;
        }
      }

      &:last-child {
        text-align: right;
        padding-right: 92rpx;

        &.active {
          background-position: left 0 top 0;
        }
      }
    }
  }

  .isFixed {
    position: fixed;
  }
}

@include sticky;

.flex-grow {
  flex-grow: 1;
  overflow: hidden;
}

.scroll-content {
  padding: 2rpx 0;

  .card {
    background-color: $color-white;
    border-radius: $border-radius;
    margin: 0 30rpx 20rpx;
    overflow: hidden;
  }

  .announcement-basic {
    padding: 20rpx 30rpx 36rpx;
    margin-top: 20rpx;

    .title {
      font-weight: bold;
      font-size: 36rpx;
      line-height: 54rpx;
    }

    .unit {
      display: flex;
      padding: 20rpx 0 30rpx;

      .logo {
        flex-shrink: 0;
        position: relative;
        height: 70rpx;

        &.vip {
          &::after {
            content: '';
            display: block;
            position: absolute;
            right: 0;
            top: 42rpx;
            width: 28rpx;
            height: 28rpx;
            background: url(#{$assets}/icon/certification.png) no-repeat center/contain;
          }
        }
      }

      .logo-img {
        width: 70rpx;
        height: 70rpx;
        border-radius: 50%;
      }

      .info {
        flex-grow: 1;
        padding: 0 20rpx;
        overflow: hidden;
      }

      .name {
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .time {
        font-size: 24rpx;
        color: $font-color-label;
      }

      .attention {
        align-self: center;
        flex-shrink: 0;
        width: 125rpx;
        line-height: 50rpx;
        font-weight: bold;
        color: #fff;
        background-color: $color-primary;
        border: 2rpx solid $color-primary;
        border-radius: 28rpx;
        text-align: center;

        &.has-attention {
          background-color: #fff;
          border-color: $border-color;
          color: $font-color-label;
        }
      }
    }

    .desc {
      background-color: #f3f8fd;
      border-radius: $border-radius;
      padding: 10rpx 20rpx 14rpx;

      .row {
        display: flex;
        padding: 10rpx 0;
        font-size: 24rpx;
      }

      .top {
        justify-content: space-between;

        .info {
          font-size: 28rpx;
        }

        .amount {
          color: $color-primary;
        }

        .arrow {
          color: $color-primary;
          display: flex;
          align-items: center;
        }
      }

      .date {
        .label {
          padding-left: 36rpx;
          background: url(#{$assets}/icon/time-black.png) no-repeat left center/26rpx;
        }
      }

      .label {
        color: #666;
        flex-shrink: 0;
      }

      .value {
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .detail-label {
    margin-bottom: 20rpx;
    display: flex;

    .label {
      padding: 0 15rpx;
      color: $label-color;
      font-size: 24rpx;
      font-style: italic;
      font-weight: bold;
      line-height: 37rpx;
      background: linear-gradient(-90deg, #f0ffdc, #fafff4);
      border-radius: $border-radius 0 $border-radius 0;
    }
  }

  .tabs-content {
    height: 88rpx;
    border-top-left-radius: $border-radius;
    border-top-right-radius: $border-radius;
    margin-bottom: 0;
    background-color: #fff;
    margin: 0 30rpx;
    overflow: hidden;
  }

  .tab-panel {
    &.job-panel {
      padding-bottom: 20rpx;
    }
    .more-box {
      display: flex;
      justify-content: center;
      .more {
        display: flex;
        align-items: center;
        height: 64rpx;
        margin-top: 20rpx;
        padding: 0 30rpx;
        color: #FFA000;
        line-height: 64rpx;
        font-weight: 500;
        font-size: 28rpx;
        background: #FFFAF1;
        border-radius: 32rpx;
      }
      image {
        width: 26rpx;
        height: 26rpx;
        margin-right: 13rpx;
      }
      text {
        display: inline-block;
        max-width: 250rpx;
        font-weight: bold;
        @include utils-ellipsis;
      }
    }
    .filter-box {
      display: flex;
      align-items: center;
      margin: 0 30rpx;
      padding: 16rpx 30rpx;
      background-color: #fff;

      &.filter-sticky {
        box-sizing: border-box;
        width: 100%;
        margin: 0;
      }

      .filter {
        display: flex;
        flex: 1;
        box-sizing: border-box;
        top: 0;
      }

      .item {
        position: relative;
        box-sizing: border-box;
        padding: 0 34rpx 0 20rpx;
        line-height: 48rpx;
        font-size: 24rpx;
        border-radius: 8rpx;
        max-width: 200rpx;
        background: #F7F7F7;
        border: 2rpx solid #f4f6fb;
        @include utils-ellipsis;

        &::after {
          content: '';
          position: absolute;
          width: 0;
          height: 0;
          right: 14rpx;
          bottom: 14rpx;
          display: block;
          border: 4rpx solid #c7c7c7;
          border-left-color: transparent;
          border-top-color: transparent;
        }

        & ~ .item {
          margin-left: 20rpx;
        }

        &.has-condition {
          color: $color-primary;
          background-color: $color-primary-background;
          border-color: $color-primary;

          &::after {
            border-right-color: $color-primary;
            border-bottom-color: $color-primary;
          }
        }
      }

      .not-login {
        padding: 0 10rpx;
        font-size: 20rpx;
        font-weight: 500;
        line-height: 33rpx;
        color: $color-primary;
        background: #FFF6E6;
        border-radius: 8rpx;
        position: relative;
        &::before {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translate(-100%,-50%);
          content: '';
          width: 0;
          height: 0;
          border-top: 3px solid transparent;
          border-bottom: 3px solid transparent;
          border-right: 6px solid #FFF6E6; /* 设置三角形的颜色 */
        }
      }
      .clear {
        font-size: 24rpx;
        color: $font-color-basic;
      }
    }
  }

  .empty {
    margin: 0 30rpx;
    padding: 60rpx 0;
    background-color: $color-white;
    border-radius: 0 0 16rpx 16rpx;
  }

  .announcement-detail {
    margin-top: 0;
    padding: 30rpx;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    line-height: 1.8;
    --up-height: 0px;
    position: relative;

    &.up {
      height: var(--up-height);
    }

    .more {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 100rpx 0 30rpx;
      color: $color-primary;
      font-weight: bold;
      display: flex;
      justify-content: center;
      align-items: center;
      background: linear-gradient(
        rgba($color: $color-white, $alpha: 0.3),
        rgba($color: $color-white, $alpha: 1) 55%,
        $color-white
      );

      &::after {
        content: '';
        width: 20rpx;
        height: 20rpx;
        margin-left: 18rpx;
        background: url('#{$assets}/company/drop-down.png') no-repeat center / 20rpx;
      }
    }

    .up {
      position: relative;
      padding: 30rpx 0 0rpx;
      background-color: transparent;

      &::after {
        transform: rotate(180deg);
      }
    }
  }

  .attachment-download {
    padding: 0 30rpx;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      padding: 26rpx 0 16rpx;
    }

    .item {
      padding: 24rpx 0;
      border-bottom: 2rpx solid $border-color;
      display: flex;
      padding-right: 40rpx;
      background: url(#{$assets}/icon/download.png) no-repeat right 0 center/36rpx;

      $fileType: word, pdf, excel;

      @each $item in $fileType {
        &.#{$item} {
          .name {
            background: url(#{$assets}/icon/#{$item}.png) no-repeat left/36rpx;
          }
        }
      }

      .name {
        padding-left: 56rpx;
        flex-grow: 1;
        max-width: 470rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .announcement-popularity {
    margin: 0 30rpx 20rpx;
    padding: 30rpx;
    background-color: $color-white;
    border-radius: $border-radius;

    &.is-show {
      .description {
        .match,
        .num {
          filter: blur(0);
        }
      }
    }

    .title {
      margin-bottom: 40rpx;
      font-size: 36rpx;
      font-weight: bold;
    }

    .description {
      margin-bottom: 30rpx;
      line-height: 42rpx;

      .match,
      .num {
        color: $color-primary;
        font-weight: bold;
        filter: blur(4px);
      }
    }

    .button {
      margin-top: 40rpx;
      text-align: center;

      .button-text {
        display: inline-block;
        padding: 0 57rpx;
        color: $color-primary;
        font-weight: bold;
        line-height: 70rpx;
        box-shadow: 0 0 0 2rpx #ffeac7;
        border-radius: 35rpx;
      }
    }
  }

  .unit-info {
    padding: 30rpx;
    display: flex;
    align-items: center;
    border-radius: $border-radius;
    background: url('//img.gaoxiaojob.com/uploads/mini/icon/company-card-background.png') no-repeat center / cover;

    .logo {
      width: 80rpx;
      height: 80rpx;
      position: relative;
      margin-right: 20rpx;
      flex-shrink: 0;
      border: 2rpx solid #e2e2e2;
      border-radius: 12rpx;
      overflow: hidden;
      padding: 6rpx;

      &.certification {
        &::before {
          content: '';
          display: block;
          width: 28rpx;
          height: 28rpx;
          position: absolute;
          bottom: 0;
          right: 0;
          background: url(#{$assets}/icon/certification.png) no-repeat center/contain;
        }
      }

      .img {
        width: 100%;
        height: 100%;
      }
    }

    .info {
      flex-grow: 1;
      overflow: hidden;
    }

    .name {
      font-size: 32rpx;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .scale {
      font-size: 24rpx;
      color: $font-color-label;
      margin-top: 8rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text {
        &::after {
          content: ' · ';
        }
        &:last-child {
          &::after {
            content: '';
          }
        }
      }
    }
  }

  .announcement-recommend {
    border-radius: $border-radius;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      padding: 26rpx 30rpx 16rpx;
    }

    .swiper {
      height: var(--swiper-height);
    }

    .list {
      position: relative;

      .name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 30rpx;
        right: 30rpx;
        display: block;
        height: 2rpx;
        background-color: $border-color;
      }
    }

    .swiper-paging {
      height: 60rpx;
      position: relative;

      --td-swiper-nav-dot-size: 10rpx;
      --td-swiper-nav-dots-bar-active-width: 20rpx;
      --td-swiper-radius: #{$border-radius};
      --td-swiper-nav-dot-color: #c7c7c7;
      --td-swiper-nav-dot-active-color: #{$color-primary};

      .t-swiper-nav__dots-bar-item {
        margin: 0 5rpx;
      }
    }
  }

  .tips {
    padding: 0 30rpx 20rpx;
  }

  .job-item {
    margin-bottom: 20rpx;
    padding: 22rpx 30rpx 30rpx;
    border-radius: $border-radius;

    .top {
      display: flex;

      .title {
        line-height: 48rpx;
        font-size: 32rpx;
        font-weight: bold;
        flex-grow: 1;
      }

      .label {
        display: inline-block;
        width: 30rpx;
        height: 30rpx;
        color: $label-color;
        font-size: 22rpx;
        text-align: center;
        line-height: 30rpx;
        vertical-align: middle;
        background-color: #f0ffdc;
        border-radius: 4px;
      }

      .salary {
        margin-left: 100rpx;
        flex-shrink: 0;
        color: $color-point;
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .announcement {
      @include utils-ellipsis;
      margin: 20rpx 0;
      margin-bottom: 16rpx;
    }

    .bottom {
      display: flex;
      font-size: 24rpx;

      .tag-content {
        display: flex;
        flex-wrap: nowrap;
        flex-grow: 1;
        overflow: hidden;
        margin-top: 16rpx;
        .tag {
          line-height: 44rpx;
          padding: 0 12rpx;
          border-radius: 4rpx;
          background-color: #f4f6fb;
          color: $font-color-basic;
          white-space: nowrap;

          & + .tag {
            margin-left: 12rpx;
          }
        }
      }

      .time {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        padding-left: 34rpx;
        margin-left: 100rpx;
        color: $font-color-tips;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/time.png') no-repeat left/28rpx;
      }
    }

    &.offline {
      background:
        url(#{$assets}/icon/offline.png) no-repeat right 32rpx top / 110rpx 96rpx,
        #fff;

      .top,
      .announcement,
      .bottom {
        opacity: 0.6;
      }

      .salary {
        display: none;
      }
    }

    &:first-child {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.footer-fixed {
  display: flex;
  padding: 20rpx 40rpx 20rpx 0;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 2rpx solid #e9e9e9;

  .btn {
    width: 130rpx;
    text-align: center;
    padding-top: 56rpx;
    position: relative;
    font-size: 28rpx;

    // & + .btn {
    //   &::after {
    //     content: '';
    //     display: block;
    //     position: absolute;
    //     background-color: #c5c5c5;
    //     top: 4rpx;
    //     bottom: 4rpx;
    //     width: 2rpx;
    //   }
    // }
  }

  .share {
    background: url(#{$assets}/icon/share.png) no-repeat center top 2rpx/38rpx;
    font-weight: normal;
    height: 95rpx;
  }

  .like {
    background: url(#{$assets}/icon/like.png) no-repeat center top 2rpx/38rpx;

    &.like-primary {
      background: url(#{$assets}/icon/like-primary.png) no-repeat center top 2rpx/38rpx;
    }
  }

  .deliver {
    width: 438rpx;
    height: 88rpx;
    background: $color-primary;
    color: #fff;
    border-radius: 44rpx;
  }
}

.announcement-info {
  .header {
    text-align: center;
    padding: 35rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    border-bottom: 2rpx solid $border-color;
  }

  .content {
    padding: 20rpx 30rpx 50rpx;
  }

  .row {
    margin-top: 36rpx;

    .label {
      font-size: 30rpx;
      color: $font-color-label;
      margin-bottom: 10rpx;
    }

    &.amount {
      .label {
        color: inherit;
        font-weight: bold;

        .number {
          color: $color-primary;
        }
      }
    }
  }
}

.report-dialog-content {
  color: $font-color-basic;
  font-size: 32rpx;
  text-align: center;
  line-height: 48rpx;
}

.report-dialog-description {
  margin-top: 30rpx;
  color: $color-primary;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
}
