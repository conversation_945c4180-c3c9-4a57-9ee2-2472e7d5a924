<view class="details-container">
  <!-- <t-navbar title="公告详情" style="{{ headerStyle }}">
    <view slot="capsule" class="custom-capsule">
      <t-icon size="20" bind:tap="onBack" aria-role="button" aria-label="返回" name="chevron-left" class="custom-capsule__icon" />
      <view class="home-icon custom-capsule__icon" bind:tap="onGoHome"></view>
    </view>
  </t-navbar> -->

  <!-- 调用此组件务必在引用处添加id="new-nav-bar"，因为在chat-socket.ts文件需要调用其方法 -->
  <new-nav-bar id="new-nav-bar" title="公告详情" type="2" bind:clickNav="checkLogin"></new-nav-bar>

  <view class="flex-grow">
    <scroll-view style="height: {{ remainHeight }}" class="scroll-content" scroll-y upper-threshold="50" lower-threshold="50" bindscroll="onScroll" bind:scrolltolower="scrolltolowerEvent">
      <view class="card announcement-basic">
        <view class="detail-label" wx:if="{{ detailLabel }}">
          <text class="label">{{ detailLabel }}</text>
        </view>

        <view class="title">{{ announcementInfo.announcementTitle }}</view>

        <view class="unit">
          <view class="logo {{ announcementInfo.companyPackageType === 2 ? 'vip' : '' }}">
            <image class="logo-img" src="{{ announcementInfo.companyLogo }}" mode="aspectFit" bind:tap="jumpToComany" />
          </view>
          <view class="info" bind:tap="jumpToComany">
            <view class="name">{{ announcementInfo.companyName }}</view>
            <view class="time">{{ announcementInfo.refreshTime }} 发布</view>
          </view>
          <view wx:if="{{ announcementInfo.isCollectCompany != 1 }}" class="attention" bind:tap="collectCompany">关注 </view>
          <view wx:if="{{ announcementInfo.isCollectCompany == 1 }}" class="attention has-attention" bind:tap="collectCompany"> 已关注</view>
        </view>

        <view class="desc">
          <view class="row top">
            <view class="info">
              共招<i class="amount">{{ announcementInfo.announcementRecruitAmount }}</i
              >人，<i class="amount">{{ announcementInfo.allJobNumber }}</i
              >个职位
            </view>
            <view class="arrow" bind:tap="showDetail">
              查看全部
              <t-icon name="chevron-right" size="26rpx" />
            </view>
          </view>
          <view class="row">
            <view class="label">学历要求：</view>
            <view class="value">{{ announcementInfo.minEducationName }}</view>
          </view>
          <view class="row">
            <view class="label">报名方式：</view>
            <view class="value">{{ announcementInfo.applyTypeText }}</view>
          </view>
          <view class="row">
            <view class="label">工作地点：</view>
            <view class="value">{{ announcementInfo.cityName }}</view>
          </view>
          <view wx:if="{{ announcementInfo.majorName }}" class="row">
            <view class="label">需求专业(供参考)：</view>
            <view class="value">{{ announcementInfo.majorName }}</view>
          </view>
          <view class="row date">
            <view class="label">截止日期：</view>
            <view class="value">{{ announcementInfo.periodDate }}</view>
          </view>
        </view>
      </view>

      <view class="tabs-content">
        <root-portal class="root-partal" enable="{{ isSticky }}">
          <view class="tabs-sticky {{ isSticky ? 'isFixed' : '' }}" style="top:{{ fixedTop }}px">
            <view bind:tap="switchType" data-type="{{ 1 }}" class="item {{ tabType == 1 ? 'active' : '' }}">
              <view class="label">公告详情</view>
            </view>
            <view bind:tap="switchType" data-type="{{ 2 }}" class="item {{ tabType == 2 ? 'active' : '' }}">
              <view class="label">在招职位({{ onlineJobNumber }})</view>
            </view>
          </view>
        </root-portal>
      </view>

      <view class="tab-panel" hidden="{{ tabType != 1 }}">
        <view class="card announcement-detail {{ isShow ? '' : 'up' }}" style="--up-height:{{ upHeight }}px">
          <!-- <rich-text nodes="{{ announcementInfo.announcementContent }}"></rich-text> -->
          <mp-html container-style="word-break: break-word" tag-style="{{ { table: 'word-break: keep-all', td: 'border: 2rpx solid #000' } }}" scroll-table content="{{ announcementInfo.announcementContent }}" bind:linktap="handleHtmlTap" copy-link="{{ copyLink }}" />

          <view wx:if="{{ showMore }}" class="more {{ isShow ? 'up' : '' }}" bindtap="showText"> {{ isShow ? '收起内容' : '展开内容' }}</view>
        </view>

        <view class="card attachment-download" wx:if="{{ announcementInfo.fileList.length > 0 }}">
          <view class="title">附件下载</view>
          <view class="attachment-content">
            <view class="item {{ item.suffix }}" wx:for="{{ announcementInfo.fileList }}" wx:key="item" data-url="{{ item.path }}" bind:tap="copyDownloadUrl">
              <view class="name">{{ item.name }}</view>
              <view class="download"></view>
            </view>
          </view>
        </view>

        <view class="announcement-popularity {{ showMatchData ? 'is-show' : '' }}">
          <view class="title">公告热度</view>

          <view class="description"
            >该公告在同类招聘公告中的热度为 <text class="match"> {{ matchText }} </text>，目前已有 <text class="num"> {{ matchValue }} </text> 对其非常感兴趣。</view
          >

          <view class="button">
            <text class="button-text" bindtap="handleReport">{{ showMatchData ? '查看' : '解锁' }}详细分析</text>
          </view>
        </view>

        <!-- 公告热度分析 -->
        <t-dialog visible="{{ reportDialogVisible }}" title="{{ reportDialogTitle }}" confirm-btn="{{ reportConfirmButton }}" cancel-btn="{{ reportCancelButton }}" bind:confirm="handleReportConfirm" bind:cancel="handleReportCancel">
          <view slot="content">
            <view class="report-dialog-content">{{ reportDialogContent }}</view>
            <view wx:if="{{ reportDialogDescription }}" class="report-dialog-description">{{ reportDialogDescription }}</view>
          </view>
        </t-dialog>

        <navigator url="/packages/company/detail/index?id={{ announcementInfo.companyId }}" class="card unit-info">
          <view class="logo {{ announcementInfo.companyPackageType === 2 ? 'certification' : '' }}">
            <image class="img" src="{{ announcementInfo.companyLogo }}" mode="aspectFit" />
          </view>
          <view class="info">
            <view class="name">{{ announcementInfo.companyName }}</view>
            <view class="scale">
              <text wx:if="{{ announcementInfo.companyNatureName }}">{{ announcementInfo.companyNatureName }}</text>
              <text wx:if="{{ announcementInfo.companyTypeName }}">{{ announcementInfo.companyTypeName }}</text>
              <text wx:if="{{ announcementInfo.companyScaleName }}">{{ announcementInfo.companyScaleName }}</text>
            </view>
          </view>
          <t-icon name="chevron-right" size="30rpx" color="#ADADAD" />
        </navigator>
        <view class="card announcement-recommend" wx:if="{{ announcementRecommend.list.length > 0 }}">
          <view class="title">推荐公告</view>

          <view class="announcement-content" style="{{ announcementRecommend.height }}">
            <swiper class="swiper" interval="5000" autoplay duration="1000" circular="true" bindchange="announcementRecommendChange">
              <swiper-item wx:for="{{ announcementRecommend.list }}" wx:key="key" wx:for-item="list">
                <announcement-item detail="{{ item }}" wx:for="{{ list }}" wx:for-item="item" wx:key="key" c-class="list" c-class-title="name" />
              </swiper-item>
            </swiper>
          </view>

          <view class="swiper-paging" wx:if="{{ announcementRecommend.list.length > 1 }}">
            <t-swiper-nav total="{{ announcementRecommend.list.length }}" current="{{ announcementRecommend.current }}" type="dots-bar"></t-swiper-nav>
          </view>
        </view>
        <view wx:if="{{ !hasPrevPage }}" class="more-box">
          <view bind:tap="jumpToSearch" class="more">
            <image class="logo-img" src="{{ moreSearchImage }}" />
            更多“<text>{{ moreSearchParams.noticeText }}</text
            >”公告
          </view>
        </view>
        <view class="tips tips-ts" style="padding-bottom: {{ !hasPrevPage && guideHeigth }}px">
          <security-tips />
        </view>
      </view>

      <view class="tab-panel job-panel" hidden="{{ tabType != 2 }}">
        <view class="filter-box {{ isSticky ? 'isFixed filter-sticky' : '' }}" style="top:{{ fixedTop + tabsHeigth }}px" wx:if="{{ announcementInfo.allJobNumber > 5 }}">
          <view class="filter">
            <!-- 弹出选择器，如果放在filter-box里面,当吸顶的时候遮盖层会有问题，所以挪到页面底部去了 -->
            <!-- <view class="item {{ cityId.length ? 'has-condition' : '' }}" data-type="area" bindtap="handlePick"> {{ cityText }}</view>
            <picker-base data-type="city" visible="{{ areaVisible }}" value="{{ cityId }}" keys="{{ keys }}" title="地区" options="{{ areaList }}" bindchange="handlePickerChange" /> -->

            <view class="item {{ jobCategoryId.length ? 'has-condition' : '' }}" data-type="jobCategory" bindtap="handlePick">{{ jobCategoryText }}</view>

            <view class="item {{ educationId.length ? 'has-condition' : '' }}" data-type="education" bindtap="handlePick"> {{ educationText }}</view>

            <view class="item {{ majorId.length ? 'has-condition' : '' }}" data-type="major" bindtap="handlePick"> {{ majorText }}</view>
          </view>
          <view class="not-login" wx:if="{{ !isLogin }}" bindtap="checkLogin">登录筛选更匹配职位 ></view>
          <view class="clear" wx:else bindtap="reset">清除筛选</view>
        </view>
        <view wx:if="{{ jobList.length }}">
          <view class="card job-item {{ item.status == 0 ? 'offline' : '' }}" wx:for="{{ jobList }}" wx:key="item">
            <navigator url="/packages/job/detail/index?id={{ item.id }}" hover-class="none">
              <view class="top">
                <view class="title">
                  {{ item.name }}
                  <text wx:if="{{ item.isEstablishment === '1' }}" class="label">编</text>
                </view>
                <view class="salary">{{ item.wage }}</view>
              </view>

              <!-- <view class="announcement">{{ item.announcementName }}</view> -->
              <view class="bottom">
                <view class="tag-content">
                  <view class="tag" wx:if="{{ item.educationName }}">{{ item.educationName }}</view>
                  <view class="tag" wx:if="{{ item.amount }}">招{{ item.amount }}人</view>
                  <view class="tag" wx:if="{{ item.cityName }}">{{ item.cityName }}</view>
                </view>
                <view class="time">{{ item.refreshTime }}</view>
              </view>
            </navigator>
          </view>
        </view>
        <empty wx:else c-class="empty" description="暂无相关职位，请修改条件试试" />
      </view>
    </scroll-view>
  </view>

  <resume-perfect-tips visible="{{ resumePerfectVisible }}" />

  <fake-guide class="fake-guide" bind:jump="jumpToSearch" visible="{{ showGuide }}" moreSearchParams="{{ moreSearchParams }}"></fake-guide>

  <view class="footer-fixed">
    <t-button hover-class="none" class="btn share" open-type="share"> 分享 </t-button>
    <view class="btn like {{ isCollect ? 'like-primary' : '' }}" bind:tap="collectAnnouncement"> {{ isCollect ? '已收藏' : '收藏' }}</view>
    <t-button hover-class="none" class="deliver" bind:tap="handleDeliver" data-id="{{ announcementId }}" theme="primary">投递简历</t-button>
  </view>

  <!-- 公告详情 -->
  <t-popup visible="{{ announcementVisible }}" placement="bottom" close-btn="{{ false }}">
    <view slot="close-btn">
      <t-icon t-class="close-btn" name="close" size="32" bind:tap="onAnnouncementClose" />
    </view>
    <view class="announcement-info">
      <view class="header">公告信息</view>
      <view class="content">
        <view class="row amount">
          <view class="label"
            >共招<i class="number">{{ announcementInfo.announcementRecruitAmount }}</i
            >人，<i class="number">{{ announcementInfo.allJobNumber }}</i
            >个职位</view
          >
          <view class="info">截止时间：{{ announcementInfo.periodDate }}</view>
        </view>
        <view class="row">
          <view class="label">学历要求</view>
          <view class="info">{{ announcementInfo.educationName }}</view>
        </view>
        <view class="row">
          <view class="label">报名方式</view>
          <view class="info">{{ announcementInfo.applyTypeText }}</view>
        </view>
        <view class="row">
          <view class="label">工作地点</view>
          <view class="info">{{ announcementInfo.cityName }}</view>
        </view>
        <view wx:if="{{ announcementInfo.majorName }}" class="row">
          <view class="label">需求专业(供参考)</view>
          <view class="info">{{ announcementInfo.majorName }}</view>
        </view>
      </view>
    </view>
  </t-popup>

  <!-- 下面这三个弹出选择器，如果放在filter-box里面,当吸顶的时候遮盖层会有问题，所以挪到下面来 -->
  <picker-base data-type="jobCategory" visible="{{ jobCategoryVisible }}" value="{{ jobCategoryId }}" keys="{{ keys }}" title="职位类型" options="{{ jobTypeList }}" bindchange="handlePickerChange" />
  <picker-base data-type="education" visible="{{ educationVisible }}" value="{{ educationId }}" keys="{{ keys }}" title="学历" options="{{ educationList }}" bindchange="handlePickerChange" />
  <picker-base data-type="major" visible="{{ majorVisible }}" value="{{ majorId }}" keys="{{ keys }}" title="专业" options="{{ majorList }}" bindchange="handlePickerChange" />

  <job-list-dialog visible="{{ deliverJobListVisible }}" apply-job-list="{{ applyJobList }}" />
  <t-dialog visible="{{ beforeApplyDialogVisible }}" title="{{ beforeApplyDialogTitle }}" content="{{ beforeApplyDialogContent }}" confirm-btn="{{ beforeApplyDialogConfirmBtn }}" cancel-btn="{{ beforeApplyDialogCancelBtn }}" bind:confirm="handleCheckDialogConfirm" bind:cancel="handleCheckDialogCancel" />
  <login-dialog visible="{{ loginDialogVisible }}" bind:loginSuccess="initDetail" />
</view>
