import { throttle } from 'throttle-debounce'
import {
  getDetail,
  getJobList,
  collect as collectAnnouncement,
  checkAnnouncementReport,
  createAnnouncementReport
} from '@/api/announcementDetail'
import beforeDeliveryMixin from '@/mixins/beforeDeliveryMixin'
import { collect as collectComapny } from '@/api/company'
import { checkLogin, getUserInfo } from '@/utils/store'
import { showLoading, showToast, getElClientRect, toWebPage } from '@/utils/util'
import { defaultDuration, h5, assetsURL } from '@/settings'
import { getResumeStepNum } from '@/api/person'
import { sceneStringToObj } from '@/utils/url'
import { htmlLinkHandlerMixin } from '@/utils/html-link-handler'

let _this = <any>null
Page({
  /**
   * 页面的初始数据
   */

  behaviors: [beforeDeliveryMixin],

  data: {
    detailLabel: '',
    showMatchData: false,
    matchText: '火爆',
    matchValue: '1000人',

    reportUrl: '',

    reportDialogVisible: false,
    reportDialogTitle: '',
    reportDialogContent: '',
    reportDialogDescription: '',
    reportConfirmButton: <any>null,
    reportCancelButton: <any>null,
    reportConfirmCallback: () => {},

    //公告ID
    announcementId: '',
    loginDialogVisible: false,
    //公告信息
    announcementInfo: <any>{},
    onlineJobNumber: 0,
    // 底部搜索更多参数
    moreSearchParams: <any>{},
    moreSearchImage: `${assetsURL}/icon/common/more-search.png`,
    // scroll-view高度
    remainHeight: '100%',
    more: '展开内容',
    showMore: false,
    isShow: true,
    upHeight: 0,
    lineHeight: 0,
    maxLine: 20,
    detailHeight: 0,
    originDetailHeight: 0,

    //职位列表
    jobList: [],
    userInfo: {},
    isLogin: false,
    isCollect: false,
    headerStyle: getApp().globalData.headerStyle,
    tabType: 1,
    page: 1,
    pageBool: false,
    announcementVisible: false,
    // 吸顶元素距离swiper高度
    offsetTop: 0,
    // 是否吸顶
    isSticky: false,
    // 距离顶部多少吸顶
    fixedTop: getApp().globalData.headerOffsetHeight,
    // 安全提示距离顶部多少
    tipsTop: 0,
    // 获取tab栏的高度
    tabsHeigth: 0,
    guideHeigth: 0,
    announcementRecommend: {
      height: `--swiper-height:${206 * 3}rpx`,
      current: 0,
      list: []
    },
    keys: {
      label: 'v',
      value: 'k'
    },
    deliverVisible: false,
    resumePerfectVisible: false,
    areaList: [],
    jobTypeList: [],
    majorList: [],
    educationList: [],
    areaVisible: false,
    jobCategoryVisible: false,
    majorVisible: false,
    educationVisible: false,
    cityId: [],
    jobCategoryId: [],
    majorId: [],
    educationId: [],
    cityText: '地区',
    jobCategoryText: '职位类型',
    majorText: '专业',
    educationText: '学历',
    showGuide: false,
    hasPrevPage: false,
    copyLink: false
  },

  handlePick(event: WechatMiniprogram.CustomEvent) {
    const { isLogin } = this.data
    const { type } = event.currentTarget.dataset
    if (isLogin) {
      this.setData({ [`${type}Visible`]: true })
    } else {
      this.setData({ loginDialogVisible: true })
    }
  },

  handlePickerChange(event: WechatMiniprogram.CustomEvent) {
    const { type } = event.currentTarget.dataset

    const { label, value } = event.detail

    this.setData({ [`${type}Id`]: value, page: 1, pageBool: false })
    this.getJobListApi(true)
  },

  handleReport() {
    if (!this.checkLogin()) return

    this.checkJobReport()
  },

  handleReportConfirm() {
    this.data.reportConfirmCallback()
    this.handleReportCancel()
  },

  handleReportCancel() {
    this.setData({ reportDialogVisible: false })
  },

  async checkJobReport(params: any = {}) {
    const { announcementId } = this.data
    const { jumpType, jumpUrl, isConfirm, title, confirmBtnTxt, cancelBtnTxt, tips1, tips2 } =
      await checkAnnouncementReport({
        announcementId,
        ...params
      })

    const reportConfirmButton = confirmBtnTxt ? { content: confirmBtnTxt, variant: 'base' } : null
    const reportCancelButton = cancelBtnTxt ? { content: cancelBtnTxt, variant: 'base' } : null

    const reportDialogData = {
      reportDialogVisible: true,
      reportDialogTitle: title,
      reportDialogContent: tips1,
      reportDialogDescription: tips2,
      reportConfirmButton,
      reportCancelButton
    }

    // 跳转类型，1:有弹窗带跳转链接,2:无弹窗带跳转链接,-1:有弹窗无跳转链接,9:可能是简历前三步未完善（🤷）

    if (jumpType === 1) {
      const callback =
        params.isConfirm || isConfirm === 0
          ? async () => {
              const { jumpUrl } = await createAnnouncementReport(announcementId)

              toWebPage(`${h5}${jumpUrl}`)
            }
          : () => {
              this.checkJobReport({ isConfirm: '1' })
            }

      this.setData({
        ...reportDialogData,
        reportConfirmCallback: callback
      })
      return
    }

    if (jumpType === 2) {
      toWebPage(`${h5}${jumpUrl}`)
      return
    }

    if (jumpType === 9) {
      this.setData({
        ...reportDialogData,
        reportConfirmCallback: () => wx.navigateTo({ url: '/packages/resume/required' })
      })
      return
    }

    if (jumpType === -1) {
      this.setData({
        ...reportDialogData,
        reportConfirmCallback: () => {}
      })
      return
    }
  },

  async getDetailApi() {
    showLoading()
    const announcementDetailInfo = await getDetail({
      id: this.data.announcementId
    })
    const { matchText, matchValue } = this.data
    const {
      establishmentType,
      moreSearchParams: { typeName, typeId, city, cityId }
    } = announcementDetailInfo.info
    const { hasRecord, heatName, announcementClickTxt, url } = announcementDetailInfo.heatInfo
    const {
      jobCategoryFilter = [],
      jobEducationTypeFilter = [],
      jobMajorFilter = []
    } = announcementDetailInfo?.jobFilter || {}

    const matchData = {
      showMatchData: hasRecord,
      matchText: hasRecord ? heatName : matchText,
      matchValue: hasRecord ? announcementClickTxt : matchValue,

      reportUrl: url,

      detailLabel: establishmentType === '1' ? '全部有编' : establishmentType === '2' ? '部分有编' : ''
    }

    let obj = {
      noticeText: typeName,
      noticeId: typeId,
      cityText: city,
      cityId: cityId
    }

    this.setData(
      {
        ...matchData,
        moreSearchParams: obj,
        announcementInfo: announcementDetailInfo.info,
        onlineJobNumber: announcementDetailInfo.info.onlineJobNumber,
        isCollect: announcementDetailInfo.info.isCollectAnnouncement === 1 ? true : false,
        'announcementRecommend.list': announcementDetailInfo.recommendList,
        jobList: announcementDetailInfo.jobList.data,
        jobTypeList: jobCategoryFilter,
        majorList: jobMajorFilter,
        educationList: jobEducationTypeFilter
      },
      () => {
        this.getOffsetTop()
        this.getDetailHeight()
        this.getTabsHeigth()
        // this.getTipsTop()
        this.checkElementPosition()

        wx.hideLoading()
      }
    )
    if (announcementDetailInfo.jobList.data.length < 20) {
      this.setData({
        pageBool: true
      })
    }
  },

  async getJobListApi(hasFilter = false) {
    const { announcementId, page, educationId, majorId, jobCategoryId } = this.data
    showLoading()
    getJobList({
      id: announcementId,
      page: page,
      categoryId: jobCategoryId.length ? jobCategoryId.join() : '',
      majorId: majorId.length ? majorId.join() : '',
      educationId: educationId.length ? educationId.join() : ''
    })
      .then((result: any) => {
        if (result.jobList.data.length < 20) {
          this.setData({
            pageBool: true
          })
        }
        let newJobList = []
        hasFilter ? (newJobList = result.jobList.data) : (newJobList = this.data.jobList.concat(result.jobList.data))
        this.setData({
          jobList: newJobList,
          onlineJobNumber: result.jobList.onlineCount
        })
        wx.hideLoading()
      })
      .catch(() => {
        wx.hideLoading()
      })
  },

  reset() {
    this.setData({
      pageBool: false,
      page: 1,
      majorId: [],
      majorText: '专业',
      jobCategoryId: [],
      jobCategoryText: '职位类型',
      educationId: [],
      educationText: '学历'
    })
    this.getJobListApi(true)
  },

  jumpToSearch() {
    let { cityId, noticeId, cityText, noticeText } = this.data.moreSearchParams
    const maxLength = 5
    const cityIdArray = cityId.split(',')
    if (cityIdArray.length > maxLength) {
      cityId = cityIdArray.slice(0, maxLength).join(',')
    }
    wx.reLaunch({ url: `/pages/announcement/index?areaId=${cityId}&searchType=2&announcementType=${noticeId}` })
  },

  async calculateGuideHeight() {
    const systemInfo = wx.getWindowInfo()
    const screenWidth = systemInfo.screenWidth
    const designWidth = 750
    const designHeight = 158

    // 根据屏幕宽度动态计算 guideHeigth 的值
    const guideHeigth = (screenWidth / designWidth) * designHeight

    this.setData({ guideHeigth })
  },

  hasPreviousPage() {
    const pages = getCurrentPages() // 获取当前页面栈
    if (pages.length > 1) {
      return true
    } else {
      return false
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    _this = this

    if (options.scene) {
      const sceneObj = sceneStringToObj(options.scene)

      const { id } = sceneObj
      this.setData({ announcementId: id })
    } else {
      this.setData({
        announcementId: options.id
      })
    }

    setTimeout(() => {
      this.getRemainHeight()
    }, 400)

    this.setData({
      hasPrevPage: this.hasPreviousPage()
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // this.getGuideHeigth()
    this.calculateGuideHeight()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.initDetail()
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  jumpToComany() {
    wx.navigateTo({
      url: `/packages/company/detail/index?id=${this.data.announcementInfo.companyId}`
    })
  },

  // 关注单位
  collectCompany() {
    if (this.checkLogin()) {
      collectComapny({ companyId: this.data.announcementInfo.companyId }).then(() => {
        if (this.data.announcementInfo.isCollectCompany == 1) {
          showToast({ title: '已取消关注', duration: defaultDuration })
        } else {
          showToast({ title: '关注成功', duration: defaultDuration })
        }
        this.setData({
          'announcementInfo.isCollectCompany': 3 - this.data.announcementInfo.isCollectCompany
        })
        // 发请求
      })
    }
  },

  // 收藏
  async collectAnnouncement() {
    if (this.checkLogin()) {
      await collectAnnouncement(this.data.announcementId)
      const { isCollect } = this.data

      if (isCollect) {
        showToast({ title: '已取消收藏', duration: defaultDuration })
      } else {
        showToast({ title: '收藏成功', duration: defaultDuration })
      }
      this.setData({ isCollect: !isCollect })
    }
  },

  initDetail() {
    const isLogin = checkLogin()
    this.setData({
      page: 1,
      pageBool: false,
      isLogin
    })
    const user = getUserInfo()
    this.setData({
      userInfo: user
    })
    if (isLogin) {
      // 设置一些信息
      getResumeStepNum().then((res: any) => {
        const resumePercent = res.resumeCompletePercent
        if (resumePercent < 75) {
          this.setData({
            resumePerfectVisible: true
          })
        } else {
          this.setData({
            resumePerfectVisible: false
          })
        }
      })
    }

    this.getDetailApi()
  },

  async getDetailHeight() {
    const { upHeight } = this.data
    if (upHeight > 0) return
    const query = wx.createSelectorQuery().select('.announcement-detail')
    query
      .fields({ computedStyle: ['line-height'] }, (res) => {
        this.setData({ lineHeight: parseFloat(res['line-height']) })
      })
      .exec()

    query
      .boundingClientRect((rect: any) => {
        const height = rect.height
        // 超过20行显示展开按钮，设置高度隐藏多余内容
        const { lineHeight, maxLine } = this.data
        const showHeight = Math.floor(lineHeight * maxLine)
        this.setData({
          showMore: height > lineHeight * maxLine,
          isShow: !(height > lineHeight * maxLine),
          upHeight: showHeight
          // detailHeight: height
        })
      })
      .exec()
    // const rect: any = await getElClientRect('.rich-text-wrap')
    // this.setData({
    //   originDetailHeight: rect.height
    // })
  },

  showText() {
    const { isShow } = this.data

    this.setData({
      isShow: !isShow
    })
  },

  onBack() {
    wx.navigateBack()
  },

  onGoHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },
  //触底事件
  scrolltolowerEvent() {
    if (this.data.pageBool || this.data.tabType == 1) {
      return
    }
    this.setData({
      page: this.data.page + 1
    })
    this.getJobListApi()
  },

  switchType(e: any) {
    const {
      dataset: { type }
    } = e.currentTarget
    this.setData(
      {
        tabType: type
      },
      () => {
        this.checkElementPosition()
      }
    )
  },

  closeRemindDialog() {
    this.setData({
      showConfirm: false
    })
  },

  showDetail() {
    this.setData({
      announcementVisible: true
    })
  },

  onAnnouncementClose() {
    this.setData({
      announcementVisible: false
    })
  },

  announcementRecommendChange(e: any) {
    const {
      detail: { current }
    } = e
    const { list } = this.data.announcementRecommend
    const { length } = list[current]
    const height = `--swiper-height:${206 * length}rpx`
    this.setData({
      'announcementRecommend.height': height,
      'announcementRecommend.current': current
    })
  },

  async handleDeliver(event: any) {
    if (!this.checkLogin()) return

    const announcementId = event.currentTarget.dataset.id

    this.handleCheckAnnouncementApply(announcementId)
  },

  handleComfirm() {
    wx.navigateTo({ url: '/packages/resume/index' })
  },

  async getRemainHeight() {
    const rect: any = await getElClientRect('.flex-grow')
    this.setData({ remainHeight: rect.height + 'px' })
  },

  async getOffsetTop() {
    const { offsetTop } = this.data
    if (offsetTop > 0) return
    const announcementFilterEl: any = await getElClientRect('.tabs-content')
    const { top } = announcementFilterEl
    this.setData({ offsetTop: top })
  },

  async getTipsTop() {
    const El: any = await getElClientRect('.tips-ts')
    const { top } = El
    this.setData({ tipsTop: top })
  },

  async getTabsHeigth() {
    const El: any = await getElClientRect('.tabs-content')
    const { height } = El
    this.setData({ tabsHeigth: height })
  },

  async getGuideHeigth() {
    const El1: any = await getElClientRect('.fake-guide')
    const El2: any = await getElClientRect('.footer-fixed')
    // 特殊加4px，界面协调一点
    this.setData({ guideHeigth: El1.height - El2.height + 4 })
  },

  checkLogin() {
    const isLogin = this.data.isLogin

    if (!isLogin) {
      this.setData({
        loginDialogVisible: true
      })
    }
    return isLogin
  },

  copyDownloadUrl(e: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { url }
      }
    } = e
    wx.setClipboardData({
      data: url,
      success: () => {
        showToast({ title: '链接已复制，请在浏览器中打开查看', icon: 'none', duration: defaultDuration })
      },
      fail: () => {
        showToast(`复制失败`, 'error')
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  onScroll(e: any) {
    const {
      currentTarget: { offsetTop },
      detail: { scrollTop }
    } = e
    this.setFixed({ offsetTop, scrollTop })
    // this.setGuide({ scrollTop })
    this.checkElementPosition()
  },

  setFixed: throttle(5, ({ offsetTop, scrollTop }) => {
    const { offsetTop: top, isSticky } = _this.data
    const flag = offsetTop + scrollTop >= top

    if (flag !== isSticky) {
      _this.setData({ isSticky: flag })
    }
  }),

  checkElementPosition() {
    const { hasPrevPage, showGuide, tabType } = this.data
    if (hasPrevPage) return

    const query = wx.createSelectorQuery()
    query.select('.announcement-recommend').boundingClientRect()
    query.select('.tips-ts').boundingClientRect()
    query.exec((res) => {
      const windowHeight = wx.getWindowInfo().windowHeight
      const recommendRect = res[0]
      const tipsRect = res[1]

      if (recommendRect && recommendRect.top < windowHeight && tabType === 1) {
        if (!showGuide) {
          this.setData({ showGuide: true })
        }
      } else if (tipsRect && tipsRect.top < windowHeight && tabType === 1) {
        if (!showGuide) {
          this.setData({ showGuide: true })
        }
      } else {
        if (showGuide) {
          this.setData({ showGuide: false })
        }
      }
    })
  },

  setGuide: throttle(5, ({ scrollTop }) => {
    // console.log(scrollTop, _this.data.tipsTop);
    const { isShow, tipsTop, remainHeight, hasPrevPage, tabType, originDetailHeight, detailHeight } = _this.data
    // console.log('hasPrevPage',hasPrevPage);
    if (hasPrevPage) return
    const topCount = isShow
      ? tipsTop - remainHeight.replace('px', '')
      : tipsTop - remainHeight.replace('px', '') - originDetailHeight
    // console.log('topCount',topCount);

    if (scrollTop > topCount && tabType === 1) {
      _this.setData({ showGuide: true })
    } else {
      _this.setData({ showGuide: false })
    }
  }),

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.announcementInfo.announcementTitle
    }
  },
  onShareTimeline() {
    return {
      title: this.data.announcementInfo.announcementTitle
    }
  },

  // 使用富文本链接处理mixin
  ...htmlLinkHandlerMixin
})
