<view class="order-confirm">
  <header title="确认订单" bindback="handleBack" />

  <view class="main">
    <view class="information">
      <view class="title">订单信息</view>

      <view class="item">
        <text class="label">产品名称</text>
        <text class="value">{{ name }}</text>
      </view>

      <view class="item">
        <text class="label">产品售价</text>
        <text class="value">{{ price }}</text>
      </view>

      <view class="item">
        <text class="label">优惠金额</text>
        <text class="value">{{ discount }}</text>
      </view>

      <view class="total">
        实付金额：<text class="value">{{ actuallyPay }}</text>
      </view>
    </view>

    <view class="pay-type">
      <text class="label">支付方式</text>
      <text class="value">微信支付</text>
    </view>
  </view>

  <view class="footer">
    <t-button theme="primary" size="large" block bindtap="handlePay">
      <block wx:if="{{ paymentValue }}">
        去支付 ¥<text class="value">{{ paymentValue }}</text>
      </block>
    </t-button>
  </view>

  <t-popup class="detainment-dialog" visible="{{ detainmentVisible }}" bind:visible-change="onVisibleChange" placement="center">
    <view class="detainment-content">
      <view class="detainment-title">确定要放弃支付吗？</view>
      <view class="detainment-desc"
        >你可能错过 <view class="detainment-type">{{ detainment.missTitle }}</view
        >的机会
      </view>
      <view class="detainment-info">
        <view class="detainment-list" wx:for="{{ detainment.listContent }}" wx:for-item="item" wx:key="index">
          <view class="detainment-name">{{ item.title }}</view>
          <view class="detainment-introduce">{{ item.subTitle }}</view>
        </view>
      </view>
      <view class="detainment-footer">
        <t-button t-class="btn cancel" size="large" hover-class="none" bindtap="handleNextTime">下次再说</t-button>
        <t-button t-class="btn" theme="primary" size="large" hover-class="none" bindtap="handleCloseDetainmentPopup"> 继续支付</t-button>
      </view>
    </view>
  </t-popup>
</view>
