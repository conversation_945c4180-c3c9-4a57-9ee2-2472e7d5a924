import request from '@/utils/request'

export function getPayOrderInfo(data: object) {
  return request({ url: '/resume-order/pay', method: 'POST', data })
}

export function getPayOrderResult(id: string) {
  return request({ url: '/resume-order/query', data: { orderId: id } })
}

export function closePackage(equityPackageCategoryId: string | int) {
  return request({ url: '/resume-order/close-package', data: { equityPackageCategoryId } })
}
