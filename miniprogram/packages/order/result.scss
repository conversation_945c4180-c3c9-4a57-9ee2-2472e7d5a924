@use 'styles/variables.scss' as *;

.order-result {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .result {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 88rpx 30rpx 56rpx;

    .title {
      margin-bottom: 28rpx;
      padding-top: 120rpx;
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      background: url(#{$assets}/order/success.png?) no-repeat top center / 80rpx;
    }

    .description {
      text-align: center;
      line-height: 42rpx;
    }

    .button {
      margin-top: 44rpx;
      width: 267rpx;
      height: 70rpx;
      color: $color-primary;
      font-size: 32rpx;
      font-weight: bold;
      text-align: center;
      line-height: 70rpx;
      border-radius: 35rpx;
      box-shadow: 0 0 0 2rpx $color-primary;
    }

    .button-hover {
      background: transparent;
    }

    .guide {
      margin-top: 60rpx;
      padding: 30rpx 30rpx 50rpx;
      color: #523b2b;
      line-height: 42rpx;
      background: #f9f9f9;
      border-radius: $border-radius;
    }

    .name {
      margin-bottom: 25rpx;
      padding-left: 80rpx;
      font-size: 32rpx;
      font-weight: bold;
      background: url(#{$assets}/person/collect/vip.png) no-repeat left center / 70rpx 27rpx;
    }

    .desc {
      margin-bottom: 20rpx;
    }

    .tips {
      margin-bottom: 50rpx;
      color: #9f7a5a;
      font-size: 24rpx;
      line-height: 36rpx;
    }

    .code {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: $font-color;
      font-size: 24rpx;
      line-height: 46rpx;
    }

    .image {
      margin-bottom: 20rpx;
      width: 260rpx;
      height: 260rpx;
      border-radius: $border-radius;
      box-shadow: 0 0 0 2rpx #f4c7a6;
    }
  }

  .pay-success {
    margin-top: 100rpx;

    .pay-success-title {
      color: #9f7a5a;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 40rpx;

      &::before {
        content: '';
        width: 80rpx;
        height: 3rpx;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 160, 0, 0.2) 50%,
          RGBA(255, 191, 83, 1) 100%
        );
        margin-right: 10rpx;
      }

      &::after {
        content: '';
        width: 80rpx;
        height: 3rpx;
        background: linear-gradient(
          to left,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 160, 0, 0.2) 50%,
          RGBA(255, 191, 83, 1) 100%
        );
        margin-left: 10rpx;
      }
    }

    .package-content {
      .package-list {
        width: 570rpx;
        margin: 0 auto 20rpx;
        background: #fff9ee;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        padding: 16rpx 20rpx 16rpx 40rpx;

        .package-icon {
          width: 80rpx;
          height: 80rpx;
          margin-right: 15rpx;
        }

        .package-name {
          font-weight: bold;
        }

        .package-desc {
          color: $font-color-basic;

          i {
            color: $color-primary;
          }
        }
      }
    }
  }
}
