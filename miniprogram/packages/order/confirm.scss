@use 'styles/variables.scss' as *;

.order-confirm {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .main {
    flex: 1;
    padding: 20rpx 30rpx;
    background-color: $color-background;
  }

  .information {
    padding: 20rpx 30rpx;
    background-color: $color-white;
    border-radius: $border-radius;

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      font-size: 36rpx;
      font-weight: bold;
      color: #333333;

      &::before {
        content: '';
        margin-right: 12rpx;
        width: 8rpx;
        height: 34rpx;
        background-color: $color-primary;
        border-radius: 4rpx;
      }
    }

    .item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20rpx 0;
      line-height: 1.5;
    }

    .value {
      max-width: 64%;
      font-weight: bold;
      text-align: right;
    }

    .total {
      padding-top: 30rpx;
      padding-bottom: 10rpx;
      font-weight: bold;
      text-align: right;
      border-top: 2px solid $color-background;

      .value {
        color: $color-point;
      }
    }
  }

  .pay-type {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;
    padding: 0 20rpx;
    height: 85rpx;
    background-color: $color-white;
    border-radius: $border-radius;

    .value {
      padding-left: 52rpx;
      font-weight: bold;
      background: url(#{$assets}/order/wechat-pay.png) no-repeat left center / 40rpx;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    padding: 0 54rpx;
    height: 148rpx;
    background-color: $color-white;

    .value {
      font-size: 40rpx;
      font-weight: bold;
    }
  }

  .detainment-dialog {
    --td-popup-border-radius: 20rpx;
  }
  .detainment-content {
    width: 630rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    overflow: hidden;
    padding: 60rpx;
    background:
      url(#{$assets}/order/detainment-bg.png) no-repeat left top/100% auto,
      #fff;

    $class-prefix: 'detainment';

    .#{$class-prefix}-desc {
      font-size: 36rpx;
      font-weight: bold;
      display: flex;
      margin-top: 12rpx;
      margin-bottom: 50rpx;

      .#{$class-prefix}-type {
        color: $color-primary;
      }
    }

    .#{$class-prefix}-list {
      margin-bottom: 45rpx;

      .#{$class-prefix}-name {
        font-weight: bold;
        margin-bottom: 8rpx;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          width: 10rpx;
          height: 10rpx;
          margin-right: 14rpx;
          flex-shrink: 0;
          background: $color-primary;
          border-radius: 50%;
        }
      }

      .#{$class-prefix}-introduce {
        font-size: 24rpx;
        color: $font-color-label;
        padding-left: 24rpx;
      }
    }

    .#{$class-prefix}-footer {
      display: flex;
      justify-content: space-between;

      .btn {
        width: calc(50% - 10rpx);
        margin: 0;
        font-size: 30rpx;
        font-weight: normal !important;

        &.cancel {
          background-color: #f7f7f7;
          color: $font-color-label;
        }
      }
    }
  }
}
