import { toWebPage } from '@/utils/util'
import { getPayOrderResult } from './api'
import { h5 } from '@/settings'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    title: '',
    content: '',
    useTitle: '',
    useSubtitle: '',
    url: '',
    contentList: []
  },

  handleBack() {
    wx.navigateBack()
  },

  async fetchData(id: string) {
    const { jobResourcesInfo } = await getPayOrderResult(id)
    const { title, content, useTitle, useSubtitle, url, contentList } = jobResourcesInfo

    this.setData({ loading: false, title, content, useTitle, useSubtitle, url, contentList })
  },

  handleNavigate(event: WechatMiniprogram.CustomEvent) {
    const {
      dataset: { url }
    } = event.currentTarget

    toWebPage(`${h5}${url}`)
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(query) {
    const { id } = query

    if (!id) return

    this.fetchData(<string>id)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
