import { closePackage, getPayOrderInfo } from './api'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    name: '',
    price: '',
    discount: '',
    actuallyPay: '',
    paymentValue: '',
    payOption: {},
    orderId: '',
    detainmentVisible: false,

    // 挽留信息
    detainment: {
      isFirstClose: false,
      listContent: [],
      equityPackageCategoryId: 0,
      missTitle: ''
    }
  },

  handleBack() {
    // 检查是否初次今天初次点击
    if (this.data.detainment.isFirstClose) {
      this.setData({
        detainmentVisible: true
      })
      closePackage(this.data.detainment.equityPackageCategoryId)
      return
    }
    wx.navigateBack()
  },

  handlePay() {
    wx.requestPayment(<WechatMiniprogram.RequestPaymentOption>{
      ...this.data.payOption,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      success: (_res) => {
        wx.redirectTo({ url: `/packages/order/result?id=${this.data.orderId}` })
      }
    })
  },

  fetchData(id: string) {
    wx.login({
      success: async (res) => {
        const unit = (val: string) => '¥' + val

        const { code } = res
        const { orderId, price, realAmount, convertPrice, equityPackageName, jsApiParams, detainment } =
          await getPayOrderInfo({
            code,
            equityPackageId: id
          })

        this.setData({
          name: equityPackageName,
          price: unit(realAmount),
          discount: unit(convertPrice),
          actuallyPay: unit(price),
          paymentValue: price,
          payOption: jsApiParams,
          orderId,
          detainment
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(query) {
    const { equityPackageId } = query

    if (!equityPackageId) return

    this.fetchData(equityPackageId)
  },

  handleCloseDetainmentPopup() {
    this.setData({
      detainmentVisible: false
    })
    // firstClose = false
    this.data.detainment.isFirstClose = false
  },

  handleNextTime() {
    this.setData({ detainmentVisible: false }, () => {
      wx.navigateBack()
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
