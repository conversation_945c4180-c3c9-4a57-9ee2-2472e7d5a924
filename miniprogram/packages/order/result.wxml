<view class="order-result">
  <header title="{{ title }}" bindback="handleBack" />

  <view wx:if="{{ loading === false }}" class="result">
    <text class="title">{{ title }}</text>

    <text class="description">{{ content }}</text>

    <navigator class="button" hover-class="button-hover" open-type="navigateBack">返回</navigator>

    <view wx:if="{{ url }}" class="guide">
      <view class="name">使用指引</view>

      <view class="desc">{{ useTitle }}</view>

      <view class="tips">{{ useSubtitle }}</view>

      <view class="code">
        <image class="image" src="{{ url }}" show-menu-by-longpress />

        <text>长按或扫码识别二维码</text>
      </view>
    </view>

    <view class="pay-success" wx:if="{{ contentList.length > 0 }}">
      <view class="pay-success-title">推荐搭配以下服务，获得更多职场机会</view>
      <view class="package-content">
        <view class="package-list" bindtap="handleNavigate" hover-class="none" wx:for="{{ contentList }}"
          wx:for-item="item" data-url="{{ item.url }}" wx:key="key">
          <image class="package-icon" src="{{item.icon}}" mode="aspectFit" />
          <view>
            <view class="package-name">{{item.title}}</view>
            <view class="package-desc">{{item.subTitle}}<i>{{item.linkLabel}}</i></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
