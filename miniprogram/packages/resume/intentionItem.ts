import { setIntentionChanged } from '@/utils/store'
import { deleteIntention, editIntention, addIntention, getIntentionEditInfo } from './api/intention'
import { handleUpdateInputValue } from '@/mixins/inputEvent'
import { validate } from './utils/utils'

// packages/resume/intentionItem.ts
Page({
  /**
   * 页面的初始数据
   */

  data: {
    id: '',
    jobCategoryId: '',
    areaId: [],
    natureType: '',
    wageType: '',
    canDelete: false,
    jobCategoryName: '',
    rules: {
      jobCategoryId: [{ required: true, message: '请选择意向职位' }],
      areaId: [{ required: true, message: '请选择意向城市' }],
      natureType: [{ required: true, message: '请选择工作性质' }],
      wageType: [{ required: true, message: '请选择期望月薪' }]
    },
    areaName: '',
    natureName: '',
    wageName: ''
  },

  handleBack() {
    wx.navigateBack()
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options: any) {
    if (!options.id) return

    const { jobCategoryId, areaId, natureType, wageType, jobCategoryName, areaName, natureName, wageName, canDelete } =
      await getIntentionEditInfo({ id: options.id })
    this.setData({
      jobCategoryId,
      areaId: areaId.split(','),
      natureType,
      wageType,
      jobCategoryName,
      areaName,
      natureName,
      wageName,
      canDelete: canDelete === 1 ? true : false,
      id: options.id
    })
  },

  handleUpdateInputValue(event: WechatMiniprogram.CustomEvent) {
    handleUpdateInputValue(this, event)
  },

  async handleConfirm() {
    const { id, rules } = this.data
    const { jobCategoryId, areaId, natureType, wageType } = this.data
    const data = { jobCategoryId, areaId, natureType, wageType, id }
    const valid = validate(rules, data)
    if (!valid) return

    const api = id ? editIntention : addIntention
    await api(data)
    this.setIntentionChange()
    this.handleBack()
  },

  async handleRemove() {
    await deleteIntention({ id: this.data.id })
    this.setIntentionChange()
    this.handleBack()
  },

  setIntentionChange() {
    setIntentionChanged(true)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
