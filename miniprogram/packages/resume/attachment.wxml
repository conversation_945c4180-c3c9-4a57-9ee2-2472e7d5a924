<wxs src="./utils/utils.wxs" module="utils" />

<view class="attachment-container">
  <view class="attachment-header">
    <header title="附件简历" fixed="{{ false }}" bindback="handleBack" />

    <t-notice-bar visible="{{ true }}" prefixIcon="{{ false }}" content="{{ content }}" />
  </view>

  <view class="attachment-main {{ attachmentList.length ? 'has-data' : '' }}">
    <t-pull-down-refresh value="{{ enable }}" bind:refresh="handleRefresh">
      <view class="attachment-list" wx:if="{{ attachmentList.length }}">
        <view class="item" wx:for="{{ attachmentList }}" wx:key="index" wx:for-item="item">
          <view class="prefix {{ utils.suffixClass(item.fileName) }}"></view>

          <view class="content" bindtap="handleOpen" data-token="{{ item.token }}">
            <view class="name">{{ item.fileName }}</view>
            <view class="date">{{ item.addTime }}上传</view>
          </view>

          <view class="suffix" data-data="{{ item }}" bindtap="handleAction">
            <t-icon name="ellipsis" />
          </view>
        </view>
      </view>

      <view class="empty-container" wx:else>
        <view class="title">上传附件简历流程</view>

        <view class="step">1、将简历发送到微信（文件传输助手、自己或好友）</view>
        <view class="step">2、点击下方“立即上传”按钮，选择发送简历的聊天对象</view>
        <view class="step">3、选择简历文件，点击确认即可上传成功</view>

        <t-empty image="{{ emptyImage }}" description="暂无附件简历" />
      </view>
    </t-pull-down-refresh>
  </view>

  <view class="attachment-footer">
    <t-button theme="primary" size="large" block="{{ true }}" bindtap="handleUpload">立即上传</t-button>
  </view>

  <t-action-sheet id="action" bind:selected="handleSelected" />

  <t-dialog visible="{{ deleteDialogVisible }}" title="提示" content="确定删除该附件简历吗？" confirm-btn="{{ { content: '确定', variant: 'base' } }}" cancel-btn="取消" bind:confirm="handleDeleteConfirm" bind:cancel="handleDialogCancel" />

  <t-dialog visible="{{ renameDialogVisible }}" title="修改附件名称" confirm-btn="{{ { content: '确定', variant: 'base' } }}" cancel-btn="取消" bind:confirm="handleRenameConfirm" bind:cancel="handleDialogCancel">
    <t-input slot="content" placeholder="请输入附件名称，最多20字" clearable maxlength="{{ 20 }}" value="{{ renameValue }}" bindchange="handleRenameChange" bindclear="handleRenameClear" />
  </t-dialog>
</view>
