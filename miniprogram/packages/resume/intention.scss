@use 'styles/variables' as *;

.intention-container {
  background-color: $color-background;
}

.container {
  background-color: $color-white;
  padding: 15rpx 30rpx;

  .title {
    background: url(#{$assets}/resume/header.png) no-repeat top right 10rpx / 137rpx 159rpx;

    .title-text {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 46rpx;
      margin-bottom: 30rpx;
      padding-top: 30rpx;

      &::before {
        content: '';
        display: inline-block;
        margin-right: 10rpx;
        width: 11rpx;
        height: 44rpx;
        background: $color-primary;
        border-radius: 6rpx;
      }
    }

    .title-desc {
      display: inline;
      padding: 9rpx 25rpx;
      border-radius: 23rpx;
      font-size: 26rpx;
      color: $color-primary;
      background: #fdf5ec;
    }
  }

  .intention-content {
    margin-top: 60rpx;

    .intention-item {
      margin: 60rpx 0;
    }

    .add-intention {
      margin-bottom: 60rpx;
    }

    .info {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 32rpx;
      background: url(#{$assets}/icon/into-gray.png) no-repeat center right / 10rpx 18rpx;

      .name {
        max-width: 310rpx;
        @include utils-ellipsis;
      }

      .salary {
        margin-right: 40rpx;
        margin-left: 20rpx;
        font-weight: normal;
        font-size: 28rpx;
      }
    }

    .tags {
      display: flex;
      color: $font-color-basic;
      font-size: 28rpx;
      margin-top: 20rpx;

      .city {
        margin-left: 20rpx;
        max-width: 527rpx;
        @include utils-ellipsis;
      }
    }
  }
}

.intention-other {
  padding: 0 30rpx 120rpx 30rpx;
  background-color: $color-white;
  margin-top: 20rpx;

  .cell-item {
    font-size: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 0;
    border-bottom: 2rpx solid $border-color;
  }
}
