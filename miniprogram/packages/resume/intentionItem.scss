@use 'styles/variables' as *;

.container {
  .content {
    padding: 30rpx;
    background: url(#{$assets}/resume/header.png) no-repeat top 10rpx right 40rpx / 137rpx 159rpx;

    .title {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 46rpx;
      margin-bottom: 30rpx;
      padding-top: 20rpx;

      &::before {
        content: '';
        display: inline-block;
        margin-right: 10rpx;
        width: 11rpx;
        height: 44rpx;
        background: $color-primary;
        border-radius: 6rpx;
      }
    }

    .intention-from {
      margin-top: 30rpx;

      .cell-label {
        margin-top: 40rpx;
      }

      .cell-item {
        border-bottom: 1rpx solid $border-color;
      }

      .cell-value {
        padding: 20rpx 0 40rpx 0;
      }
    }

    .submit {
      margin-top: 60rpx;

      .remove {
        padding: 0 80rpx;
        margin-right: 20rpx;
      }
      .save {
        padding: 0 190rpx;
      }
    }
  }
}
