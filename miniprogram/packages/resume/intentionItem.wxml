<view class="container">
  <header fixed="{{ false }}" bindback="handleBack" />
  <view class="content">
    <view class="title">添加/编辑求职意向</view>

    <view class="intention-from">
      <view class="cell-item">
        <view class="cell-label">意向职位</view>

        <view class="cell-value">
          <picker-job-category model="{{ jobCategoryId }}" label="{{ jobCategoryName }}" title="意向职位" data-key="jobCategoryId" bindchange="handleUpdateInputValue" />
        </view>
      </view>

      <view class="cell-item">
        <view class="cell-label">意向城市</view>

        <view class="cell-value">
          <picker-area model="{{ areaId }}" label="{{ areaName }}" type="intention" title="意向城市" limit="5" data-key="areaId" bindchange="handleUpdateInputValue" />
        </view>
      </view>

      <view class="cell-item">
        <view class="cell-label">工作性质</view>

        <view class="cell-value">
          <picker-nature model="{{ natureType }}" model-text="{{ natureName }}" data-key="natureType" bindchange="handleUpdateInputValue" />
        </view>
      </view>

      <view class="cell-item">
        <view class="cell-label">期望月薪</view>

        <view class="cell-value">
          <picker-wage model="{{ wageType }}" model-text="{{ wageName }}" data-key="wageType" bindchange="handleUpdateInputValue" />
        </view>
      </view>
    </view>

    <view class="submit">
      <t-button theme="light" class="remove" size="large" wx:if="{{ canDelete }}" bindtap="handleRemove">删除</t-button>
      <t-button theme="primary" class="save" size="large" block="{{ !canDelete }}" bindtap="handleConfirm">保存</t-button>
    </view>
  </view>
</view>
