@use 'styles/variables' as *;

.attachment-container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .t-notice-bar {
    --td-font-size-base: 22rpx;
    --td-notice-bar-font-color: #{$color-primary};
    --td-notice-bar-info-bg-color: #{$tag-primary-background};

    padding: 8rpx 30rpx;
  }

  .t-pull-down-refresh {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .attachment-header {
    flex: none;
  }

  .attachment-main {
    flex: 1 0 auto;
    position: relative;

    &.has-data {
      background-color: $color-background;
    }
  }

  .attachment-list {
    padding: 30rpx;
    box-sizing: border-box;

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      padding: 30rpx;
      background-color: $color-white;
      border-radius: $border-radius;
    }

    .prefix {
      flex: none;
      width: 78rpx;
      height: 78rpx;

      $list: (doc, word), (docx, word), (pdf, pdf);
      @each $label, $value in $list {
        &.#{$label} {
          background: url(#{$assets}/resume/attachment/#{$value}.png) no-repeat center / contain;
        }
      }
    }

    .content {
      flex: 1 0 auto;
      margin: 0 20rpx;
      max-width: 72%;
      line-height: 1;
    }

    .name {
      @include utils-ellipsis;

      margin-bottom: 20rpx;
      font-size: 32rpx;
      font-weight: bold;
    }

    .date {
      color: $font-color-label;
      font-size: 24rpx;
    }

    .suffix {
      flex: none;
      color: #9a9a9a;
      font-size: 48rpx;
    }
  }

  .empty-container {
    padding: 60rpx 30rpx;
    color: $font-color-basic;
    font-size: 28rpx;
    line-height: 2;
    box-sizing: border-box;

    .title {
      margin-bottom: 45rpx;
      font-size: 48rpx;
      font-weight: bold;
      line-height: 1;
    }

    .t-empty {
      margin-top: 128rpx;
    }

    .t-image {
      width: 400rpx;
      height: 290rpx;
      object-fit: contain;
    }

    .t-empty__description {
      --td-empty-description-color: #{$font-color-basic};
    }
  }

  .attachment-footer {
    flex: none;
    padding: 30rpx 54rpx;
    box-shadow: 0 -1px 0 0 $border-color;
  }
}
