<view class="intention-container">
  <header title="求职意向" bindback="handleBack" />
  <view class="container">
    <view class="title">
      <view class="title-text">管理求职意向</view>
      <view class="title-desc">将根据求职意向为您匹配推荐职位，最多添加5个</view>
    </view>

    <view class="intention-content">
      <view class="intention-item" wx:for="{{ intentionList }}" wx:key="index" data-id="{{ item.id }}" bindtap="handleNavigate">
        <view class="info">
          <view class="name">{{ item.jobCategoryName }}</view>
          <view class="salary">{{ item.wageName }}</view>
        </view>
        <view class="tags">
          <view class="tag">{{ item.natureName }}</view>
          <view class="city">{{ item.areaName }}</view>
        </view>
      </view>

      <t-button class="add-intention" theme="primary" icon="add-circle" wx:if="{{ canAdd }}" size="large" block bindtap="handleNavigate">添加求职意向 </t-button>
    </view>
  </view>

  <view class="intention-other">
    <view class="cell-item">
      <view class="cell-label">求职状态</view>

      <view class="cell-value">
        <picker-status model="{{ postStatusList.workStatus }}" model-text="{{ postStatusList.workStatusText }}" data-key="workStatus" bindchange="handleChange" />
      </view>
    </view>

    <view class="cell-item">
      <view class="cell-label">到岗时间</view>

      <view class="cell-value">
        <picker-arrive model="{{ postStatusList.arriveDateType }}" model-text="{{ postStatusList.arriveDateTypeText }}" data-key="arriveDateType" bindchange="handleChange" />
      </view>
    </view>
  </view>
</view>
