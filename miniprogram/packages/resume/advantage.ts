import { defaultDuration } from '@/settings'
import { getAdvantageData, setAdvantageData } from './api/index'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    value: ''
  },

  async handleChange(event: WechatMiniprogram.CustomEvent) {
    const { value } = event.detail

    if (value.length === 0 || value.length >= 50) {
      await setAdvantageData({ advantage: value })
      wx.navigateBack()
    } else {
      wx.showToast({ title: '请输入至少50字的个人优势介绍', icon: 'none', duration: defaultDuration })
    }
  },

  async fetchData() {
    const { advantage } = await getAdvantageData()

    this.setData({ value: advantage })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
