<wxs src="./utils/utils.wxs" module="utils" />

<view class="resume-container">
  <t-navbar title="在线简历{{ resumeData.complete ? '(' + resumeData.complete + '%)' : '' }}">
    <view slot="capsule" class="custom-capsule">
      <t-icon size="20" bind:tap="onBack" aria-role="button" aria-label="返回" name="chevron-left" class="custom-capsule__icon" />
      <view class="home-icon custom-capsule__icon" bind:tap="onGoHome"></view>
    </view>
  </t-navbar>

  <block>
    <view wx:if="{{ hintText }}" class="resume-hint {{ hintStatus ? 'is-success' : '' }}">{{ hintText }}</view>

    <view wx:if="{{ optimizationList.length || identityTip.show }}" class="optimization">
      <view wx:if="{{ optimizationList.length }}" class="optimization-content">
        <view class="title">
          你有
          <text class="num">{{ optimizationList.length }}</text>
          个简历优化项~
        </view>

        <view class="content">
          <view class="list">
            <view class="item" wx:for="{{ optimizationList }}" wx:key="index">
              <view class="item-title">{{ item.title }}</view>

              <view class="item-desc">{{ item.content }}</view>

              <view class="item-action">
                <text class="button" data-url="{{ item.url }}" bindtap="handleOptimization">{{ item.btnText }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view wx:if="{{ identityTip.show }}" class="perfect-tips">
        {{ identityTip.content }}
        <view class="perfect-btn" data-url="{{ identityTip.h5Url }}" bindtap="handleBasic">{{ identityTip.btnText }}</view>
      </view>
    </view>

    <view class="resume-main">
      <view class="basic">
        <view class="avatar" bindtap="handleAvatar">
          <image class="image" mode="aspectFill" src="{{ resumeData.avatar }}" />
          <text class="text">更换头像</text>
        </view>

        <view class="data" bindtap="handleBasic">
          <view class="name">
            <text class="text">{{ resumeData.name }}</text>
          </view>

          <view class="info">{{ resumeData.age }}岁 | {{ resumeData.topEducation }} | {{ resumeData.topEducationMajor }}</view>

          <view class="contact">
            <text class="text mobile">{{ resumeData.fullMobile }}</text>
            <text class="text email">{{ resumeData.email }}</text>
          </view>
        </view>
      </view>

      <view class="column">
        <block wx:if="{{ resumeData.advantage }}">
          <view class="title">个人优势<view class="action" bindtap="handleAdvantage"></view></view>

          <view class="content" bindtap="handleAdvantage">{{ resumeData.advantage }}</view>
        </block>

        <block wx:else>
          <view class="title">个人优势<view class="action" bindtap="handleAdvantage">脱颖而出的关键</view></view>
        </block>
      </view>

      <view class="column has-list">
        <view class="title">求职意向<view class="action is-add" bindtap="handleIntention"></view></view>

        <view class="status">
          <text class="text">{{ resumeData.intentionInfo }}</text>
        </view>

        <view class="list">
          <view class="item" wx:for="{{ resumeData.intentionList }}" wx:key="index" data-data="{{ item }}" bindtap="handleIntentionItem">
            <view class="cell">
              <text class="label">{{ item.jobCategoryName }}</text>
              <text class="value color-primary font-size-32 font-weight-bold">{{ item.wageName }}</text>
            </view>

            <view class="info">
              {{ item.natureName }}<text class="text">{{ item.areaName }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="column has-list">
        <view class="title">教育经历<view class="action is-add" bindtap="handleEducation"></view></view>

        <view class="list">
          <view class="item" wx:for="{{ resumeData.educationList }}" wx:key="index" data-data="{{ item }}" bindtap="handleEducation">
            <view class="cell">
              <text class="label">{{ item.school }}</text>
              <text class="value">{{ item.beginDate }}-{{ item.endDate }}</text>
            </view>

            <view class="tags" wx:if="{{ utils.isString1(item.isRecruitment) || utils.isString1(item.isProjectSchool) || utils.isString1(item.isAbroad) }}">
              <text class="text" wx:if="{{ utils.isString1(item.isRecruitment) }}">统招</text>
              <text class="text" wx:if="{{ utils.isString1(item.isProjectSchool) }}">985/211</text>
              <text class="text is-special" wx:if="{{ utils.isString1(item.isAbroad) }}">海外</text>
            </view>

            <view class="info">{{ utils.formatString(item.educationName, item.majorName, item.mentor ? '导师：' + item.mentor : '') }}</view>
          </view>
        </view>
      </view>

      <view class="column">
        <block wx:if="{{ resumeData.researchDirection }}">
          <view class="title">研究方向<view class="action" bindtap="handleResearch"></view></view>

          <view class="content" bindtap="handleResearch">{{ resumeData.researchDirection }}</view>
        </block>

        <block wx:else>
          <view class="title">研究方向<view class="action" bindtap="handleResearch">展示你的研究方向</view></view>
        </block>
      </view>

      <view class="column {{ resumeData.projectList.length ? 'has-list' : '' }}">
        <view class="title">项目经历<view class="action is-add" bindtap="handleProject"></view></view>

        <block wx:if="{{ resumeData.projectList.length }}">
          <view class="list">
            <view class="item" wx:for="{{ resumeData.projectList }}" wx:key="index" data-data="{{ item }}" bindtap="handleProject">
              <view class="cell">
                <text class="label">{{ item.name }}</text>
                <text class="value">{{ item.beginDate }}-{{ item.endDate }}</text>
              </view>

              <view class="info">{{ utils.formatString(item.company, item.role) }}</view>

              <view class="content" wx:if="{{ item.description.length }}">项目描述：{{ item.description }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="tips">用人部门筛选学术科研类候选人的重要依据</view>
        </block>
      </view>

      <view class="column">
        <view class="title">工作/实习/研究经历<view class="action is-add" bindtap="handleWork"></view></view>

        <block wx:if="{{ resumeData.workList.length }}">
          <view class="list">
            <view class="item" wx:for="{{ resumeData.workList }}" wx:key="index" data-data="{{ item }}" bindtap="handleWork">
              <view class="cell">
                <text class="label">{{ item.company }}</text>
                <text class="value">{{ item.beginDate }}-{{ item.endDate }}</text>
              </view>

              <view class="tags" wx:if="{{ utils.isString1(item.isPostdoc) || utils.isString1(item.isAbroad) || utils.isString1(item.isPractice) }}">
                <text class="text" wx:if="{{ utils.isString1(item.isPostdoc) }}">博士后经历</text>
                <text class="text is-special" wx:if="{{ utils.isString1(item.isAbroad) }}">海外经历</text>
                <text class="text is-practice" wx:if="{{ utils.isString1(item.isPractice) }}">实习</text>
              </view>

              <view class="info">{{ utils.formatString(item.jobName, item.department) }}</view>

              <view class="content">工作内容：{{ item.content }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="tips">展示你的工作实力</view>
        </block>
      </view>

      <view class="column">
        <view class="title">学术成果</view>

        <block wx:if="{{ resumeData.paperList.length === 0 && resumeData.patentList.length === 0 && resumeData.bookList.length === 0 }}">
          <view class="tips">用人部门筛选学术科研类候选人的重要依据</view>
        </block>

        <block wx:if="{{ resumeData.paperList.length }}">
          <view class="subtitle">学术论文<view class="action is-add" bindtap="handlePaper"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.paperList }}" wx:key="index" data-data="{{ item }}" bindtap="handlePaper">
              <view class="cell">
                <text class="label max-width-75 font-size-30">{{ item.title }}</text>
                <text class="value">{{ item.publishDate }}</text>
              </view>

              <view class="info">{{ utils.formatArrayString(['刊物名/卷(期)号:', item.serialNumber], ['收录情况:', item.recordSituation], ['本人位次：', item.positionText], ['影响因子：', item.impactFactor]) }}</view>

              <view class="content" wx:if="{{ item.description.length }}">论文描述：{{ item.description }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handlePaper"><text class="text">学术论文</text></view>
        </block>

        <block wx:if="{{ resumeData.patentList.length }}">
          <view class="subtitle">学术专利<view class="action is-add" bindtap="handlePatent"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.patentList }}" wx:key="index" data-data="{{ item }}" bindtap="handlePatent">
              <view class="cell">
                <text class="label max-width-75 font-size-30">{{ item.name }}</text>
                <text class="value">{{ item.authorizationDate }}</text>
              </view>

              <view class="info">{{ utils.formatArrayString(['本人位次：', item.positionText], ['专利编号：', item.number], ['完成状态：', item.finishStatus]) }}</view>

              <view class="content" wx:if="{{ item.description.length }}">专利描述：{{ item.description }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handlePatent"><text class="text">学术专利</text></view>
        </block>

        <block wx:if="{{ resumeData.bookList.length }}">
          <view class="subtitle">学术专著<view class="action is-add" bindtap="handleBook"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.bookList }}" wx:key="index" data-data="{{ item }}" bindtap="handleBook">
              <view class="cell">
                <text class="label max-width-75 font-size-30">{{ item.name }}</text>
                <text class="value">{{ item.publishDate }}</text>
              </view>

              <view class="info">{{ utils.formatArrayString(['字数：', item.words, '万字'], ['发行数量：', item.publishAmount]) }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handleBook"><text class="text">学术专著</text></view>
        </block>
      </view>

      <view class="column">
        <view class="title">奖励荣誉</view>

        <block wx:if="{{ resumeData.rewardList.length === 0 && resumeData.otherRewardList.length === 0 }}">
          <view class="tips">优秀履历的背书</view>
        </block>

        <block wx:if="{{ resumeData.rewardList.length }}">
          <view class="subtitle">学术奖励<view class="action is-add" bindtap="handleReward"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.rewardList }}" wx:key="index" data-data="{{ item }}" bindtap="handleReward">
              <view class="cell">
                <text class="label max-width-75 font-size-30">{{ item.name }}</text>
                <text class="value">{{ item.obtainDate }}</text>
              </view>

              <view class="info">{{ utils.formatArrayString(['获奖级别：', item.level], ['获奖角色：', item.role]) }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handleReward"><text class="text">学术奖励</text></view>
        </block>

        <block wx:if="{{ resumeData.otherRewardList.length }}">
          <view class="subtitle">其他荣誉<view class="action is-add" bindtap="handleOtherReward"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.otherRewardList }}" wx:key="index" data-data="{{ item }}" bindtap="handleOtherReward">
              <view class="cell">
                <text class="label max-width-75 font-size-30">{{ item.name }}</text>
                <text class="value">{{ item.obtainDate }}</text>
              </view>

              <view class="info">{{ utils.formatArrayString(['获奖级别：', item.level], ['获奖角色：', item.role]) }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handleOtherReward"><text class="text">其他荣誉</text></view>
        </block>
      </view>

      <view class="column">
        <view class="title">技能特长</view>

        <block wx:if="{{ resumeData.certificateList.length === 0 && resumeData.skillList.length === 0 && resumeData.otherSkillList.length === 0 }}">
          <view class="tips">用人部门筛选学术科研类候选人的重要依据</view>
        </block>

        <block wx:if="{{ resumeData.certificateList.length }}">
          <view class="subtitle">资质证书<view class="action is-add" bindtap="handleCertificate"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.certificateList }}" wx:key="index" data-data="{{ item }}" bindtap="handleCertificate">
              <view class="cell">
                <text class="label max-width-75 font-size-28 font-weight-normal">{{ item.certificateName }}</text>
                <text class="value">{{ item.obtainDate }}</text>
              </view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handleCertificate"><text class="text">资质证书</text></view>
        </block>

        <block wx:if="{{ resumeData.skillList.length }}">
          <view class="subtitle">技能/语言<view class="action is-add" bindtap="handleSkill"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.skillList }}" wx:key="index" data-data="{{ item }}" bindtap="handleSkill">
              <view class="cell">
                <text class="label max-width-75 font-size-28 font-weight-normal">{{ item.skillName }}</text>
                <text class="value font-color font-size-28">{{ item.degreeTypeName }}</text>
              </view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handleSkill"><text class="text">技能/语言</text></view>
        </block>

        <block wx:if="{{ resumeData.otherSkillList.length }}">
          <view class="subtitle">其他技能<view class="action is-add" bindtap="handleOtherSkill"></view></view>
          <view class="list">
            <view class="item" wx:for="{{ resumeData.otherSkillList }}" wx:key="index" data-data="{{ item }}" bindtap="handleOtherSkill">
              <view class="cell">
                <text class="label max-width-75 font-size-28">{{ item.name }}</text>
                <text class="value font-color font-size-28">{{ item.degreeTypeName }}</text>
              </view>

              <view class="content" wx:if="{{ item.description.length }}">技能描述：{{ item.description }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="add-button" bindtap="handleOtherSkill"><text class="text">其他技能</text></view>
        </block>
      </view>

      <view class="column">
        <block wx:if="{{ resumeData.additionalList.length }}">
          <view class="title">附加信息<view class="action is-add" bindtap="handleAdditional"></view></view>

          <view class="list">
            <view class="item" wx:for="{{ resumeData.additionalList }}" wx:key="index" data-data="{{ item }}" bindtap="handleAdditional">
              <view class="cell">
                <text class="value flex-1 font-color font-size-30 font-weight-bold">{{ item.themeIdName || item.themeName }}</text>
              </view>

              <view class="content" wx:if="{{ item.content.length }}">主题描述：{{ item.content }}</view>
            </view>
          </view>
        </block>

        <block wx:else>
          <view class="title">附加信息<view class="action is-add" bindtap="handleAdditional">让单位更加全面了解您</view></view>
        </block>
      </view>
    </view>
  </block>

  <!-- todo next version -->
  <!-- <view class="resume-tabbar">
    <view class="item person">个人中心</view>
    <view class="item refresh">刷新简历</view>
    <view class="item preview">预览简历</view>
  </view> -->
</view>
