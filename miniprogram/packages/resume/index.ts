import { handleUploadImage } from '@/utils/upload'
import { getResumeData } from './api/index'
import { h5 } from '@/settings'
import { showLoading, toWebPage } from '@/utils/util'

/**
 * todo 目前以下都将跳转至H5
 * 基本信息：/resume/base-info
 * 教育经历：/resume/resume-education-view?id=201920
 * 科研项目：/resume/resume-research-project-view?id=50570?id=123963
 * 工作经历：/resume/edit-work?id=108165
 * 学术论文：/resume/paper?id=66430
 * 学术专利：/resume/patent?id=10042
 * 学术专著：/resume/book?id=2481
 * 学术奖励：/resume/reward?id=68712
 * 其他荣誉：/resume/other-reward?id=126539
 * 资质证书：/resume/certificate?id=116483
 * 技能：/resume/skill?id=116657
 * 其他技能：/resume/other-skill?id=9152
 * 附加信息：/resume/additional-info?id=23574
 */

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,

    hintText: '',
    hintStatus: false,

    optimizationList: [],

    identityTip: {
      show: false,
      btnText: '立即完善',
      content: '',
      h5Url: ''
    },

    resumeData: {
      memberId: '',
      resumeId: '',
      complete: '',

      username: '',
      avatar: '',
      name: '',
      age: '',
      gender: '',
      topEducation: '',
      topEducationMajor: '',
      workExperience: '',
      fullMobile: '',
      email: '',
      titleArr: [],

      advantage: '',

      workStatus: '',
      arriveDate: '',
      arriveDateType: '',
      intentionInfo: '',
      intentionList: [],

      educationList: [],

      researchDirection: '',

      projectList: [],

      workList: [],

      paperList: [],
      patentList: [],
      bookList: [],

      rewardList: [],
      otherRewardList: [],

      certificateList: [],
      skillList: [],
      otherSkillList: [],

      additionalList: []
    }
  },

  onBack() {
    wx.navigateBack()
  },

  onGoHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  async handleOptimization(event: WechatMiniprogram.CustomEvent) {
    const { url } = event.currentTarget.dataset

    // 头像缺失
    if (url === 'avatar') {
      await this.handleAvatar()
      return
    }

    // 跳转小程序路由
    if (/^\/(pages|packages)\//.test(url)) {
      wx.navigateTo({ url })
      return
    }

    // 跳转至H5
    toWebPage(`${h5}${url}`)
  },

  handleAvatar() {
    handleUploadImage({ url: '/resume/upload-avatar', count: 1 }, (result: any) => {
      const [{ data }] = result
      const { resumeData } = this.data

      resumeData.avatar = data.fullUrl

      this.setData({ resumeData }, this.fetchData)
    })
  },

  handleBasic(e: any) {
    const {
      currentTarget: {
        dataset: { url }
      }
    } = e
    toWebPage(url ? `${h5 + url}` : `${h5}/resume/base-info`)
  },

  handleAdvantage() {
    wx.navigateTo({ url: '/packages/resume/advantage' })
  },

  handleIntention() {
    wx.navigateTo({ url: '/packages/resume/intention' })
  },

  handleIntentionItem(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    wx.navigateTo({ url: `/packages/resume/intentionItem?id=${data.id}` })
  },

  handleEducation(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/resume-education-view${data ? `?id=${data.id}` : ''}`)
  },

  handleResearch() {
    wx.navigateTo({ url: '/packages/resume/research' })
  },

  handleProject(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/resume-research-project-view${data ? `?id=${data.id}` : ''}`)
  },

  handleWork(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/edit-work${data ? `?id=${data.id}` : ''}`)
  },

  handlePaper(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/paper${data ? `?id=${data.id}` : ''}`)
  },

  handlePatent(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/patent${data ? `?id=${data.id}` : ''}`)
  },

  handleBook(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/book${data ? `?id=${data.id}` : ''}`)
  },

  handleReward(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/reward${data ? `?id=${data.id}` : ''}`)
  },

  handleOtherReward(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/other-reward${data ? `?id=${data.id}` : ''}`)
  },

  handleCertificate(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/certificate${data ? `?id=${data.id}` : ''}`)
  },

  handleSkill(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/skill${data ? `?id=${data.id}` : ''}`)
  },

  handleOtherSkill(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/other-skill${data ? `?id=${data.id}` : ''}`)
  },

  handleAdditional(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset

    toWebPage(`${h5}/resume/additional-info${data ? `?id=${data.id}` : ''}`)
  },

  async fetchData() {
    // this.setData({ loading: true })
    showLoading()

    const { info, identityTipList, resumeUnCompleteList } = await getResumeData()
    const { completeTipsInfo, ...rest } = info
    const { isComplete, text } = completeTipsInfo

    let identityTip = this.data.identityTip
    const [identityTipObj = {}] = identityTipList

    identityTip = identityTipObj
    identityTip.show = !!identityTipList.length

    this.setData(
      { optimizationList: resumeUnCompleteList, identityTip, resumeData: rest, hintText: text, hintStatus: isComplete },
      () => wx.hideLoading()
    )
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
