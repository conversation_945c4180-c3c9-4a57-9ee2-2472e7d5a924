const defaultPlaceholder = '请选择'

Component({
  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {
    custom: { type: Boolean, value: false },

    /** 选项值 string | string[] */
    model: { type: null, value: '' },

    arrow: { type: Boolean, value: true },

    placeholder: { type: String, value: defaultPlaceholder }
  },

  /**
   * 组件的初始数据
   */
  data: {
    label: '',
    value: '',
    placeholderClass: ''
  },

  observers: {
    model() {
      this.updateData()
    }
  },

  lifetimes: {
    attached() {
      this.updateData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    updateData() {
      const { model, custom, placeholder } = this.data
      const value = Array.isArray(model) ? model.join() : model
      const label = value ? value : placeholder ? placeholder : defaultPlaceholder
      const placeholderClass = value || custom ? '' : 'is-placeholder'

      this.setData({ label, value, placeholderClass })
    },

    handleClick() {
      this.triggerEvent('click')
    }
  }
})
