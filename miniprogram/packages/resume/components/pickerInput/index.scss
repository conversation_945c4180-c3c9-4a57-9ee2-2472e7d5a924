@use 'styles/variables' as *;

.picker-input {
  &.is-placeholder {
    .t-cell__title-text {
      font-weight: initial;
      --td-cell-title-color: var(--font-color-tips);
    }
  }

  .t-cell__title {
    overflow: hidden;
  }

  .t-cell__title-text {
    --td-cell-title-color: var(--font-color);
    --td-cell-title-font-size: 30rpx;

    @include utils-ellipsis;

    display: block;
    font-weight: bold;
  }

  .t-cell {
    --td-cell-vertical-padding: 0;
    --td-cell-horizontal-padding: 0;
  }

  .t-icon {
    font-size: 38rpx;
  }
}
