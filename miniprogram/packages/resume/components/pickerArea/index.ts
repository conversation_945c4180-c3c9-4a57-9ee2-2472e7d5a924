import { getIntentionCity, getNative } from '@/utils/store'

Component({
  externalClasses: ['t-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    /** 单选 string；多选 string[] */
    model: { type: null, value: '' },

    /** 单选 string；多选 string[] */
    label: { type: null, value: '' },

    /** native -> 户籍/国籍； intention -> 意向城市 */
    type: { type: String, value: 'native' },

    title: { type: String, value: '请选择' },

    limit: { type: Number, value: 1 }
  },

  /**
   * 组件的初始数据
   */
  data: {
    visible: false,

    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const { type } = this.data
      const requestOptions = {
        native: getNative,
        intention: getIntentionCity
      }

      type keyType = keyof typeof requestOptions

      const valid = Object.keys(requestOptions).includes(type)
      const api = valid ? requestOptions[<keyType>type] : getNative
      const options = <never>await api()

      this.setData({ options })
    },

    handleClick() {
      this.setData({ visible: true })
    },

    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.setData({ label })
      this.triggerEvent('change', { label, value })
    }
  }
})
