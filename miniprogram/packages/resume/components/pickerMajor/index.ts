import { getMajor } from '../../api/config'

Component({
  externalClasses: ['t-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    model: { type: String, value: '' },

    label: { type: String, value: '' },

    title: { type: String, value: '请选择' }
  },

  /**
   * 组件的初始数据
   */
  data: {
    visible: false,

    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const options = <never>await getMajor()
      this.setData({ options })
    },

    handleClick() {
      this.setData({ visible: true })
    },

    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.setData({ label })
      this.triggerEvent('change', { label, value })
    }
  }
})
