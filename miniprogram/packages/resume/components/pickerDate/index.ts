import dayjs from 'dayjs'

const current: any = dayjs()

const currentYear = current.$y

const sofarValue = '0000'

const sofarLabel = '至今'

const noneValue = '0000'

const noneLabel = '暂无工作经历'

const suffixType = ['sofar', 'none']

const eightYearLater = currentYear + 8

const currentMonth = current.$M + 1

const startYear = 1900

const getYearOptions = (year: number) => {
  const step = year - startYear + 1

  return new Array(step).fill('').map((_item, index) => {
    const value = (startYear + index).toString()

    return { label: `${value}年`, value }
  })
}

const getMonthOptions = (month: number) => {
  return new Array(month).fill('').map((_item, index) => {
    const value = (index + 1).toString().padStart(2, '0')
    return { label: `${value}月`, value }
  })
}

const defaultYearOptions = getYearOptions(currentYear)

const soFarYearOptions = (() => {
  const val = getYearOptions(currentYear)

  val.push({ label: sofarLabel, value: sofarValue })
  return val
})()

const eightYearLaterOptions = (() => getYearOptions(eightYearLater))()

const noneOptions = (() => {
  const val = getYearOptions(currentYear)

  val.push({ label: noneLabel, value: noneValue })
  return val
})()

const defaultMonthOptions = getMonthOptions(12)

const currentMonthOptions = getMonthOptions(currentMonth)

const yearOptions = {
  now: <never[]>defaultYearOptions,
  sofar: <never[]>soFarYearOptions,
  '8': <never[]>eightYearLaterOptions,
  none: <never[]>noneOptions
}

const monthOptions = {
  now: <never[]>currentMonthOptions,
  all: <never[]>defaultMonthOptions
}

const dateDefaultValue = [`${currentYear}`, `${currentMonth.toString().padStart(2, '0')}`]

Component({
  options: {
    styleIsolation: 'shared'
  },
  /**
   * 组件的属性列表
   */
  properties: {
    // now -> 现在；sofar -> 至今；8 -> 8年后；none -> 暂无工作经验
    type: { type: String, value: 'now' },

    model: { type: String, value: '' },

    title: { type: String, value: '选择日期' },

    arrow: { type: Boolean, value: true },

    placeholder: { type: String, value: '' },

    defaultValue: { type: Array, value: dateDefaultValue }
  },

  /**
   * 组件的初始数据
   */
  data: {
    yearOptions: [],
    monthOptions: [],

    label: '',
    value: <string[]>[],

    visible: false
  },

  observers: {
    model() {
      this.updateValue()
    }
  },

  lifetimes: {
    attached() {
      const { type } = this.data
      const exist = Object.keys(yearOptions).includes(type)

      this.setData({
        yearOptions: exist ? yearOptions[<keyof typeof yearOptions>type] : yearOptions['now']
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    updateValue() {
      const { model, defaultValue } = this.data
      const value = model.split('-')
      const valid = value.length === 2

      if (valid) {
        const { type } = this.data

        let label = ''
        switch (type) {
          case 'sofar':
            label = value[0] === sofarValue ? sofarLabel : model
            break
          case 'none':
            label = value[0] === noneValue ? noneLabel : model
            break
          default:
            label = model
        }

        this.setData({ label, value })
      }

      this.updateMonthOptions(valid ? value : defaultValue)
    },

    getMonthOptions(year: string) {
      const { type } = this.data
      const defaultOptions = monthOptions['all']
      const options = {
        [currentYear]: monthOptions['now'],
        [sofarValue]: []
      }

      if (type === '8') {
        return defaultOptions
      } else {
        if (Object.keys(options).includes(year)) {
          return options[<keyof typeof options>year]
        }
        return defaultMonthOptions
      }
    },

    updateMonthOptions(value: string[]) {
      const [year] = value
      const monthOptions = <never[]>this.getMonthOptions(year)

      this.setData({ value, monthOptions })
    },

    handleClick() {
      this.setData({ visible: true }, this.updateValue)
    },

    handlePick(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.detail

      this.updateMonthOptions(value)
    },

    handleConfirm(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.detail
      const { type } = this.data

      const realValue = value
      if (suffixType.includes(type) && value[1] === undefined) {
        realValue[1] = '00'
      }

      this.triggerEvent('change', { value: realValue.join('-') })
    }
  }
})
