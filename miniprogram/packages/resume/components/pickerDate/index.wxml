<view class="picker-date">
  <picker-input model="{{ label }}" arrow="{{ arrow }}" placeholder="{{ placeholder }}" bindclick="handleClick" />

  <t-picker class="picker" visible="{{ visible }}" value="{{ value.length === 2 ? value : defaultValue }}" title="{{ title }}" cancelBtn="取消" confirmBtn="确认" bindpick="handlePick" bindchange="handleConfirm">
    <t-picker-item options="{{ yearOptions }}"></t-picker-item>
    <t-picker-item options="{{ monthOptions }}"></t-picker-item>
  </t-picker>
</view>
