@use 'styles/variables' as *;

.gender-container {
  .gender-item {
    display: inline-flex;
    position: relative;
    width: 120rpx;
    height: 120rpx;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;

    & + .gender-item {
      margin-left: 30rpx;
    }

    $list: male female;

    @each $item in $list {
      &.#{$item} {
        background-image: url(#{$assets}/resume/#{$item}.png);
      }
    }

    .radio {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 32rpx;
      height: 32rpx;
      background: url(#{$assets}/resume/checkbox.png) no-repeat center / contain;
    }

    &.is-checked {
      &::after {
        background-image: url(#{$assets}/resume/checkbox-checked.png);
      }
    }
  }
}
