Component({
  /**
   * 组件的属性列表
   */
  properties: {
    model: { type: String, value: '1' }
  },

  /**
   * 组件的初始数据
   */
  data: {
    value: '',
    options: [
      { label: 'male', value: '1' },
      { label: 'female', value: '2' }
    ]
  },

  observers: {
    model(value) {
      this.setData({ value })
    }
  },

  lifetimes: {
    attached() {
      const { model: value } = this.data

      this.setData({ value })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.detail

      this.triggerEvent('change', { value })
    }
  }
})
