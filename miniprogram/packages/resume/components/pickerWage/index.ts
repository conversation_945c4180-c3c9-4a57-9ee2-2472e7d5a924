import { getWage } from '@/utils/store'

Component({
  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {
    model: { type: String, value: '' },

    modelText: { type: String, value: '' }
  },

  /**
   * 组件的初始数据
   */
  data: {
    label: '',
    value: [],

    viisble: false,
    keys: { label: 'v', value: 'k' },
    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  observers: {
    model(value: string) {
      const val = <never[]>[value]

      this.setData({ value: val })
    },

    modelText(label: string) {
      this.setData({ label })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const data = <never[]>await getWage()

      this.setData({ options: data })
    },

    handleClick() {
      this.setData({ visible: true })
    },

    handlePickerChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.setData({ label: label.join(), value })
      this.triggerEvent('change', { value: value.join() })
    }
  }
})
