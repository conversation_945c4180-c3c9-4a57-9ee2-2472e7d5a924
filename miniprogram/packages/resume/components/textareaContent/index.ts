Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: { type: String, value: '' },

    subtitle: { type: String, value: '' },

    placeholder: { type: String, value: '' },

    placeholderStyle: { type: String, value: 'color: rgba(51, 51, 51, 0.4); font-size: 28rpx;' },

    limit: { type: Number, value: 500 },

    value: { type: String, value: '' }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    handleBack() {
      wx.navigateBack()
    },

    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.detail

      this.setData({ value })
    },

    handleSubmit() {
      const { value } = this.data

      this.triggerEvent('change', { value })
    }
  }
})
