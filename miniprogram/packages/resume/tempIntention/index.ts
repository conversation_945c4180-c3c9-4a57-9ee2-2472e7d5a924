import { showToast } from '@/utils/util'
import { getIntentionId, setIntentionId, setIntentionChanged, getEducation } from '@/utils/store'
import { getTempIntention, saveTempIntention } from '../api/required'

Page({
  /**
   * 页面的初始数据
   */
  data: <any>{
    headerStyle: getApp().globalData.headerStyle,
    educationList: [],

    educationId: '',
    majorId: '',
    jobCategoryId: '',
    areaId: '',

    majorTxt: '',
    jobCategoryTxt: '',
    areaTxt: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getTempId()
    this.getList()
  },

  getTempId() {
    const id = getIntentionId()
    if (!id) return
    getTempIntention({ id }).then((resp: any) => {
      const { topEducationCode, majorId, jobCategoryId, areaId, majorName, jobCategoryName, areaName } = resp
      this.setData({
        educationId: topEducationCode,
        majorId,
        jobCategoryId,
        areaId,
        majorTxt: majorName,
        jobCategoryTxt: jobCategoryName,
        areaTxt: areaName
      })
    })
  },

  getList() {
    getEducation().then((resp: any) => {
      this.setData({ educationList: resp })
    })
  },

  educationChange(e: any) {
    const {
      detail: { value }
    } = e
    this.setData({
      educationId: value
    })
  },

  handleUpdateInputValue(e: WechatMiniprogram.CustomEvent) {
    const {
      detail: { value },
      currentTarget: {
        dataset: { key }
      }
    } = e
    this.setData({ [key]: value })
  },

  handleSave() {
    const { educationId, majorId, jobCategoryId, areaId } = this.data

    if (!educationId) {
      showToast('请选择学历')
      return
    }
    if (!majorId) {
      showToast('请选择所学专业')
      return
    }
    if (!jobCategoryId) {
      showToast('请选择意向职位')
      return
    }
    if (!areaId) {
      showToast('请选择意向城市')
      return
    }
    saveTempIntention({ educationId, majorId, jobCategoryId, areaId }).then((resp: any) => {
      const { intentionId } = resp
      setIntentionId(intentionId)
      showToast('保存成功', 'succeed')
      // 这里需要改一下求职意向被改变的值
      setIntentionChanged(true)
      wx.navigateBack()
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
