@use 'styles/variables' as *;

.temporary-intention {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.temporary-content {
  padding: 14rpx 30rpx env(safe-area-inset-bottom);
  background: url(#{$assets}/person/star-adorn.png) no-repeat right 30rpx top 14rpx/138rpx 160rpx;

  .wrapper-title {
    font-size: 46rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    &:before {
      content: '';
      width: 10rpx;
      height: 44rpx;
      background: $color-primary;
      border-radius: 6rpx;
      margin-right: 14rpx;
    }
  }

  .require-tips {
    margin-top: 30rpx;
    font-size: 26rpx;
    color: $color-primary;
    background-color: #fdf5ec;
    border-radius: 23rpx;
    text-align: center;
    line-height: 46rpx;
    width: 670rpx;
  }

  .education {
    margin-top: 100rpx;

    .title {
      font-size: 30rpx;
      padding-bottom: 20rpx;
    }

    .content {
      display: flex;
      flex-wrap: wrap;
      margin-right: -20rpx;

      .item {
        border: 2rpx solid $border-color;
        border-radius: $border-radius;
        line-height: 102rpx;
        height: 110rpx;
        width: 208rpx;
        text-align: center;
        margin: 20rpx 20rpx 0rpx 0;
        overflow: hidden;

        &:nth-child(1),
        &:nth-child(2) {
          width: 326rpx;
        }

        &.checked {
          border-color: $color-primary;
          background-color: $color-primary-background;

          .radio {
            --td-radio-label-checked-color: var(--color-primary);
            font-weight: bold;
          }
        }

        .radio {
          background: transparent;
          font-size: 28rpx;
        }
      }
    }
  }

  .intention-info {
    margin-top: 36rpx;

    .row {
      display: flex;
      justify-content: space-between;
      padding: 42rpx 0;
      border-bottom: 2rpx solid $border-color;
    }

    .label {
      font-size: 30rpx;
      flex-shrink: 0;
    }
    .value {
      flex-grow: 1;
    }
    .txt {
      .picker-input {
        .t-cell {
          justify-content: flex-end;
        }

        .t-cell__title {
          flex-grow: 0;
        }
      }
    }
  }

  .save {
    padding: 40rpx 24rpx;
  }
}
