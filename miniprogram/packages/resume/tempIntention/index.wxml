<t-navbar style="{{ headerStyle }}" left-arrow></t-navbar>

<view class="temporary-content">
  <view class="wrapper-title">填写求职意向</view>
  <view class="require-tips">需要您提供以下信息，以便为您推荐匹配度更高的职位</view>

  <view class="education">
    <view class="title">请选择您的最高学历（或即将取得的最高学历）</view>
    <t-radio-group t-class="content" bind:change="educationChange" borderless>
      <view wx:for="{{ educationList }}" wx:key="index" class="item {{ educationId == item.k ? 'checked' : '' }}">
        <t-radio t-class="radio" value="{{ item.k }}" label="{{ item.v }}" icon="none" />
      </view>
    </t-radio-group>
  </view>

  <view class="intention-info">
    <view class="row">
      <view class="label">所学专业</view>
      <view class="value">
        <picker-major model="{{ majorId }}" label="{{ majorTxt }}" title="所学专业" bindchange="handleUpdateInputValue" data-key="majorId" t-class="txt" />
      </view>
    </view>
    <view class="row">
      <view class="label">意向职位</view>
      <view class="value">
        <picker-job-category model="{{ jobCategoryId }}" label="{{ jobCategoryTxt }}" title="意向职位" bindchange="handleUpdateInputValue" data-key="jobCategoryId" t-class="txt" />
      </view>
    </view>
    <view class="row">
      <view class="label">意向城市</view>
      <view class="value">
        <picker-area model="{{ areaId }}" type="intention" label="{{ areaTxt }}" title="意向城市" bindchange="handleUpdateInputValue" data-key="areaId" t-class="txt" />
      </view>
    </view>
  </view>

  <view class="save">
    <t-button block theme="primary" size="large" bind:tap="handleSave">保存</t-button>
  </view>
</view>
