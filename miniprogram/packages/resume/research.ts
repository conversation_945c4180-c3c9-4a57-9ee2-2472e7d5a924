import { addResearchData, getResearchData, setResearchData } from './api/index'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    value: ''
  },

  async handleChange(event: WechatMiniprogram.CustomEvent) {
    const { id } = this.data
    const { value } = event.detail
    const method = id ? setResearchData : addResearchData

    await method({ id, researchDirection: value })
    wx.navigateBack()
  },

  async fetchData() {
    const { id = '', content = '' } = await getResearchData()

    this.setData({ id, value: content })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
