// @ts-check

/**
 *
 * @param {string} name
 * @returns {string}
 */
function suffixClass(name) {
  // @ts-ignore
  // prettier-ignore
  var reg = getRegExp('\.(\w+)$')
  var res = reg.exec(name)

  return res ? res[1] : ''
}

/**
 *
 * @param {string} value
 * @returns {boolean}
 */
function isString1(value) {
  return value === '1'
}

/**
 *
 * @param {...string} arguments
 * @returns {string}
 */
function formatString() {
  var result = []

  for (var index = 0; index < arguments.length; index++) {
    var value = arguments[index]

    if (value) {
      result.push(value)
    }
  }

  return result.join(' | ')
}

/**
 *
 * @param {...string[]} arguments
 * @returns {string}
 */
function formatArrayString() {
  var result = []

  for (var index = 0; index < arguments.length; index++) {
    var data = arguments[index]
    var label = data[0]
    var value = data[1]
    var suffix = data[2] || ''

    if (value) {
      result.push(label + value + suffix)
    }
  }

  return result.join(' | ')
}

module.exports = {
  isString1: isString1,
  suffixClass: suffixClass,
  formatString: formatString,
  formatArrayString: formatArrayString
}
