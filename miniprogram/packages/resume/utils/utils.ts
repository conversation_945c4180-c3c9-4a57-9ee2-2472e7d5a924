import { defaultDuration } from '@/settings'

/**
 * 校验数据
 * @param rules
 * @param data
 * @returns
 */
export function validate(rules: Miniprogram.ValidateRules, data: any) {
  const messages: string[] = []

  Object.keys(rules).map((name: string) => {
    rules[name].map((rule: Miniprogram.ValidateRule) => {
      const value = data[name]
      const { required, message, validator } = rule

      if (validator) {
        if (validator(value) === false) {
          messages.push(message)
        }
      } else {
        if (required && value?.length === 0) {
          messages.push(message)
        }
      }
    })
  })

  if (messages.length) {
    wx.showToast({ title: messages[0], icon: 'none', duration: defaultDuration })
  }

  return messages.length === 0
}
