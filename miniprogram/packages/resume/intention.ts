import { getResumeIntentionList, editWorkStatus, editArriveDateType } from './api/intention'

// packages/resume/intention.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    intentionList: [],
    postStatusList: [],
    workStatus: 0,
    arriveDateType: 0,
    workStatusText: '',
    arriveDate: '',
    canAdd: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getList()
  },

  handleBack() {
    wx.navigateBack()
  },

  handleNavigate(event: WechatMiniprogram.CustomEvent) {
    const { id } = event.currentTarget.dataset
    let url = '/packages/resume/intentionItem'

    if (id) {
      url += `?id=${id}`
    }

    wx.navigateTo({ url })
  },

  async getList() {
    const { intentionList, postStatusList } = await getResumeIntentionList()
    this.setData({
      intentionList,
      postStatusList,
      canAdd: intentionList.length < 5
    })
  },

  async handleChange(data: any) {
    const { value } = data.detail
    const { key } = data.currentTarget.dataset
    const postData = <any>{}

    postData[key] = value

    const api = key === 'workStatus' ? editWorkStatus : editArriveDateType
    await api(postData)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
