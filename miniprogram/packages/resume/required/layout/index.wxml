<view class="required-container">
  <header title="{{ title }}" bindback="handleBack" />

  <t-dialog visible="{{ dialogVisible }}" title="提示" confirm-btn="{{ confirmButton }}" cancel-btn="{{ cancelButton }}" bindconfirm="handleDialogConfirm" bindcancel="handleDialogCancel">
    <view class="dialog-content" slot="content">
      <view class="content">确定要退出填写吗？</view>
      <view class="tips">（退出后本页面信息将不做保存）</view>
    </view>
  </t-dialog>

  <view class="main">
    <view class="title">
      <text class="text">{{ pageTitle }}</text>
    </view>

    <view class="content">
      <slot></slot>
    </view>
  </view>
</view>
