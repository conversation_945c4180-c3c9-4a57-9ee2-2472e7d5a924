@use 'styles/variables' as *;

.required-container {
  background-color: var(--color-white);

  .dialog-content {
    text-align: center;
  }

  .main {
    padding: 18rpx 0 30rpx;

    .title {
      padding: 42rpx 40rpx 73rpx 30rpx;
      background: url(#{$assets}/resume/header.png) no-repeat 532rpx 18rpx / 137rpx 159rpx;

      .text {
        position: relative;
        display: flex;
        padding-left: 25rpx;
        font-size: 46rpx;
        font-weight: bold;
        line-height: 1;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 11rpx;
          height: 100%;
          border-radius: 6rpx;
          background-color: var(--color-primary);
        }
      }
    }

    .content {
      margin-top: -33rpx;

      .t-cell {
        --td-cell-vertical-padding: 0;
        --td-cell-horizontal-padding: 0;
      }

      .cell-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 164rpx;
        box-shadow: 0 1px 0 0 var(--border-color);

        &.none-shadow {
          box-shadow: none;
        }

        &.is-row {
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .cell-label {
            margin-bottom: 0;
          }
        }

        &.is-special {
          height: auto;
          box-shadow: none;

          .cell-label {
            margin-top: 40rpx;
          }
        }
      }

      .cell-label {
        margin-bottom: 30rpx;
        font-size: 28rpx;

        &.is-required::before {
          content: '*';
          color: var(--color-point);
        }
      }

      .cell-value {
        .t-input {
          --td-input-vertical-padding: 0;
        }
      }

      .button-cell {
        display: flex;
        padding: 60rpx 0 30rpx;

        .t-button {
          flex: 1;

          & + .t-button {
            margin-left: 20rpx;
          }
        }

        .prev-button {
          max-width: 270rpx;
          box-shadow: 0 0 0 1px var(--color-primary);
        }

        .next-button {
          max-width: 400rpx;
        }
      }

      .t-input__control {
        color: $font-color;
        font-size: 30rpx;
        font-weight: bold;
      }

      .t-input__placeholder {
        font-size: 30rpx;
      }

      .t-textarea__placeholder {
        font-size: 28rpx;
      }

      .t-textarea__wrapper-inner {
        color: $font-color;
        font-size: 28rpx;
      }

      .t-input__placeholder,
      .t-textarea__placeholder {
        color: var(--font-color-tips);
        font-weight: normal;
      }
    }
  }
}
