const pageTitleList = ['基本信息', '最高教育经历', '求职意向']

Component({
  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {
    model: { type: Number, value: 1 }
  },

  /**
   * 组件的初始数据
   */
  data: {
    title: '',

    dialogVisible: false,
    confirmButton: { content: '继续填写', variant: 'base' },
    cancelButton: { content: '下次再填', variant: 'base' },

    pageTitle: ''
  },

  observers: {
    model(value) {
      this.updateTitle(value)
    }
  },

  lifetimes: {
    attached() {
      const { model } = this.data

      this.updateTitle(model)
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    updateTitle(value: number) {
      const title = `创建简历（${value}/3）`
      const index = value - 1
      const valid = index >= 0 && index < pageTitleList.length
      const pageTitle = valid ? pageTitleList[index] : ''

      this.setData({ title, pageTitle })
    },

    handleBack() {
      this.setData({ dialogVisible: true })
    },

    handleDialogConfirm() {
      this.setData({ dialogVisible: false })
    },

    handleDialogCancel() {
      this.handleDialogConfirm()
      wx.navigateBack()
    }
  }
})
