@use 'styles/variables' as *;

.education-container {
  padding: 0 30rpx;

  .picker-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .picker-duration {
    flex: 1;
  }

  .cell-checkbox {
    margin-top: 20rpx;

    .t-checkbox {
      margin-right: 40rpx;
      font-size: 26rpx;

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  .cell-value {
    &.is-duration {
      .picker-cell-title {
        display: flex;
        align-items: center;
      }

      .duration-line {
        margin: 0 30rpx;
        width: 26rpx;
        height: 2rpx;
        background-color: $font-color;
      }
    }
  }

  .cell-tips {
    margin-top: 20rpx;
    font-size: 26rpx;
    text-align: right;

    .text {
      color: var(--color-primary);
    }
  }

  .external-class {
    padding: 27rpx 30rpx 0;
    height: 320rpx;
    background-color: #f9f9f9;
    border-radius: 8rpx;
  }
}
