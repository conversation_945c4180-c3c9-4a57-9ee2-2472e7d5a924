import inputEvent from '@/mixins/inputEvent'
import { getEducationData, setEducationData } from '../../api/required'
import { validate } from '../../utils/utils'
import { setIntentionChanged } from '@/utils/store'
import { CheckStatus } from '@/settings'

Component({
  behaviors: [inputEvent],

  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {
    showSwitchMajor: false,
    showCustomMajor: false,

    showMentor: false,

    rules: {
      school: [{ required: true, message: '请填写学校名称' }],
      studyBeginDate: [{ required: true, message: '请选择入学时间' }],
      studyEndDate: [{ required: true, message: '请选择毕业时间' }],
      educationId: [{ required: true, message: '请选择学历水平' }],
      majorId: [{ required: true, message: '请选择所学专业' }],
      majorCustom: [{ required: true, message: '请填写所学专业' }]
    },

    checkedState: CheckStatus.checked,

    intentionId: '',

    school: '',
    isRecruitment: CheckStatus.checked,
    isProjectSchool: CheckStatus.uncheck,
    isOverseasStudy: CheckStatus.uncheck,
    studyBeginDate: '',
    studyEndDate: '',
    educationId: '',
    educationText: '',
    majorId: '',
    majorText: '',
    majorCustom: '',
    college: '',
    mentor: '',
    researchDirection: ''
  },

  observers: {
    educationId(value: string) {
      /** 硕士及以上；不可自定义专业 3 -> 硕士；4 -> 博士 */
      // “博士/硕士”显示“导师”字段 3 -> 硕士；4 -> 博士
      const { showCustomMajor } = this.data
      const valid = /^[34]$/.test(value)

      this.setData({
        showSwitchMajor: value.length > 0 && valid === false,
        showCustomMajor: valid ? false : showCustomMajor,
        showMentor: valid
      })
    }
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      let showCustomMajor = false
      const data: any = await getEducationData()

      Object.keys(data).map((name: string) => {
        const value = data[name]
        if (/^(isRecruitment|isProjectSchool|isOverseasStudy)$/.test(name)) {
          if (value.length === 0) {
            data[name] = name === 'isRecruitment' ? CheckStatus.checked : CheckStatus.uncheck
          }
        }
      })

      if (data.majorCustom) {
        showCustomMajor = true
      }

      this.setData({ ...data, showCustomMajor })
    },

    handleCheckboxChange(event: WechatMiniprogram.CustomEvent) {
      const { detail, target } = event
      const { checked } = detail
      const { key } = target.dataset

      this.setData({ [key]: checked ? CheckStatus.checked : CheckStatus.uncheck })
    },

    handleSwitchCustom(event: WechatMiniprogram.CustomEvent) {
      const { dataset } = event.target

      if (Object.keys(dataset).includes('target')) {
        const { showCustomMajor } = this.data

        this.setData({ showCustomMajor: !showCustomMajor })
      }
    },

    handleUpdateMajor(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.setData({ majorId: value, majorText: label })
    },

    handlePrev() {
      this.triggerEvent('prev')
    },

    async handleNext() {
      const {
        intentionId,
        showCustomMajor,
        rules,
        school,
        isRecruitment,
        isProjectSchool,
        isOverseasStudy,
        studyBeginDate,
        studyEndDate,
        educationId,
        majorId,
        majorCustom,
        college,
        mentor,
        researchDirection,
        showMentor
      } = this.data

      const data = {
        intentionId,
        school,
        isRecruitment,
        isProjectSchool,
        isOverseasStudy,
        studyBeginDate,
        studyEndDate,
        educationId,
        majorId,
        majorCustom,
        college,
        mentor: showMentor ? mentor : '',
        researchDirection
      }

      rules.majorId[0].required = !showCustomMajor
      rules.majorCustom[0].required = showCustomMajor

      const valid = validate(rules, data)

      if (valid) {
        data[showCustomMajor ? 'majorId' : 'majorCustom'] = ''
        await setEducationData(data)
        this.triggerEvent('next')
        // 认为用户修改了信息
        setIntentionChanged(true)
      }
    }
  }
})
