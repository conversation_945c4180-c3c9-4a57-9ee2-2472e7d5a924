<view class="education-container">
  <view class="cell-item">
    <view class="cell-label is-required">学校名称</view>

    <view class="cell-value">
      <t-input placeholder="请填写学校名称" borderless="{{ true }}" clearable="{{ true }}" value="{{ school }}" data-key="school" bindchange="handleUpdateInputValue" bindclear="handleClearInputValue" />
    </view>
  </view>

  <view class="cell-checkbox">
    <t-checkbox label="统招" icon="rectangle" block="{{ false }}" value="{{ isRecruitment }}" checked="{{ isRecruitment === checkedState }}" data-key="isRecruitment" bindchange="handleCheckboxChange" />
    <t-checkbox label="985/211" icon="rectangle" block="{{ false }}" value="{{ isProjectSchool }}" checked="{{ isProjectSchool === checkedState }}" data-key="isProjectSchool" bindchange="handleCheckboxChange" />
    <t-checkbox label="留学" icon="rectangle" block="{{ false }}" value="{{ isOverseasStudy }}" checked="{{ isOverseasStudy === checkedState }}" data-key="isOverseasStudy" bindchange="handleCheckboxChange" />
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">就读时间</view>

    <view class="cell-value is-duration">
      <view class="picker-cell">
        <picker-date class="picker-duration" title="入学时间" arrow="{{ false }}" placeholder="入学时间" model="{{ studyBeginDate }}" data-key="studyBeginDate" bindchange="handleUpdateInputValue" />

        <text class="duration-line"></text>

        <picker-date class="picker-duration" title="毕业时间" type="8" arrow="{{ false }}" placeholder="毕业时间" model="{{ studyEndDate }}" data-key="studyEndDate" bindchange="handleUpdateInputValue" />
      </view>
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">学历水平</view>

    <view class="cell-value">
      <picker-education model="{{ educationId }}" model-text="{{ educationText }}" data-key="educationId" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item" wx:if="{{ !showCustomMajor }}">
    <view class="cell-label is-required">所学专业</view>

    <view class="cell-value">
      <picker-major model="{{ majorId }}" label="{{ majorText }}" title="所学专业" bindchange="handleUpdateMajor" />
    </view>
  </view>

  <view class="cell-item" wx:else>
    <view class="cell-label is-required">所学专业</view>

    <view class="cell-value">
      <t-input placeholder="请输入平台未收录专业" borderless="{{ true }}" clearable="{{ true }}" value="{{ majorCustom }}" data-key="majorCustom" bindchange="handleUpdateInputValue" bindclear="handleClearInputValue" />
    </view>
  </view>

  <view class="cell-tips" bindtap="handleSwitchCustom" wx:if="{{ showSwitchMajor }}">
    <view hidden="{{ showCustomMajor }}"> 若无您所需专业，请点击<text class="text" data-target="">自定义录入专业</text> </view>
    <view hidden="{{ !showCustomMajor }}"> 点击恢复<text class="text" data-target="">选择专业</text> </view>
  </view>

  <view class="cell-item">
    <view class="cell-label">二级院系（机构）</view>

    <view class="cell-value">
      <t-input placeholder="请填写" borderless="{{ true }}" clearable="{{ true }}" value="{{ college }}" data-key="college" bindchange="handleUpdateInputValue" bindclear="handleClearInputValue" />
    </view>
  </view>

  <view class="cell-item" wx:if="{{ showMentor }}">
    <view class="cell-label">导师</view>

    <view class="cell-value">
      <t-input placeholder="请填写您的导师信息" borderless="{{ true }}" clearable="{{ true }}" value="{{ mentor }}" data-key="mentor" maxlength="{{ 50 }}" bindchange="handleUpdateInputValue" bindclear="handleClearInputValue" />
    </view>
  </view>

  <view class="cell-item is-special">
    <view class="cell-label">研究方向</view>

    <view class="cell-value">
      <t-textarea t-class="external-class" placeholder="请输入您的研究方向" cursor-spacing="{{ 100 }}" maxlength="500" disableDefaultPadding="{{ true }}" indicator value="{{ researchDirection }}" data-key="researchDirection" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="button-cell">
    <t-button class="prev-button" theme="primary" size="large" variant="outline" bindtap="handlePrev">上一步</t-button>

    <t-button class="next-button" theme="primary" size="large" variant="base" bindtap="handleNext">下一步</t-button>
  </view>
</view>
