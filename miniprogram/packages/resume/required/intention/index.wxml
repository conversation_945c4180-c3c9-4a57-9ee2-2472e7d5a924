<view class="intention-container">
  <view class="cell-item">
    <view class="cell-label is-required">意向职位</view>

    <view class="cell-value">
      <picker-job-category model="{{ jobCategoryId }}" label="{{ jobCategoryText }}" title="意向职位" data-key="jobCategoryId" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">意向城市</view>

    <view class="cell-value">
      <picker-area model="{{ areaId }}" label="{{ areaText }}" type="intention" title="意向城市" limit="5" data-key="areaId" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">工作性质</view>

    <view class="cell-value">
      <picker-nature model="{{ natureType }}" model-text="{{ natureText }}" data-key="natureType" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item none-shadow">
    <view class="cell-label is-required">期望月薪</view>

    <view class="cell-value">
      <picker-wage model="{{ wageType }}" model-text="{{ wageText }}" data-key="wageType" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-space"></view>

  <view class="cell-item">
    <view class="cell-label is-required">求职状态</view>

    <view class="cell-value">
      <picker-status model="{{ workStatus }}" model-text="{{ workStatusText }}" data-key="workStatus" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">到岗时间</view>

    <view class="cell-value">
      <picker-arrive model="{{ arriveDateType }}" model-text="{{ arriveDate }}" data-key="arriveDateType" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="button-cell">
    <t-button class="prev-button" theme="primary" size="large" variant="outline" bindtap="handlePrev">上一步</t-button>

    <t-button class="next-button" theme="primary" size="large" variant="base" bindtap="handleConfirm">保存</t-button>
  </view>

  <t-dialog visible="{{ dialogVisible }}" title="{{ title }}" content="{{ content }}" confirm-btn="{{ confirmButton }}" cancel-btn="{{ cancelButton }}" bindconfirm="handleDialogConfirm" bindcancel="handleDialogCancel" />
</view>
