import inputEvent from '@/mixins/inputEvent'
import { getIntentionData, setIntentionData } from '../../api/required'
import { validate } from '../../utils/utils'
import { setIntentionChanged } from '@/utils/store'

Component({
  behaviors: [inputEvent],

  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {
    intentionId: '',

    jobCategoryId: '',
    jobCategoryText: '',
    areaId: [],
    areaText: [],
    natureType: '',
    natureText: '',
    wageType: '',
    wageText: '',
    workStatus: '',
    workStatusText: '',
    arriveDateType: '',
    arriveDate: '',

    rules: {
      jobCategoryId: [{ required: true, message: '请选择意向职位' }],
      areaId: [{ required: true, message: '请选择意向城市' }],
      natureType: [{ required: true, message: '请选择工作性质' }],
      wageType: [{ required: true, message: '请选择期望月薪' }],
      workStatus: [{ required: true, message: '请选择求职状态' }],
      arriveDateType: [{ required: true, message: '请选择到岗时间' }]
    },

    dialogVisible: false,
    title: '',
    content: '',
    confirmButton: { content: '继续完善', variant: 'base' },
    cancelButton: { content: '稍作休息', variant: 'base' }
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const data: any = await getIntentionData()

      this.setData({ ...data })
    },

    handlePrev() {
      this.triggerEvent('prev')
    },

    async handleConfirm() {
      const { intentionId, jobCategoryId, areaId, natureType, wageType, workStatus, arriveDateType, rules } = this.data
      const data = { intentionId, jobCategoryId, areaId, natureType, wageType, workStatus, arriveDateType }
      const valid = validate(rules, data)

      if (valid) {
        const { title, content } = await setIntentionData(data)

        this.setData({ dialogVisible: true, title, content })

        // 认为用户修改了信息
        setIntentionChanged(true)
      }
    },

    handleDialogConfirm() {
      this.setData({ dialogVisible: false }, () => {
        wx.redirectTo({ url: '/packages/resume/index' })
      })
    },

    handleDialogCancel() {
      this.setData({ dialogVisible: false }, () => {
        wx.navigateBack()
      })
    }
  }
})
