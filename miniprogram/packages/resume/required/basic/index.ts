import dayjs from 'dayjs'
import inputEvent from '@/mixins/inputEvent'
import { getBasicData, setBasicData } from '../../api/required'
import { validEmail } from '@/utils/validate'
import { getUserInfo, setIntentionChanged } from '@/utils/store'
import { validate } from '../../utils/utils'

Component({
  behaviors: [inputEvent],

  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: <Miniprogram.RequiredBasicData>{
    defaultBirthday: [],
    rules: {
      name: [{ required: true, message: '请填写姓名' }],
      birthday: [{ required: true, message: '请选择出生日期' }],
      householdRegisterId: [{ required: true, message: '请选择户籍/国籍' }],
      email: [
        { required: true, message: '请填写邮箱' },
        { validator: validEmail, message: '请填写正确的邮箱地址' }
      ],
      politicalStatusId: [{ required: true, message: '请选择政治面貌' }],
      identityType: [{ required: true, message: '请选择您的身份' }],
      beginWorkDate: [{ required: true, message: '请选择参加工作时间' }]
    },

    name: '',
    gender: '1',
    birthday: '',
    householdRegisterId: '',
    householdRegisterText: '',
    email: '',
    politicalStatusId: '',
    politicalStatusText: '',
    identityType: '1',
    identityTypeText: '',
    beginWorkDate: ''
  },

  lifetimes: {
    async attached() {
      const date: any = dayjs()

      this.setData({ defaultBirthday: [`${date.$y - 18}`, `${date.$M + 1}`] })
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const data: any = await getBasicData()

      this.setData({ ...this.data, ...data })
    },

    async handleConfirm() {
      const {
        rules,
        name,
        gender,
        birthday,
        householdRegisterId,
        email,
        politicalStatusId,
        identityType,
        beginWorkDate
      } = this.data
      const data = {
        name,
        gender,
        birthday,
        householdRegisterId,
        email,
        politicalStatusId,
        identityType
      }
      const postData = identityType === '1' ? { ...data, beginWorkDate } : data
      const valid = validate(rules, postData)

      if (valid) {
        const { mobile } = getUserInfo()

        await setBasicData({ ...postData, mobile })
        this.triggerEvent('next')
        // 认为用户修改了信息
        setIntentionChanged(true)
      }
    }
  }
})
