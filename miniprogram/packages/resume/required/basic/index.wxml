<view class="basic-container">
  <view class="cell-item">
    <view class="cell-label is-required">姓名</view>

    <view class="cell-value">
      <t-input placeholder="请填写姓名" borderless="{{ true }}" clearable="{{ true }}" value="{{ name }}" data-key="name" bindchange="handleUpdateInputValue" bindclear="handleClearInputValue" />
    </view>
  </view>

  <view class="cell-item is-row">
    <view class="cell-label is-required">性别</view>

    <view class="cell-value">
      <gender model="{{ gender }}" data-key="gender" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">出生日期</view>

    <view class="cell-value">
      <picker-date title="出生日期" model="{{ birthday }}" default-value="{{ defaultBirthday }}" data-key="birthday" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">户籍/国籍</view>

    <view class="cell-value">
      <picker-area model="{{ householdRegisterId }}" title="户籍/国籍" label="{{ householdRegisterText }}" data-key="householdRegisterId" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">邮箱</view>

    <view class="cell-value">
      <t-input placeholder="请填写邮箱，用于接收求职反馈" borderless="{{ true }}" clearable="{{ true }}" value="{{ email }}" data-key="email" bindchange="handleUpdateInputValue" bindclear="handleClearInputValue" />
    </view>
  </view>

  <view class="cell-item">
    <view class="cell-label is-required">政治面貌</view>

    <view class="cell-value">
      <picker-political model="{{ politicalStatusId }}" model-text="{{ politicalStatusText }}" data-key="politicalStatusId" bindchange="handleUpdateInputValue" />
    </view>
  </view>
  <view class="cell-item">
    <view class="cell-label is-required">我的身份</view>

    <view class="cell-value">
      <picker-identity model="{{ identityType }}" model-text="{{ identityTypeText }}" data-key="identityType" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="cell-item" wx:if="{{ identityType === '1' }}">
    <view class="cell-label is-required">参加工作时间</view>

    <view class="cell-value">
      <picker-date title="参加工作时间" model="{{ beginWorkDate }}" type="none" data-key="beginWorkDate" bindchange="handleUpdateInputValue" />
    </view>
  </view>

  <view class="button-cell">
    <t-button theme="primary" size="large" bindtap="handleConfirm">下一步</t-button>
  </view>
</view>
