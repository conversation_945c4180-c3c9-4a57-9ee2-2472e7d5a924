import { getCurrentStep } from './api/required'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    step: 0
  },

  handleStep(value: number) {
    const step = this.data.step + value

    wx.pageScrollTo({
      scrollTop: 0,
      success: () => {
        this.setData({ step })
      }
    })
  },

  handlePrevStep() {
    this.handleStep(-1)
  },

  handleNextStep() {
    this.handleStep(1)
  },

  validate(step: string | number) {
    return /^[123]$/.test(step.toString())
  },

  async fetchData() {
    const { validate } = this
    const { step }: any = await getCurrentStep()

    this.setData({ step: validate(step) ? Number(step) : 1 })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
