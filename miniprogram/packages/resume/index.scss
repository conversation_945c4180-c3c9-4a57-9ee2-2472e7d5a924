@use 'styles/variables' as *;

$path: '#{$assets}/resume/index';

@mixin utils-line($x: 0, $y: -1rpx) {
  box-shadow: $x $y 0 0 $border-color;
}

@mixin utils-background {
  background-color: #fff3e4;
}

.resume-container {
  padding-bottom: calc(env(safe-area-inset-bottom) + 50rpx);

  .home-icon {
    width: 30rpx;
    height: 30rpx;
    background: url(#{$assets}/icon/home.png) no-repeat center/contain;
  }

  .resume-hint {
    @include utils-ellipsis;

    padding: 0 30rpx 0 66rpx;
    color: $font-color-basic;
    font-size: 24rpx;
    line-height: 60rpx;
    background-repeat: no-repeat;
    background-position: 30rpx center;
    background-size: 26rpx;
    background-image: url(#{$assets}/icon/warning.png);

    &.is-success {
      background-image: url(#{$assets}/icon/hint.png);
    }
  }

  .optimization {
    padding: 40rpx 0 65rpx 30rpx;
    background: url(#{$path}/background.png) no-repeat center / cover;
    box-sizing: border-box;

    & + .resume-main {
      margin-top: -35rpx;
    }

    .title {
      margin-bottom: 16rpx;
      padding-left: 90rpx;
      font-size: 36rpx;
      font-weight: bold;
      background: url(#{$path}/robot.png) no-repeat left center / contain;
    }

    .num {
      color: $color-primary;
      font-size: 50rpx;
    }

    .content {
      height: 240rpx;
      overflow: hidden;
    }

    .list {
      display: flex;
      flex-wrap: nowrap;
      height: 300rpx;
      overflow-x: auto;
      overflow-y: hidden;
    }

    .item {
      flex: none;
      padding: 30rpx;
      width: 608rpx;
      height: 240rpx;
      color: $font-color;
      line-height: 1;
      background: $color-white;
      box-shadow: 0 5rpx 6rpx 0 rgba(255, 160, 0, 0.05);
      border-radius: $border-radius;
      box-sizing: border-box;

      & + .item {
        margin-left: 20rpx;
      }

      &:last-of-type {
        margin-right: 30rpx;
      }
    }

    .item-title {
      margin-bottom: 24rpx;
      font-size: 32rpx;
      font-weight: bold;
      line-height: 36rpx;
    }

    .item-desc {
      height: 80rpx;
      font-size: 26rpx;
      line-height: 40rpx;
    }

    .item-action {
      color: $color-white;
      font-size: 28rpx;
      text-align: right;

      .button {
        display: inline-block;
        padding: 0 30rpx;
        font-weight: bold;
        line-height: 56rpx;
        background-color: $color-primary;
        border-radius: 28rpx;
      }
    }

    .optimization-content {
      & + .perfect-tips {
        margin-top: 30rpx;
      }
    }

    .perfect-tips {
      border-radius: $border-radius;
      border: 1px solid $color-primary;
      padding: 14rpx 20rpx 14rpx 60rpx;
      margin: 0rpx 30rpx 0 0;
      background: url('#{$assets}/icon/attention-fill.png') no-repeat 20rpx 20rpx/26rpx;
      font-size: 26rpx;

      .perfect-btn {
        color: $color-primary;
        font-size: inherit;
        display: inline-block;
        padding-right: 20rpx;
        background: url('#{$assets}/icon/into.png') no-repeat right 0 center/12rpx 18rpx;
      }
    }
  }

  .resume-main {
    padding: 0 30rpx;
    line-height: 1;
    word-break: break-word;

    .basic {
      display: flex;
      padding: 50rpx 0 40rpx;

      .avatar {
        flex: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 30rpx;
        color: $color-primary;
        font-size: 24rpx;
        line-height: 1;

        .image {
          margin-bottom: 16rpx;
          width: 112rpx;
          height: 112rpx;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .data {
        flex: auto;

        .name {
          margin-bottom: 30rpx;
          font-size: 46rpx;
          font-weight: bold;
          line-height: 56rpx;

          .text {
            padding-right: 60rpx;
            background: url(#{$path}/edit.png) no-repeat right center / 40rpx;
          }
        }

        .info {
          margin-bottom: 24rpx;
          line-height: 1.5;
        }

        .contact {
          line-height: 40rpx;
          font-size: 22rpx;

          .text {
            display: inline-block;
            margin-right: 20rpx;
            padding-left: 40rpx;
            background-repeat: no-repeat;
            background-position: left center;
            background-size: 32rpx;

            &:last-of-type {
              margin-right: 0;
            }

            $list: mobile email;

            @each $var in $list {
              &.#{$var} {
                background-image: url(#{$path}/#{$var}.png);
              }
            }
          }
        }
      }
    }

    .column {
      @include utils-line;

      padding: 50rpx 0;

      &.has-list {
        padding-bottom: 30rpx;
      }

      .list {
        @include utils-line($y: 1rpx);

        &:last-of-type {
          box-shadow: none;
        }
      }
    }

    .title,
    .subtitle {
      display: flex;
      justify-content: space-between;
      font-size: 40rpx;
      font-weight: bold;
      line-height: 46rpx;

      .action {
        padding-right: 64rpx;
        color: $font-color-label;
        font-size: 26rpx;
        font-weight: initial;
        background: url(#{$path}/edit.png) no-repeat right center / 46rpx;

        &.is-add {
          background-image: url(#{$path}/add.png);
        }
      }
    }

    .subtitle {
      position: relative;
      margin-top: 30rpx;
      padding-left: 20rpx;
      font-size: 32rpx;

      &::before {
        content: '';
        position: absolute;
        top: 20rpx;
        left: 0;
        width: 8rpx;
        height: 8rpx;
        background: $color-primary;
        border-radius: 50%;
      }
    }

    .content {
      @include utils-ellipsis-lines(2, 2);

      margin-top: 30rpx;
    }

    .status {
      margin-top: 24rpx;
      color: $color-primary;
      font-size: 26rpx;

      .text {
        @include utils-background;

        display: inline-block;
        padding: 8rpx 20rpx;
        border-radius: 20rpx;
      }
    }

    .tips {
      margin-top: 20rpx;
      color: $font-color-label;
      font-size: 26rpx;
    }

    .add-button {
      display: flex;
      margin-top: 30rpx;
      padding: 24rpx 30rpx 24rpx 48rpx;
      font-size: 32rpx;
      font-weight: bold;
      background-color: #f6f7f9;
      border-radius: 12rpx;

      .text {
        flex: auto;
        line-height: 40rpx;
        background: url(#{$path}/add-ghost.png) no-repeat right center / 40rpx;
      }
    }

    .list {
      display: flex;
      flex-direction: column;

      .content {
        margin-top: 0;
        color: $font-color-basic;
      }

      .cell {
        display: flex;
        justify-content: space-between;
        font-size: 32rpx;
        line-height: 48rpx;
      }

      .item {
        padding: 20rpx 0;
      }

      .label {
        @include utils-ellipsis;

        max-width: 64%;
        font-weight: bold;
      }

      .value {
        flex: none;
        padding-right: 30rpx;
        color: $font-color-label;
        font-size: 26rpx;
        background: url(#{$path}/arrow.png) no-repeat right center / 12rpx 22rpx;
      }

      // * 重置 .label .value 样式 start
      .flex-1 {
        @include utils-ellipsis;

        flex: 1 0 auto;
        max-width: 100%;
        box-sizing: border-box;
      }

      .max-width-75 {
        max-width: 75%;
      }

      .color-primary {
        color: $color-primary;
      }

      .font-color {
        color: $font-color;
      }

      .font-size-28 {
        font-size: 28rpx;
      }

      .font-size-30 {
        font-size: 30rpx;
      }

      .font-size-32 {
        font-size: 32rpx;
      }

      .font-weight-bold {
        font-weight: bold;
      }

      .font-weight-normal {
        font-weight: normal;
      }
      // * 重置 .label .value 样式 end

      .info {
        margin-top: 10rpx;
        line-height: 48rpx;

        .text {
          margin-left: 24rpx;
        }
      }

      .tags {
        display: flex;
        margin-top: 10rpx;

        .text {
          padding: 4rpx 16rpx;
          color: $color-primary;
          font-size: 26rpx;
          background-color: $color-primary-background;
          border-radius: 4rpx;

          & + .text {
            margin-left: 8rpx;
          }

          &.is-special {
            color: #486cf5;
            background-color: #f6f8ff;
          }

          &.is-practice {
            padding: 2rpx 14rpx;
            color: $color-primary;
            border: 2rpx solid $color-primary;
            background-color: transparent;
          }
        }
      }
    }
  }

  .preview-button {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 15rpx 27rpx;
    background-color: $color-white;

    .preview {
      display: block;
      padding: 15rpx - 1;
      color: $color-white;
      font-size: 16rpx;
      text-align: center;
      line-height: 1;
      background-color: $color-primary;
      border-radius: 22rpx;
    }
  }

  // todo next version
  // .resume-tabbar {
  //   position: fixed;
  //   left: 40rpx;
  //   right: 40rpx;
  //   bottom: 80rpx;
  //   display: flex;
  //   align-items: center;
  //   height: 96rpx;
  //   line-height: 1;
  //   background: $color-white;
  //   border-radius: 48rpx;
  //   box-shadow: 0 4rpx 10rpx 4rpx rgba($font-color, 0.1);

  //   .item {
  //     flex: 1;
  //     display: flex;
  //     flex-direction: column;
  //     justify-content: space-around;
  //     align-items: center;
  //     height: 100%;
  //     color: $font-color;
  //     font-size: 24rpx;
  //     text-align: center;

  //     &::before {
  //       content: '';
  //       width: 50rpx;
  //       height: 50rpx;
  //       background-repeat: no-repeat;
  //       background-position: center;
  //       background-size: contain;
  //     }

  //     $list: person, preview, modify, refresh;

  //     @each $var in $list {
  //       &.#{$var} {
  //         &::before {
  //           background-image: url(#{$path}/../tabbar/#{$var}.png);
  //         }
  //       }
  //     }
  //   }
  // }
}
