import { assetsURL, baseURL, defaultDuration } from '@/settings'
import { handleUploadMessageFile } from '@/utils/upload'
import {
  delAttachment,
  getAttachmentData,
  getAttachmentList,
  renameAttachment,
  createDownloadAttachmentToken
} from './api/attachment'
import ActionSheet, { ActionSheetTheme } from 'tdesign-miniprogram/action-sheet'
import { showLoading, showToast } from '@/utils/util'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    emptyImage: `${assetsURL}/resume/attachment/empty.png`,

    content: '',

    url: '/resume/upload',
    count: 0,
    extension: ['doc', 'docx', 'pdf'],

    size: 0,
    limit: 0,
    message: '',

    enable: false,

    attachmentList: [],

    deleteDialogVisible: false,

    renameDialogVisible: false,

    currentData: <any>{},

    renameValue: ''
  },

  handleBack() {
    wx.navigateBack()
  },

  handleRefresh() {
    this.setData({ enable: true })
    this.fetchData(() => {
      this.setData({ enable: false })
    })
  },

  handleAction(event: WechatMiniprogram.CustomEvent) {
    const { data } = event.currentTarget.dataset
    this.setData({ currentData: data })

    ActionSheet.show({
      description: '',
      align: 'center',
      theme: ActionSheetTheme.List,
      selector: '#action',
      context: this,
      items: [{ label: '文件重命名' }, { label: '删除附件' }]
    })
  },

  async handleOpen(event: WechatMiniprogram.CustomEvent) {
    const { token } = event.currentTarget.dataset

    // 去换临时token
    const data = await createDownloadAttachmentToken({ token })

    showLoading()
    const url = baseURL + '/resume/download-attachment?token=' + data.token

    wx.downloadFile({
      // 示例 url，并非真实存在
      url,
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          filePath: filePath,
          fileType: data.fileType,
          success: function () {
            wx.hideLoading()
          },
          fail: function (res) {
            // 弹出错误提示
            showToast(res.errMsg)
            wx.hideLoading()
          }
        })
      },
      fail: function () {
        wx.hideLoading()
      }
    })
  },

  handleSelected(event: WechatMiniprogram.CustomEvent) {
    const { index } = event.detail
    const { currentData } = this.data
    const renameCallback = () => {
      this.setData({ renameDialogVisible: true, renameValue: currentData.trueName })
    }
    const deleteCallback = () => {
      this.setData({ deleteDialogVisible: true })
    }
    const action = [renameCallback, deleteCallback]

    action[index]()
  },

  handleRenameChange(event: WechatMiniprogram.CustomEvent) {
    const { value = '' } = event.detail

    this.setData({ renameValue: value })
  },

  handleRenameClear(event: WechatMiniprogram.CustomEvent) {
    this.handleRenameChange(event)
  },

  async handleDeleteConfirm() {
    const { token } = this.data.currentData

    await delAttachment({ token })
    this.handleDialogCancel()
    this.fetchData()
  },

  async handleRenameConfirm() {
    const { currentData, renameValue } = this.data
    const { token } = currentData
    const name = renameValue.trim()

    if (name.length === 0) {
      wx.showToast({ title: '请输入附件名称', icon: 'none', duration: defaultDuration })
      return
    }

    await renameAttachment({ token, name })
    this.handleDialogCancel()
    this.fetchData()
  },

  handleDialogCancel() {
    this.setData({ renameDialogVisible: false, deleteDialogVisible: false })
  },

  handleUpload() {
    const { attachmentList, message, limit, url, extension } = this.data

    if (attachmentList.length < limit) {
      handleUploadMessageFile({ url, type: 'file', count: 1, extension }, this.fetchData, this.fetchData)
    } else {
      wx.showToast({ title: message, icon: 'none', duration: defaultDuration })
    }
  },

  async fetchData(callback = () => {}) {
    const { size, errorMsg, limitAmount } = await getAttachmentData()
    const list = await getAttachmentList()
    const count = limitAmount - list.length
    const content = `限doc/docx/pdf格式附件，大小${size}M以内，最多${limitAmount}份`

    this.setData({ content, count, size, limit: limitAmount, message: errorMsg, attachmentList: list }, callback)
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
