import request from '@/utils/request'
import { getIntentionId } from '@/utils/store'

export function saveTempIntention(data: object) {
  return request({
    url: '/resume-intention/save-temp-intention',
    method: 'POST',
    data
  })
}

export function getTempIntention(data: object) {
  return request({
    url: '/resume-intention/get-temp-intention',
    method: 'POST',
    data
  })
}

export function getCurrentStep() {
  return request({
    url: '/resume/get-step-num'
  })
}

export function getBasicData() {
  return request({
    url: '/resume/get-user-base-info'
  })
}

export function setBasicData(data: object) {
  return request({
    url: '/resume/save-user-base-info',
    method: 'POST',
    data
  })
}

export function getEducationData() {
  const data = { intentionId: getIntentionId() }

  return request({
    url: '/resume/get-step-two-info',
    data
  })
}

export function setEducationData(data: object) {
  return request({
    url: '/resume/save-step-two-info',
    method: 'POST',
    data
  })
}

export function getIntentionData() {
  const data = { intentionId: getIntentionId() }

  return request({
    url: '/resume/get-step-three-info',
    data
  })
}

export function setIntentionData(data: object) {
  return request({
    url: '/resume/save-step-three-info',
    method: 'POST',
    data
  })
}
