import request from '@/utils/request'

export function getAttachmentData() {
  return request({ url: '/resume/get-resume-attachment-info' })
}

export function getAttachmentList() {
  return request({ url: '/resume/get-resume-attachment-list' })
}

export function delAttachment(data: object) {
  return request({ url: '/resume/del-attachment', method: 'POST', data })
}

export function renameAttachment(data: object) {
  return request({ url: '/resume/rename-attachment', method: 'POST', data })
}

export function createDownloadAttachmentToken(data: object) {
  return request({ url: '/resume/create-download-attachment-token', method: 'POST', data })
}
