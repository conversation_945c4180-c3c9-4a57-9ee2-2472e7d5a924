import request from '@/utils/request'

export function getResumeIntentionList() {
  return request({
    url: '/resume-intention/index'
  })
}

export function updatePostWork(data: object) {
  return request({
    url: '/resume/update-post-work',
    method: 'POST',
    data
  })
}

export function getIntentionEditInfo(data: object) {
  return request({
    url: '/resume-intention/get-edit-info',
    data
  })
}

export function editIntention(data: object) {
  return request({
    url: '/resume-intention/edit',
    method: 'POST',
    data
  })
}

export function deleteIntention(data: object) {
  return request({
    url: '/resume-intention/delete',
    method: 'POST',
    data
  })
}

export function addIntention(data: object) {
  return request({
    url: '/resume-intention/add',
    method: 'POST',
    data
  })
}

export function editWorkStatus(data: object) {
  return request({
    url: '/resume/edit-work-status',
    method: 'POST',
    data
  })
}

export function editArriveDateType(data: object) {
  return request({
    url: '/resume/edit-arrive-date-type',
    method: 'POST',
    data
  })
}
