import request from '@/utils/request'

export function getResumeData() {
  return request({ url: '/resume/get-edit-info' })
}

export function getAdvantageData() {
  return request({ url: '/resume/get-advantage-info' })
}

export function setAdvantageData(data: object) {
  return request({ url: '/resume/edit-advantage-info', method: 'POST', data })
}

export function getResearchData() {
  return request({ url: '/resume-research-direction/get-edit-info' })
}

export function addResearchData(data: object) {
  return request({ url: '/resume-research-direction/add', method: 'POST', data })
}

export function setResearchData(data: object) {
  return request({ url: '/resume-research-direction/edit', method: 'POST', data })
}
