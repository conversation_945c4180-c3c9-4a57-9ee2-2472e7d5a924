import { showLoading, showToast } from '@/utils/util'
import { getAllRemindCount, getApplyOffSiteList, getJobApplyList, jobApplyCheck } from './api/delivery'
import { defaultDuration, h5 } from '@/settings'
import { getVipInfo } from '@/api/person'

// packages/delivery/index.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    showHintBar: false,
    hintBarUrl: '',

    visible: false,
    isEnded: false,
    status: '',
    site: '1',
    title: '站内投递',
    content: '',
    tips: '',
    page: 1,
    list: <any>[],
    jobApplyAllCount: 0,
    jobApplyWaitCheckCount: 0,
    jobApplyCheckCount: 0,
    jobApplyPassCount: 0,
    jobApplyInviteCount: 0,
    jobApplyNoPassCount: 0,
    jobApplyEmployedCount: 0,
    interviewInfo: <any>{}
  },

  async getList(replace = true) {
    showLoading()

    const { site, page, status, isEnded } = this.data
    const isOffSite = site === '1' ? true : false

    const postData = <any>{ page }
    if (isOffSite) {
      postData.status = status
    } else {
      postData.applyStatus = status
    }

    if (isEnded && !replace) {
      wx.hideLoading()
      return
    }

    const api = isOffSite ? getJobApplyList : getApplyOffSiteList
    const { list } = await api(postData)

    this.setData({ list: replace ? list : this.data.list.concat(list), isEnded: list.length < 10 ? true : false })

    if (!replace) {
      wx.hideLoading()
      return
    }

    this.getRemindList()

    wx.hideLoading()
  },

  async getRemindList() {
    const {
      jobApplyAllCount,
      jobApplyWaitCheckCount,
      jobApplyCheckCount,
      jobApplyPassCount,
      jobApplyInviteCount,
      jobApplyNoPassCount,
      jobApplyEmployedCount
    } = await getAllRemindCount()

    this.setData({
      jobApplyAllCount,
      jobApplyWaitCheckCount,
      jobApplyCheckCount,
      jobApplyPassCount,
      jobApplyInviteCount,
      jobApplyNoPassCount,
      jobApplyEmployedCount
    })
  },

  async fetchVipInfo() {
    const { isShowJobFastBuy, buyUrl } = await getVipInfo()

    this.setData({
      showHintBar: isShowJobFastBuy,
      hintBarUrl: `${h5}${buyUrl.jobFast}`
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  handleBack() {
    wx.navigateBack()
  },

  handleTabChange(event: WechatMiniprogram.CustomEvent) {
    const { type } = event.currentTarget.dataset
    this.setData({ site: type, status: '', page: 1 })
    this.getList()
  },

  handleQuestion() {
    const { site } = this.data
    this.setData({
      visible: true,
      title: site === '1' ? '站内投递' : '站外投递',
      content:
        site === '1'
          ? '站内投递：职位详情中，“报名方式”为“站内投递”的岗位。'
          : '站外投递：①职位“投递方式”为 电子邮件/网上系统/现场报名/邮寄/电话/传真/微信/其他方式的投递；',
      tips: site === '1' ? '' : '②由求职者自定义添加的投递；'
    })
  },

  onVisibleChange(e: any) {
    this.setData({ visible: e.detail.visible })
  },

  async handleJobDetail(event: WechatMiniprogram.CustomEvent) {
    const { site, list } = this.data
    const {
      currentTarget: {
        dataset: { info, index }
      }
    } = event
    const { isCheckRemind, id, jobId } = info

    if (info.jobStatus == 9) {
      showToast({ icon: 'none', title: '职位已删除', duration: defaultDuration })
      return
    }

    if (isCheckRemind === '2' && site === '1') {
      await jobApplyCheck({ id })

      list[index].isCheckRemind = '1'

      this.getRemindList()

      this.setData({ list })
    }
    wx.navigateTo({ url: `/packages/job/detail/index?id=${jobId}` })
  },

  handleCheck(event: WechatMiniprogram.CustomEvent) {
    const {
      currentTarget: {
        dataset: { status }
      }
    } = event
    this.setData({ status, page: 1 })
    this.getList()
  },

  handleGoHome() {
    wx.switchTab({ url: '/pages/home/<USER>' })
  },

  handleView(evevt: WechatMiniprogram.CustomEvent) {
    const { status, interviewInfo } = evevt.currentTarget.dataset.info

    if (status === '3') {
      this.setData({
        visible: true,
        title: '面试信息',
        interviewInfo,
        content: '',
        tips: ''
      })
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.getList()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.fetchVipInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    const { page } = this.data
    this.setData({ page: page + 1 })
    this.getList(false)
  }

  /**
   * 用户点击右上角分享
   */
})
