import request from '@/utils/request'

export function getList(data: object) {
  return request({ url: '/job-invite/list', data })
}

export function adminCheck(id: number | string) {
  return request({ url: '/job-invite/admin-check', data: { id }, method: 'POST' })
}

export function companyCheck(id: number | string) {
  return request({ url: '/job-invite/company-check', data: { id }, method: 'POST' })
}
