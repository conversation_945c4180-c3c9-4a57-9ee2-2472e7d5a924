import request from '@/utils/request'

export function getJobSubscribeInfo() {
  return request({ url: '/job-subscribe/get-info' })
}

export function getJobCategoryList() {
  return request({ url: '/config/get-all-category-job-list' })
}

export function getAreaList() {
  return request({ url: '/config/get-hierarchy-city-list' })
}

export function getEducationList() {
  return request({ url: '/config/get-education-list' })
}

export function submit(data: object) {
  return request({
    url: '/job-subscribe/save',
    method: 'POST',
    data
  })
}

export function cancel() {
  return request({
    url: '/job-subscribe/cancel',
    method: 'POST'
  })
}
