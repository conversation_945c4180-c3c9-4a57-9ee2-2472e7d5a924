import { assetsURL, defaultDuration, h5 } from '@/settings'
import { collect as jobCollect } from '@/api/jobDetail'
import { collect as announcementCollect } from '@/api/announcementDetail'
import { collect as companyCollect } from '@/api/company'
import { getAnnouncementList, getCompanyList, getJobList } from './api/collect'
import { getVipInfo } from '@/api/person'
import { toWebPage } from '@/utils/util'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    tabValue: 'job',
    tabOption: [
      { label: '职位', value: 'job', route: '/pages/home/<USER>' },
      { label: '公告', value: 'announcement', route: '/pages/announcement/index' },
      { label: '单位', value: 'company', route: '/pages/announcement/index?tabType=2' }
    ],

    showVipHint: false,
    vipPageUrl: '',

    viewportHeight: 0,

    scrollTop: 0,

    page: 1,

    refreshTrigger: false,

    showBackTop: false,

    hasMoreData: true,

    resultList: <any>[],

    emptyImage: `${assetsURL}/person/collect/empty.png`,
    emptyButtonText: '查看更多职位'
  },

  handleBack() {
    wx.navigateBack()
  },

  onTabsChange(event: WechatMiniprogram.CustomEvent) {
    const { value } = event.detail
    const { tabOption } = this.data

    const currentTab = tabOption.find((item) => item.value === value)
    const emptyButtonText = `查看更多${currentTab?.label}`

    this.setData({ tabValue: value, resultList: [], page: 1, showBackTop: false, hasMoreData: true, emptyButtonText })

    wx.nextTick(() => {
      this.fetchData()
    })
  },

  handleToVip() {
    toWebPage(this.data.vipPageUrl)
  },

  async handleClick(event: WechatMiniprogram.CustomEvent) {
    const { index, item } = event.currentTarget.dataset
    const { tabValue, resultList } = this.data
    const fetchOption = {
      job: { handler: jobCollect, params: item.jobId },
      announcement: { handler: announcementCollect, params: item.id },
      company: { handler: companyCollect, params: item }
    }
    const { handler, params } = fetchOption[tabValue as keyof typeof fetchOption]

    await handler(params as never)

    wx.showToast({
      title: `取消${tabValue === 'company' ? '关注' : '收藏'}成功`,
      icon: 'none',
      duration: defaultDuration
    })

    resultList.splice(index, 1)

    this.setData({ resultList })
  },

  handleView() {
    const { tabValue, tabOption } = this.data
    const currentTab = tabOption.find((item) => item.value === tabValue)

    wx.reLaunch({ url: <string>currentTab?.route })
  },

  handleRefresh() {
    this.setData({ page: 1 }, () => {
      this.fetchData()
      this.setData({ refreshTrigger: false })
    })
  },

  handleLower() {
    const { page, hasMoreData } = this.data

    if (hasMoreData === false) return

    this.fetchData(page + 1)
  },

  handleScroll(event: WechatMiniprogram.CustomEvent) {
    const { scrollTop } = event.detail
    const { viewportHeight } = this.data

    this.setData({ showBackTop: scrollTop > viewportHeight })
  },

  handleBackTop() {
    this.setData({ scrollTop: 0, showBackTop: false })
  },

  getSystemInfoSync() {
    const { windowHeight: viewportHeight } = wx.getSystemInfoSync()

    this.setData({ viewportHeight })
  },

  async fetchVipInfo() {
    const { isVip, buyUrl } = await getVipInfo()

    this.setData({ showVipHint: isVip !== '1', vipPageUrl: `${h5}${buyUrl.vip}` })
  },

  async fetchData(page: number = 1) {
    let { resultList, hasMoreData } = this.data
    const { tabValue } = this.data
    const fetchList = { job: getJobList, announcement: getAnnouncementList, company: getCompanyList }

    const handler = fetchList[tabValue as keyof typeof fetchList]
    const append = page > 1
    const { list, page: pageData } = await handler({ page })

    if (append) {
      resultList.push(...list)
    } else {
      resultList = list
    }

    hasMoreData = list.length === pageData.limit

    this.setData({ resultList, page: pageData.page, hasMoreData })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getSystemInfoSync()
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.fetchVipInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
