import { showToast, setData, validateForm } from '@/utils/util'
import { validMobile } from '@/utils/validate'
import { checkMobileCodeLogin, getSMSCode, mobileCodeLogin } from '@/api/entry'
import { authSuccessCallback, wxLogin } from '@/utils/auth'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isAgree: false,
    countdownTime: 60,
    countdownDisabled: false,
    sendCodeText: '获取验证码',
    loading: false,
    loginDisabled: true,
    fromUrl: '',
    rules: {
      mobile: [
        { required: true, message: '请输入您的手机号' },
        {
          validator: (value: any, callback: any) => {
            if (value.length !== 11) {
              callback('请输入正确格式的手机号')
            }
          }
        }
      ],
      code: [{ required: true, message: '请输入验证码' }]
    },

    formData: {
      mobile: '',
      code: '',
      ticket: '',
      randstr: ''
    }
  },

  handleGetCode() {
    const {
      formData: { mobile }
    } = this.data
    if (mobile === '') {
      showToast('请输入您的手机号')
      return
    }
    if (!validMobile(mobile)) {
      showToast('请输入正确格式的手机号')
      return
    }
    this.selectComponent('#captcha').show()
  },

  handleClear(e: any) {
    const {
      currentTarget: {
        dataset: { key: k }
      }
    } = e
    this.setData({
      [k]: ''
    })
  },

  handleSetData(e: any) {
    setData(e, this)
    this.handleLoginDisabled()
  },

  handleLoginDisabled() {
    const { mobile, code } = this.data.formData
    this.setData({
      loginDisabled: !(mobile && code)
    })
  },

  handleCodeLogin() {
    showToast('请勾选同意后再登录')
  },

  loginSuccess(data: object) {
    authSuccessCallback(data, () => {
      wx.navigateBack()
    })
  },

  async getPhoneNumber(event: WechatMiniprogram.ButtonGetPhoneNumber) {
    const { /* iv, encryptedData, */ code: mobileCode } = event.detail

    if (!mobileCode) return

    const unionCode = await wxLogin()

    const data = await mobileCodeLogin({ unionCode, mobileCode })

    this.loginSuccess(data)
  },

  async handleSubmit() {
    const { formData, rules, isAgree } = this.data
    const valid = validateForm(formData, rules)

    if (valid) {
      if (!isAgree) {
        showToast('请勾选同意后再登录')
        return
      }

      this.setData({ loading: true })

      try {
        const { mobile, code } = formData
        const unionCode = await wxLogin()
        const data = await checkMobileCodeLogin({ unionCode, code, mobile })

        this.loginSuccess(data)
      } finally {
        this.setData({ loading: false })
      }
    }
  },

  // 验证码验证结果回调
  async handlerVerify(ev: WechatMiniprogram.CustomEvent) {
    // 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
    const { ret, ticket, randstr } = ev.detail

    if (ret !== 0) return

    // 验证成功
    let countdown: number
    const { mobile } = this.data.formData

    try {
      await getSMSCode({ mobile, ticket, randstr })
      this.setData({ countdownDisabled: true })

      countdown = setInterval(() => {
        const { countdownTime } = this.data

        if (countdownTime == 1) {
          clearInterval(countdown)
          this.setData({
            countdownTime: 60,
            sendCodeText: '重新发送',
            countdownDisabled: false
          })
          return
        }

        this.setData({
          countdownTime: countdownTime - 1
        })
      }, 1000)
    } catch {
      // 验证失败
      // 请不要在验证失败中调用refresh，验证码内部会进行相应处理
    }
  },
  // 验证码准备就绪
  handlerReady() {
    // console.log('验证码准备就绪')
  },
  // 验证码弹框准备关闭
  handlerClose(ev: WechatMiniprogram.CustomEvent) {
    // 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
    const { ret } = ev.detail

    if (ret === 2) {
      // console.log('点击了关闭按钮，验证码弹框准备关闭')
    } else {
      // console.log('验证完成，验证码弹框准备关闭')
    }
  },
  // 验证码出错
  handlerError() {
    // console.log(ev.detail.errMsg)
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const pages = getCurrentPages()
    const lastRouter = pages[pages.length - 2]
    if (lastRouter) {
      this.setData({
        fromUrl: lastRouter.route
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
