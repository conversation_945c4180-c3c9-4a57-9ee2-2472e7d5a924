@use 'styles/variables' as *;

.exposure-popup {
  .popup {
    max-height: 82vh;
    display: flex;
  }

  .popup-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
  }

  .header {
    text-align: center;
    padding: 35rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    border-bottom: 2rpx solid $border-color;
  }

  .exposure-content {
    padding: 30rpx 30rpx 50rpx;
    flex-grow: 1;
    overflow-y: scroll;

    .data {
      display: flex;
      justify-content: space-between;
      background-color: #fff8f0;
      line-height: 66rpx;
      padding: 0 20rpx;
      border-radius: 10rpx;

      .date {
        flex-grow: 1;
      }

      .amount {
        display: flex;

        .item {
          display: flex;
          align-items: center;
          margin-left: 40rpx;

          &::before {
            content: '';
            width: 12rpx;
            height: 12rpx;
            background: #486cf5;
            border-radius: 50%;
            margin-right: 7rpx;
          }

          &:first-child {
            &::before {
              background-color: $color-primary;
            }
          }
        }
      }
    }

    .chart-content {
      height: 400rpx;
    }
  }

  .disabled {
    opacity: 0.4;
  }

  .exposure-bottom {
    .btn {
      display: block;
      width: 340rpx;
      height: 80rpx;
      background: $color-primary;
      border-radius: 40rpx;
      margin: 0 auto 60rpx;
      background-color: $color-primary;
      color: #fff;
      font-size: 30rpx;
      font-weight: bold;
      text-align: center;
      line-height: 80rpx;
      position: relative;

      .tips {
        position: absolute;
        top: -21rpx;
        right: -84rpx;
        width: 176rpx;
        height: 54rpx;
        line-height: 54rpx;
        font-size: 22rpx;
        background: url(#{$assets}/resume/exposure.png) no-repeat left/100% 100%;
      }
    }
  }
}
