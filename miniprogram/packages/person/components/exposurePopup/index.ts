import { checkLogin } from '@/utils/store'
import { h5 } from '@/settings'
import { toWebPage } from '@/utils/util'
import { getVipInfo } from '@/api/person'

import { getVipPvChart } from '../../api/resume'

import { exposureLineChart } from '../../utils/chart'

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  observers: {
    visible(value) {
      const { isInit } = this.data
      if (value && !isInit) {
        this.init()
      }
    }
  },

  /**
   * 页面的初始数据
   */

  options: {
    styleIsolation: 'shared'
  },

  data: {
    isShowJobFastBuy: true,
    isInit: false,
    echartsEl: <any>null,
    my: '',
    avg: '',
    date: '',
    xAxisData: [],
    dateArray: [],
    data: [[], []],
    ec: {
      lazyLoad: true
    },
    openUrl: ''
  },

  methods: {
    async init() {
      await this.handleCallback(async () => {
        await this.getPV()
        this.fetchVipInfo()
      })
    },

    handleCallback(callback = () => {}) {
      const isLogin = checkLogin()

      if (isLogin) {
        callback()
      }
    },

    async getPV() {
      const {
        exposureData,
        avgData,
        lastData: { lastAvgData, lastExposureData, xInit },
        xDate,
        xInit: dateArray
      } = await getVipPvChart()

      this.setData(
        {
          xAxisData: xDate,
          data: [exposureData, avgData],
          my: lastExposureData,
          avg: lastAvgData,
          date: xInit,
          dateArray,
          echartsEl: this.selectComponent('.exposure-line')
        },
        () => {
          this.data.echartsEl.init()
        }
      )
    },

    async fetchVipInfo() {
      const {
        isShowJobFastBuy,
        buyUrl: { jobFast }
      } = await getVipInfo()

      this.setData({ isShowJobFastBuy, openUrl: `${h5}${jobFast}` })
    },

    chartInit(e: WechatMiniprogram.CustomEvent) {
      const {
        detail: { canvas, width, height, dpr }
      } = e
      const { xAxisData, data } = this.data
      const option = {
        canvas,
        width,
        height,
        canvasDpr: dpr,
        xAxisData,
        data
      }
      const chart = exposureLineChart(option, (res: any) => {
        const {
          index,
          data: [my, avg = '-']
        } = res
        const { dateArray } = this.data
        this.setData({ my, avg, date: dateArray[index] })
      })
      this.setData({
        isInit: true
      })
      this.data.echartsEl.chart = chart
    },

    handleOpen() {
      this.setData({ visible: false })
      const { openUrl } = this.data
      toWebPage(openUrl)
    },

    onVisibleChange(e: any) {
      this.setData({ visible: e.detail.visible })
    }
  }
})
