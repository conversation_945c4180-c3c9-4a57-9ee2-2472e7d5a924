<view class="exposure-popup">
  <t-popup t-class="popup" t-class-content="popup-content" visible="{{ visible }}" placement="bottom" bind:visible-change="onVisibleChange" close-btn>
    <view class="header">简历曝光度统计</view>

    <view class="exposure-content">
      <view class="data">
        <view class="date">{{ date }}</view>
        <view class="amount">
          <view class="item">我:{{ my }}</view>
          <view class="item">行业平均:{{ avg }}</view>
        </view>
      </view>
      <view class="chart-content">
        <ec-canvas class="exposure-line" canvas-id="exposure-line" ec="{{ ec }}" bind:init="chartInit"></ec-canvas>
      </view>
    </view>

    <view class="exposure-bottom" wx:if="{{ isShowJobFastBuy }}">
      <view bind:tap="handleOpen" class="btn">
        立即置顶简历
        <view class="tips">曝光度提升6倍</view>
      </view>
    </view>
  </t-popup>
</view>
