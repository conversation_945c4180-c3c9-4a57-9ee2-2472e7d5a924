import { toWebPage } from '@/utils/util'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    url: {
      type: String,
      value: ''
    },

    title: {
      type: String,
      value: '想加快求职进度？'
    },

    buttonText: {
      type: String,
      value: '去置顶投递'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    catchTap() {
      const { url } = this.properties

      toWebPage(url)
    }
  }
})
