@use 'styles/variables.scss' as *;

.hint-bar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 26rpx;
  font-weight: bold;
  background-color: #f0f2fd;

  .title {
    @include utils-ellipsis;

    padding-left: 50rpx;
    color: #041361;
    background: url(#{$assets}/person/components/hint-bar/title.png) no-repeat left center / 40rpx;
  }

  .button {
    flex: none;
    padding: 0 20rpx;
    color: #5f78f2;
    line-height: 48rpx;
    background-color: #cfd7ff;
    border-radius: 24rpx;
  }
}
