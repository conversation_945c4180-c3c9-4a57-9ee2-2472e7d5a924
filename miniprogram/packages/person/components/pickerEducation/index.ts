import { getEducationList } from '../../api/subscribe'

Component({
  options: {
    styleIsolation: 'shared'
  },

  /**
   * 组件的属性列表
   */
  properties: {
    model: { type: String, value: '' },
    modelText: { type: String, value: '' },
    visible: { type: Boolean, value: true }
  },

  /**
   * 组件的初始数据
   */
  data: {
    label: '',
    value: [],

    keys: {
      label: 'v',
      value: 'k'
    },
    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  observers: {
    model(value: string) {
      const val = <never[]>[value]

      this.setData({ value: val })
    },

    modelText(label: string) {
      this.setData({ label })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const data = <never[]>await getEducationList()

      this.setData({ options: data })
    },

    handlePickerChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.setData({ label: label.join(), value })
      this.triggerEvent('change', { value: value.join(), label: label.join() })
    }
  }
})
