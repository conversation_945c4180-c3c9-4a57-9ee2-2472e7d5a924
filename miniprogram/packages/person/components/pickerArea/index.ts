import { getAreaList } from '../../api/subscribe'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /** 单选 string；多选 string[] */
    model: { type: null, value: '' },

    /** 单选 string；多选 string[] */
    label: { type: null, value: '' },

    title: { type: String, value: '请选择地区' },

    limit: { type: Number, value: 5 },
    visible: { type: Boolean, value: false }
  },

  /**
   * 组件的初始数据
   */
  data: {
    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const options = <never>await getAreaList()

      this.setData({ options })
    },
    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.setData({ label })
      this.triggerEvent('change', { label, value })
    }
  }
})
