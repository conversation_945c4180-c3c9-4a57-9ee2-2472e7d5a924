import { getList, companyCheck, adminCheck } from './api/invite'
import { getCheckSubscribe } from '@/api/person'
import beforeDeliveryMixin from '@/mixins/beforeDeliveryMixin'

import { showLoading } from '@/utils/util'
Page({
  /**
   * 页面的初始数据
   */

  behaviors: [beforeDeliveryMixin],

  deliveryPopup: <any>{},

  data: {
    jobId: '',
    visible: false,
    visibleEmpty: false,
    buttonDisabled: false,
    isShowTips: false,
    jobIndex: 0,
    page: 1,
    list: <any>[],
    isEnd: false,
    isSubscribe: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.deliveryPopup = this.selectComponent('#deliveryPopup')
    this.getList()
  },

  async checkBind() {
    const { isSubscribe } = await getCheckSubscribe()
    this.setData({ isSubscribe: isSubscribe === 1 ? true : false })
    if (isSubscribe == 1) {
      this.closeBindTag()
    } else {
      this.showSubTips()
    }
  },

  handleCheckStatus(applyStatus: number) {
    const { list, jobIndex } = this.data
    const res = list.map((item: any, index: number) => {
      if (index === jobIndex) {
        item.applyStatus = applyStatus
      }
      return item
    })

    this.setData({ list: res })
  },

  showSubTips() {
    const { isShowSubscribeTips } = getApp().globalData

    const { isSubscribe } = this.data

    if (isShowSubscribeTips && !isSubscribe) {
      this.setData({ isShowTips: true })
    }
  },

  async getList() {
    const { isEnd, page } = this.data
    if (isEnd) return
    showLoading()

    const { list } = await getList({ page })

    this.setData({ list: this.data.list.concat(list), page: page + 1 })

    wx.hideLoading()
    if (list.length == 0 && this.data.list.length === 0) {
      this.setData({ visibleEmpty: true })
    }

    if (list.length < 10) {
      this.setData({ isEnd: true })
    }
  },

  handleOpen() {
    this.setData({
      visible: true
    })
  },

  clickCard(event: WechatMiniprogram.CustomEvent) {
    // 根据邀约类型来
    const {
      item: { id, sourceType, isRemindCheck, jobId },
      index
    } = event.currentTarget.dataset

    const { list } = this.data

    if (sourceType === '2' && isRemindCheck == '2') {
      // 平台
      adminCheck(id)
    }
    if (sourceType === '1' && isRemindCheck == '2') {
      // 单位
      companyCheck(id)
    }

    list[index].isRemindCheck = '1'
    this.setData({ list })

    wx.navigateTo({ url: `/packages/job/detail/index?id=${jobId}` })
  },

  closeBindTag() {
    this.setData({ isShowTips: false })
    getApp().updateSubscribeTipsStatus(false)
  },

  openDelivery(event: WechatMiniprogram.CustomEvent) {
    const { index } = event.currentTarget.dataset

    this.setData({ jobId: event.currentTarget.dataset.id, jobIndex: index })

    this.handleCheckJobInfo(event.currentTarget.dataset.id)
  },

  openDeliveryPopup(data: any) {
    const { jobId } = this.data

    this.deliveryPopup.open({ ...data, jobId })
  },

  handleChange(event: WechatMiniprogram.CustomEvent) {
    const { applyStatus } = event.detail
    this.handleCheckStatus(applyStatus)
  },

  toHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkBind()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.getList()
  }

  /**
   * 用户点击右上角分享
   */
})
