<view class="invite">
  <view class="fix">
    <t-navbar title="职位邀约" left-arrow />
    <view class="notify" wx:if="{{ isShowTips }}">
      <view class="text">开启消息通知，及时接收重要消息。</view>
      <view class="open" bindtap="handleOpen">去开启</view>
      <view class="close" bindtap="closeBindTag"></view>
    </view>
  </view>

  <view class="invite-list">
    <view class="invite-detail" wx:for="{{ list }}" wx:key="key" wx:for-item="item" bind:tap="clickCard" data-item="{{ item }}" data-index="{{ index }}">
      <view wx:if="{{ item.sourceType === '1' }}" class="invite-origin company">单位邀约</view>
      <view wx:else class="invite-origin platform">平台推荐</view>
      <view class="company-card">
        <view class="logo-box {{ item.packageType === '2' ? 'vip' : '' }}">
          <image class="company-logo" src="{{ item.logoUrl }}" />
        </view>

        <view class="company-head">
          <view class="company-name">{{ item.fullName }}</view>
          <view class="date">{{ item.addTime }}</view>
        </view>
      </view>

      <view class="copy-writing">{{ item.textContent }}</view>

      <view class="job-card">
        <view class="job-basic {{ item.isRemindCheck !== '1' ? 'unread' : '' }}">
          <view class="title">
            {{ item.jobList.jobName }}
            <view class="tag">
              <view class="worry" wx:if="{{ item.isTop }}">急</view>
              <view class="organization" wx:if="{{ item.isEstablishment }}">编</view>
              <view class="fast" wx:if="{{ item.isFast }}"></view>
            </view>
          </view>
          <view class="salary">{{ item.jobList.wage }}</view>
        </view>

        <view class="announcement" wx:if="{{ item.jobList.announcementTitle }}">{{ item.jobList.announcementTitle }} </view>

        <view class="label-content">
          <view class="label" wx:if="{{ item.jobList.area }}">{{ item.jobList.area }}</view>
          <view class="label" wx:if="{{ item.jobList.education }}">{{ item.jobList.education }}</view>
          <view class="label">招{{ item.jobList.amount }}人</view>
        </view>
      </view>
      <view class="view-status">
        <t-button theme="primary" size="small" bind:tap="accept" data-item="item" wx:if="{{ item.applyStatus === 2 && item.status === 1 }}" data-id="{{ item.jobList.jobId }}" data-index="{{ index }}" bind:tap="openDelivery">立即投递</t-button>
        <!-- item.jobList.status!=1 已下线 applyStatus==1 已投递-->
        <t-button disabled theme="primary" size="small" data-item="item" wx:if="{{ item.status === 1 && item.applyStatus === 1 }}">已投递</t-button>
        <t-button theme="light" size="small" data-item="item" wx:if="{{ item.status === 0 }}">已下线</t-button>
      </view>
    </view>
  </view>

  <view class="empty" wx:if="{{ visibleEmpty }}">
    <image class="empty-img" src="//img.gaoxiaojob.com/uploads/mini/empty/view-empty.png" />
    <view>暂无数据</view>
    <view>不如投递简历主动出击？</view>

    <t-button class="to" theme="primary" size="small" bindtap="toHome">去投递</t-button>
  </view>
</view>

<qrcode-popup visible="{{ visible }}" />

<delivery-popup id="deliveryPopup" bindclose="handleChange" />
<t-dialog visible="{{ beforeApplyDialogVisible }}" title="{{ beforeApplyDialogTitle }}" content="{{ beforeApplyDialogContent }}" confirm-btn="{{ beforeApplyDialogConfirmBtn }}" cancel-btn="{{ beforeApplyDialogCancelBtn }}" bind:confirm="handleCheckDialogConfirm" bind:cancel="handleCheckDialogCancel" />
