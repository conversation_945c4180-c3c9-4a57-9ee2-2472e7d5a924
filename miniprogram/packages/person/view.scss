@use 'styles/variables' as *;

page {
  background-color: $color-background;
}

.view {
  position: relative;

  .fix {
    position: sticky;
    top: 0;
    z-index: 2;
  }

  .notify {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    padding: 18rpx 30rpx;
    background-color: $tag-primary-background;

    .open {
      flex: 1;
      color: $color-primary;
      padding-right: 22rpx;
      background: url(#{$assets}/icon/into.png) no-repeat center left 81rpx / 10rpx 18rpx;
    }

    .close {
      width: 28rpx;
      height: 28rpx;
      background: url(#{$assets}/icon/delete-round.png) no-repeat center / contain;
    }
  }

  .view-list {
    padding: 20rpx 30rpx;

    .company-container {
      background-color: $color-white;
      padding: 40rpx 30rpx 30rpx;
      border-radius: $border-radius;
      margin-bottom: 20rpx;

      .company-card {
        position: relative;
        display: flex;
        align-items: center;
      }

      .company-logo {
        width: 78rpx;
        height: 78rpx;
        margin-right: 23rpx;
      }

      .vip {
        &::after {
          content: '';
          position: absolute;
          top: 55rpx;
          left: 55rpx;
          width: 28rpx;
          height: 28rpx;
          background: url(#{$assets}/icon/certification.png) no-repeat center / 28rpx;
        }
      }

      .unread {
        &::before {
          content: '';
          position: absolute;
          background-color: $color-point;
          top: 0;
          left: 72rpx;
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
        }
      }

      .company-info {
        line-height: 1;
        flex: 1;

        .company-head {
          display: flex;
          justify-content: space-between;
        }

        .company-name {
          flex: 1;
          max-width: 330rpx;
          font-size: 34rpx;
          font-weight: bold;
          @include utils-ellipsis;
        }

        .date {
          color: $font-color-tips;
          font-size: 24rpx;
        }

        .company-message {
          max-width: 480rpx;
          font-size: 24rpx;
          color: $font-color-label;
          margin-top: 20rpx;
          @include utils-ellipsis;
        }
      }

      .company-job {
        padding: 25rpx 20rpx;
        border-radius: $border-radius;
        background-color: $tag-info-background;
        margin: 20rpx 0;

        .title {
          display: flex;
        }

        .name {
          line-height: 48rpx;
          font-size: 32rpx;
          font-weight: bold;
        }

        .salary {
          flex: 1;
          text-align: right;
          color: $color-point;
          font-size: 32rpx;
          margin-left: 60rpx;
          font-weight: bold;
          white-space: nowrap;
        }

        .announcement {
          @include utils-ellipsis;
          margin: 20rpx 0;
          margin-bottom: 16rpx;
        }

        .list-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 24rpx;

          .location {
            flex-shrink: 0;
            padding-left: 34rpx;
            max-width: 30%;
            color: $font-color-tips;
            max-width: 114rpx;
            @include utils-ellipsis;
            background: url(#{$assets}/icon/address.png) no-repeat left/24rpx;
          }
        }

        .tag-content {
          display: flex;
          flex-wrap: nowrap;

          .tag {
            line-height: 44rpx;
            padding: 0 12rpx;
            border-radius: 4rpx;
            background-color: $color-white;
            color: $font-color-basic;

            & + .tag {
              margin-left: 12rpx;
            }
          }
        }
      }

      .check-all {
        text-align: center;
        color: $color-primary;
      }
    }
  }

  .empty {
    text-align: center;
    padding: 170rpx 0;
    color: $font-color-basic;
    line-height: 1.8;

    .empty-img {
      width: 400rpx;
      height: 290rpx;
      margin-bottom: 25rpx;
    }

    .to {
      margin-top: 45rpx;
      width: 146rpx;
      height: 56rpx;
      font-size: 28rpx;
    }
  }

  .check {
    margin-bottom: 23rpx;
    padding-left: 177rpx;
    text-align: left;
    font-weight: bold;
    background: url(#{$assets}/view/check.png) no-repeat center left 134rpx / 26rpx 26rpx;
  }

  .mg-50 {
    margin-bottom: 50rpx;
  }
}
