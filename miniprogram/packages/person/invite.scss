@use 'styles/variables' as *;

page {
  background-color: $color-background;
}

.invite {
  position: relative;

  .fix {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .notify {
    display: flex;
    align-items: center;

    font-size: 24rpx;
    padding: 18rpx 30rpx;
    background-color: $tag-primary-background;

    .open {
      flex: 1;
      color: $color-primary;
      padding-right: 22rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/into.png') no-repeat center left 81rpx / 10rpx 18rpx;
    }

    .close {
      width: 28rpx;
      height: 28rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/delete-round.png') no-repeat center / contain;
    }
  }

  .invite-list {
    padding: 20rpx 30rpx;

    .invite-detail {
      position: relative;
      background-color: $color-white;
      border-radius: $border-radius;
      padding: 35rpx 30rpx 30rpx;
      margin-bottom: 20rpx;
    }

    .invite-origin {
      position: absolute;
      text-align: center;
      font-size: 24rpx;
      top: 0;
      right: 30rpx;
      width: 120rpx;
      height: 34rpx;

      &.company {
        color: #486cf5;
        background: url('//img.gaoxiaojob.com/uploads/mini/invite/company.png') no-repeat left center / contain;
      }

      &.platform {
        color: $color-primary;
        background: url('//img.gaoxiaojob.com/uploads/mini/invite/platform.png') no-repeat center / contain;
      }
    }

    .company-card {
      position: relative;
      display: flex;
      align-items: center;
    }

    .company-logo {
      width: 58rpx;
      height: 58rpx;
      margin-right: 8rpx;
    }

    .vip {
      &::after {
        content: '';
        position: absolute;
        top: 35rpx;
        left: 35rpx;
        width: 28rpx;
        height: 28rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/certification.png') no-repeat center / 28rpx;
      }
    }

    .company-head {
      display: flex;
      justify-content: space-between;
      line-height: 1;
      flex: 1;
    }

    .company-name {
      flex: 1;
      max-width: 365rpx;
      font-size: 28rpx;
      font-weight: bold;
      @include utils-ellipsis;
    }

    .date {
      color: $font-color-tips;
      font-size: 24rpx;
    }

    .copy-writing {
      margin-top: 15rpx;
      color: $font-color-basic;
      font-size: 24rpx;
    }

    .job-card {
      background-color: $tag-info-background;
      padding: 25rpx 20rpx 30rpx 20rpx;
      border-radius: $border-radius;
      margin-top: 30rpx;

      .job-basic {
        display: flex;

        &.unread {
          &::before {
            content: '';
            width: 12rpx;
            height: 12rpx;
            background-color: $color-point;
            margin-right: 10rpx;
            margin-top: 20rpx;
            border-radius: 50%;
          }
        }
      }

      .title {
        display: flex;
        align-items: center;
        line-height: 48rpx;
        font-size: 32rpx;
        font-weight: bold;
        flex: 1;

        .tag {
          display: inline-flex;
          align-items: center;
          margin-left: 10rpx;

          .worry,
          .organization,
          .fast {
            font-size: 22rpx;
            line-height: 30rpx;
            border-radius: 4rpx;
            padding: 5rpx;
            margin-right: 10rpx;
          }

          .worry {
            background-color: #fff0ef;
            color: $color-point;
          }

          .organization {
            background-color: #f0ffdc;
            color: #4fbc67;
          }

          .fast {
            width: 25rpx;
            height: 25rpx;
            background: $tag-primary-background url('//img.gaoxiaojob.com/uploads/mini/icon/lightning.png') no-repeat
              center/ 12rpx 22rpx;
          }
        }
      }

      .salary {
        flex-shrink: 0;
        margin-left: 2rpx;
        color: $color-point;
        font-size: 32rpx;
        font-weight: bold;
      }

      .announcement {
        @include utils-ellipsis;
        margin-top: 20rpx;
      }

      .label-content {
        display: flex;
        flex-wrap: nowrap;
        font-size: 24rpx;
        margin-top: 20rpx;

        .label {
          line-height: 44rpx;
          padding: 0 12rpx;
          border-radius: 4rpx;
          background-color: $color-white;
          color: $font-color-basic;

          & + .label {
            margin-left: 12rpx;
          }
        }
      }
    }
    .view-status {
      text-align: right;
      margin-top: 30rpx;

      .t-button {
        width: 172rpx;
        height: 56rpx;
      }
    }
  }

  .empty {
    text-align: center;
    padding: 170rpx 0;
    color: $font-color-basic;
    line-height: 1.8;

    .empty-img {
      width: 400rpx;
      height: 290rpx;
      margin-bottom: 25rpx;
    }

    .to {
      width: 146rpx;
      height: 56rpx;
      margin-top: 45rpx;
    }
  }
}
