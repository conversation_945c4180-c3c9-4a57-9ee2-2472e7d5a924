@use 'styles/variables' as *;

page {
  background-color: $page-background;
}

.delivery {
  .tab-panel {
    display: flex;
    align-items: center;
    background-color: $color-white;
    padding: 0 30rpx;
    border-bottom: 1rpx solid $border-color;

    .tab {
      margin-right: 62rpx;
    }

    .focus {
      position: relative;
      font-size: 32rpx;
      font-weight: bold;
      &::after {
        content: '';
        position: absolute;
        width: 40rpx;
        bottom: -28rpx;
        left: 35%;
        height: 6rpx;
        background: $color-primary;
        border-radius: 3rpx;
      }
    }

    .question {
      flex: 2;
      height: 32rpx;
      padding: 20rpx 20rpx 40rpx 20rpx;
      background: $color-white url('//img.gaoxiaojob.com/uploads/mini/icon/question.png') no-repeat center right / 32rpx;
    }
  }

  .status-box {
    height: 79rpx;
    overflow: hidden;
    background-color: $page-background;
    padding: 10rpx 10rpx 20rpx 23rpx;
    font-size: 24rpx;
  }

  .status-check {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    flex-wrap: nowrap;
    padding: 20rpx 0;
    height: 73rpx;

    .status-span {
      position: relative;
      margin: 0 7rpx;
      padding: 0 25rpx;
      height: 52rpx;
      line-height: 52rpx;
      background-color: $color-white;
      white-space: nowrap;
      border-radius: 30rpx;
      color: $font-color-basic;
      border: 1px solid transparent;

      &.checked {
        color: $color-primary;
        border: 1px solid $color-primary;
      }

      .count {
        position: absolute;
        top: -14rpx;
        right: -16rpx;
        line-height: 1.2;
        border-radius: 20rpx;
        padding: 1rpx 9rpx;
        font-size: 20rpx;
        color: $color-white;
        background-color: $color-point;
      }
    }
  }

  .fixed {
    position: fixed;
    width: 100%;
    left: 0;
    overflow-x: auto;
    z-index: 1;
  }

  .delivery-list {
    padding: 20rpx 30rpx;
    margin-top: 180rpx;

    .job-item {
      background-color: $color-white;
      padding: 30rpx;
      border-radius: $border-radius;
      margin-bottom: 20rpx;
      &:first-child {
        margin-top: 86rpx;
      }
    }

    .offline {
      .title,
      .announcement,
      .tag-content,
      .unit,
      .time {
        opacity: 0.6;
      }

      .salary {
        display: none;
      }

      .job-status {
        color: $font-color-tips;
        font-size: 32rpx;
        font-weight: bold;
        opacity: 1;
      }
    }

    .title {
      display: flex;
      justify-content: space-between;
    }

    .name {
      line-height: 48rpx;
      font-size: 32rpx;
      font-weight: bold;
      flex: 1;

      &.unread {
        position: relative;
        padding-left: 20rpx;
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 20rpx;
          width: 10rpx;
          height: 10rpx;
          background-color: $color-point;
          border-radius: 50%;
        }
      }
    }

    .salary {
      text-align: right;
      color: $color-point;
      font-size: 32rpx;
      font-weight: bold;
      max-width: 33%;
      margin-left: 20rpx;
      @include utils-ellipsis;
    }

    .announcement {
      @include utils-ellipsis;
      margin-top: 20rpx;
    }

    .tag-content {
      display: flex;
      flex-wrap: nowrap;
      font-size: 24rpx;
      margin-top: 20rpx;

      .tag {
        line-height: 44rpx;
        padding: 0 12rpx;
        border-radius: 4rpx;
        background-color: #f4f6fb;
        color: $font-color-basic;

        & + .tag {
          margin-left: 12rpx;
        }
      }
    }

    .unit {
      margin: 20rpx 0;
      font-size: 24rpx;
      @include utils-ellipsis;
    }

    .list-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;

      .time {
        color: $font-color-label;
        font-size: 24rpx;
      }

      .status {
        color: $color-primary;

        &.invite {
          background: $color-primary url('//img.gaoxiaojob.com/uploads/mini/icon/into-small.png') no-repeat center right
            24rpx / 9rpx 11rpx;
          color: $color-white;
          padding: 10rpx 43rpx 10rpx 23rpx;
          border-radius: 50rpx;
        }
      }
    }
  }

  .empty {
    margin-top: 150rpx;
    text-align: center;
    padding-top: 320rpx;
    line-height: 1.5;
    background: url(#{$assets}/empty/view-empty.png) no-repeat top center / 400rpx 290rpx;
    justify-content: center;
    color: $font-color-basic;

    .delivery-button {
      width: 148rpx;
      height: 56rpx;
      margin-top: 45rpx;
    }
  }
}

.block {
  .header {
    text-align: center;
    padding: 35rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    border-bottom: 2rpx solid $border-color;
  }

  .content {
    padding: 55rpx 30rpx;
    line-height: 2;
  }
  .interview {
    display: block;
    padding: 55rpx 30rpx;
    line-height: 2;
  }
}
