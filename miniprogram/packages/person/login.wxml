<navbar title="高才优聘" />

<view class="login-container">
  <view class="title">手机号登录/注册</view>
  <view class="tips">未注册的手机号将自动完成账号注册</view>

  <form class="login-form">
    <t-input bind:change="handleSetData" bind:clear="handleClear" data-key="formData.mobile" maxlength="{{ 11 }}" borderless t-class="login-form-item" placeholder="请输入手机号" type="number" clearable></t-input>
    <t-input bind:change="handleSetData" data-key="formData.code" borderless t-class="login-form-item" placeholder="请输入验证码" type="number">
      <t-button disabled="{{ countdownDisabled }}" slot="suffix" bind:tap="handleGetCode" t-class="send-code" ghost>
        <view class="text">
          {{ countdownDisabled ? '(' + countdownTime + 'S)' : sendCodeText }}
          <i class="label">{{ countdownDisabled ? '后重新发送' : '' }}</i>
        </view>
      </t-button>
    </t-input>

    <view class="agreement">
      <t-checkbox bind:change="handleSetData" data-key="isAgree" t-class="checkbox" t-class-content="checkbox-content" t-checkbox-icon="checkbox-icon" value="{{ isAgree }}">
        <view class="content" slot="content">
          <!-- <icon name="check-circle-filled {{ isAgree ? 'checked' : 'unchecked' }}" size="30rpx" t-class="checkbox-icon" /> -->
          我已阅读并同意
        </view>
      </t-checkbox>
      <navigator class="link" url="/pages/link/index?url=https://agreement.gaoxiaojob.com/docs/service-agreement" hover-class="none">用户协议</navigator>
      和
      <navigator class="link" url="/pages/link/index?url=https://agreement.gaoxiaojob.com/docs/privacy-policy" hover-class="none">隐私条款</navigator>
    </view>

    <t-button theme="primary" disabled="{{ loginDisabled }}" loading="{{ loading }}" shape="round" block bind:tap="handleSubmit">进入小程序</t-button>
    <view class="fast-login">
      <t-button wx:if="{{ isAgree }}" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" t-class="fast-login-btn" theme="primary" shape="round" ghost>快速登录</t-button>

      <t-button wx:else bind:tap="handleCodeLogin" t-class="fast-login-btn" theme="primary" shape="round" ghost>快速登录 </t-button>
    </view>
  </form>

  <!-- app-id：验证码CaptchaAppId, 从腾讯云的验证码控制台中获取, 在验证码控制台页面内【图形验证】>【验证列表】进行查看 -->
  <t-captcha id="captcha" app-id="2029280348" bindverify="handlerVerify" bindready="handlerReady" bindclose="handlerClose" binderror="handlerError" />
</view>
