import { showLoading } from '@/utils/util'
import { getList, check } from './api/view'
import { getCheckSubscribe, getVipInfo } from '@/api/person'
import { h5 } from '@/settings'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    showHintBar: false,
    hintBarUrl: '',
    visible: false,
    visibleEmpty: false,
    page: 1,
    list: <any>[],
    isEnd: false,
    isSubscribe: true,
    isShowTips: false,
    companyIndex: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    this.getList()
  },

  showSubTips() {
    const { isShowSubscribeTips } = getApp().globalData

    const { isSubscribe } = this.data

    if (isShowSubscribeTips && !isSubscribe) {
      this.setData({ isShowTips: true })
    }
  },

  async getList() {
    const { isEnd, page } = this.data
    if (isEnd) return
    showLoading()

    const { list } = await getList({ page })

    this.setData({ list: this.data.list.concat(list), page: page + 1 })

    wx.hideLoading()
    if (list.length == 0) {
      this.setData({ visibleEmpty: true })
    }

    if (list.length < 10) {
      this.setData({ isEnd: true })
    }
  },

  onVisibleChange(e: any) {
    this.setData({
      visible: e.detail.visible
    })
  },

  onClose() {
    this.setData({
      visible: false
    })
  },

  closeBindTag() {
    this.setData({ isShowTips: false })
    getApp().updateSubscribeTipsStatus(false)
  },

  handleOpen() {
    this.setData({
      visible: true
    })
  },

  clickCard(event: WechatMiniprogram.CustomEvent) {
    const { item, parentIndex, index, type } = event.currentTarget.dataset

    const { list } = this.data

    if (item.isResumeView === '2') {
      check({ id: item.companyId })
      list[parentIndex].isResumeView = '1'
      this.setData({ list })
    }

    if (type) {
      wx.navigateTo({ url: `/packages/job/detail/index?id=${item.jobList[index].id}` })
    } else {
      wx.navigateTo({ url: `/packages/company/detail/index?id=${item.companyId}` })
    }
  },

  toHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  async checkBind() {
    const { isSubscribe } = await getCheckSubscribe()

    if (isSubscribe == 1) {
      this.setData({ isSubscribe: true })
      this.closeBindTag()
    } else {
      this.setData({ isSubscribe: false })
      this.showSubTips()
    }
  },

  async fetchVipInfo() {
    const { isShowJobFastBuy, buyUrl } = await getVipInfo()

    this.setData({ showHintBar: isShowJobFastBuy, hintBarUrl: `${h5}${buyUrl.jobFast}` })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkBind()
    this.fetchVipInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.getList()
  }

  /**
   * 用户点击右上角分享
   */
})
