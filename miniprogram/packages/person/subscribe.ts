// packages/subscribe/index.ts
import { showLoading, showToast } from '@/utils/util'
import { getJobSubscribeInfo, submit, cancel } from './api/subscribe'
import { defaultDuration } from '@/settings'
import { validEmail } from '@/utils/validate'
import { getCheckSubscribe } from '@/api/person'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    urlParams: <any>{},
    jobCategoryIds: [],
    areaIds: [],
    educationIds: [],
    sendEmail: '',
    isSendWechat: true,
    isSendEmail: false,
    jobCategoryText: '',
    areaText: '',
    educationText: '',
    isSubscribe: 2,
    areaVisible: false,
    isEdit: false,
    jobCategoryVisible: false,
    educationVisible: false,
    cancelDialogShow: false,
    serviceDialogVisible: false,
    title: '创建您的专属订阅',
    content: '定时推送最新、最精准的职位信息，让您不',
    tips: '再错过好工作！',
    confirmBtn: { content: '暂不取消', variant: 'base' },
    cancelBtn: { content: '取消订阅' }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({ urlParams: options })
    this.getDetail()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkBind()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {},

  showPicker(event: WechatMiniprogram.CustomEvent) {
    const { type } = event.currentTarget.dataset
    this.setData({ [`${type}Visible`]: true })
  },

  showServiceDialog() {
    this.setData({ serviceDialogVisible: true })
  },

  async checkBind() {
    const { isSubscribe } = await getCheckSubscribe()
    this.setData({ isSubscribe: isSubscribe === 1 ? 1 : 2 })
  },

  getDetail() {
    showLoading()
    getJobSubscribeInfo().then((r: any) => {
      const jobCategoryIds = r.jobCategoryIds ? r.jobCategoryIds.split(',') : []
      const areaIds = r.areaIds ? r.areaIds.split(',') : []
      const educationIds = r.educationIds !== '0' ? r.educationIds.split(',') : []
      const sendEmail = r.sendEmail ? r.sendEmail : ''
      const jobCategoryText = r.jobCategoryText ? r.jobCategoryText : ''
      const areaText = r.areaText ? r.areaText : ''
      const educationText = r.educationText ? r.educationText : ''
      const isSubscribe = r.isSubscribe ? r.isSubscribe : 2

      wx.hideLoading()

      this.setData({
        jobCategoryIds,
        areaIds,
        educationIds,
        sendEmail,
        jobCategoryText,
        areaText,
        educationText,
        isSubscribe,
        isSendEmail: r.isSendEmail === '1' ? true : false,
        isEdit: jobCategoryIds.length > 0 ? true : false
      })
      const { urlParams: { formType, jobType, areaId, backupAreaLabel, backupJobLabel } } = this.data
      // 如果是从搜索结果页来
      console.log(this.data.urlParams);
      if (formType === 'serachResult') {
        this.setData({
          jobCategoryIds: jobType ? jobType.split(',') : [],
          jobCategoryText: backupJobLabel,
          areaIds: areaId ? areaId.split(',') : [],
          areaText: backupAreaLabel
        })
      }

      if (!r.isSendWechat) {
        this.setData({ isSendWechat: true })
      } else {
        this.setData({ isSendWechat: r.isSendWechat === '1' ? true : false })
      }

      if (this.data.isEdit) {
        this.setData({
          title: '专属订阅已开启',
          content: '每周一早上8:30将向您推送最新、最精准的',
          tips: '职位信息，让您不再错过好工作！'
        })
      } else {
        this.setData({
          title: '创建您的专属订阅',
          content: '定时推送最新、最精准的职位信息，让您不',
          tips: '再错过好工作！'
        })
      }
    })
  },
  changeEmail(e: any) {
    const email = e.detail.value
    this.setData({ sendEmail: email })
  },

  handleWechatChange(e: any) {
    const { checked } = e.detail

    this.setData({ isSendWechat: checked })
  },

  handleEmailChange(e: any) {
    const { checked } = e.detail

    this.setData({ isSendEmail: checked })
  },

  jobCategoryChange(e: any) {
    const { value, label } = e.detail
    this.setData({
      jobCategoryIds: value,
      jobCategoryText: label.join(',')
    })
  },
  areaChange(e: any) {
    const { value, label } = e.detail
    this.setData({
      areaIds: value,
      areaText: label.join(',')
    })
  },
  educationChange(e: any) {
    const { value, label } = e.detail
    this.setData({
      educationIds: value,
      educationText: label
    })
  },
  async submit() {
    const { jobCategoryIds, areaIds, educationIds, sendEmail, isSendWechat, isSendEmail, isEdit } = this.data

    // 意向职位必填
    if (!jobCategoryIds.length) {
      wx.showToast({
        title: '请填写意向职位',
        icon: 'none',
        duration: defaultDuration
      })
      return
    }

    // 地区必填
    if (!areaIds.length) {
      wx.showToast({
        title: '请填写意向城市',
        icon: 'none',
        duration: defaultDuration
      })
      return
    }

    // 学历要求必填
    if (!educationIds.length) {
      wx.showToast({
        title: '请填写学历要求',
        icon: 'none',
        duration: defaultDuration
      })
      return
    }

    // 微信服务号和邮箱必须勾选一个
    if (!isSendWechat && !isSendEmail) {
      wx.showToast({
        title: '请至少选择一个推送渠道',
        icon: 'none',
        duration: defaultDuration
      })
      return
    }

    if (isSendEmail && sendEmail === '') {
      wx.showToast({
        title: '请输入邮箱地址',
        icon: 'none',
        duration: defaultDuration
      })
      return
    }

    if (isSendEmail && sendEmail !== '') {
      const valid = validEmail(sendEmail)
      if (!valid) {
        wx.showToast({
          title: '请输入正确的邮箱地址',
          icon: 'none',
          duration: defaultDuration
        })
        return
      }
    }

    const params = {
      jobCategoryIds: jobCategoryIds.join(','),
      areaIds: areaIds.join(','),
      educationIds: educationIds.join(','),
      sendEmail,
      isSendWechat: isSendWechat ? '1' : '2',
      isSendEmail: isSendEmail ? '1' : '2'
    }

    await submit(params)

    if (isEdit) {
      showToast({
        title: '保存修改成功',
        icon: 'success',
        duration: defaultDuration
      })
      return
    }

    showToast({
      title: '订阅成功',
      icon: 'success',
      duration: defaultDuration,
      callback: () => {
        this.getDetail()
      }
    })
  },
  // 取消订阅
  cancelSubmitShow() {
    this.setData({ cancelDialogShow: true })
  },

  cancelHide() {
    this.setData({ cancelDialogShow: false })
  },

  cancelSubmit() {
    cancel().then(() => {
      // 提示成功
      this.setData({ cancelDialogShow: false })
      showToast({
        title: '取消订阅成功！',
        icon: 'success',
        duration: defaultDuration,
        callback: () => {
          this.getDetail()
        }
      })
    })
  }
})
