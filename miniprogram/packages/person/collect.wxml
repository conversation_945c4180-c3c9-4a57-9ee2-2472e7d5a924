<view class="collect-container">
  <header title="我的收藏" bind:back="handleBack" />

  <t-tabs value="{{ tabValue }}" space-evenly="{{ false }}" bind:change="onTabsChange">
    <t-tab-panel wx:for="{{ tabOption }}" wx:key="index" label="{{ item.label }}" value="{{ item.value }}" />
  </t-tabs>

  <view wx:if="{{ showVipHint }}" class="vip-hint {{ tabValue === 'company' ? (resultList.length ? 'is-none' : 'is-hidden') : '' }}" bindtap="handleToVip">
    <view class="content">
      <text class="title">立即解锁全部收藏内容</text>
      <text class="description">普通用户仅可查看最近收藏50个职位/公告</text>
    </view>

    <text class="button">了解更多</text>
  </view>

  <block wx:if="{{ resultList.length }}">
    <view class="back-top-trigger" hidden="{{ showBackTop === false }}" catchtap="handleBackTop"></view>

    <scroll-view class="scroll-view" refresher-enabled scroll-y refresher-triggered="{{ refreshTrigger }}" scroll-top="{{ scrollTop }}" bindrefresherrefresh="handleRefresh" bindscrolltolower="handleLower" bindscroll="handleScroll">
      <view class="result-list">
        <t-swipe-cell wx:for="{{ resultList }}" wx:key="id">
          <view class="result-item {{ tabValue === 'job' ? 'job-class-name' : '' }}">
            <job-item wx:if="{{ tabValue === 'job' }}" c-class="job-item" detail="{{ item }}" show-welfare="{{ false }}" show-release-time="{{ true }}" />

            <announcement-item wx:if="{{ tabValue === 'announcement' }}" detail="{{ item }}" />

            <company-item wx:if="{{ tabValue === 'company' }}" detail="{{ item }}" />
          </view>

          <view slot="right" class="swipe-slot-right" catchtap="handleClick" data-index="{{ index }}" data-item="{{ item }}">取消{{ tabValue === 'company' ? '关注' : '收藏' }}</view>
        </t-swipe-cell>
      </view>

      <block wx:if="{{ tabValue !== 'company' }}">
        <view wx:if="{{ showVipHint && resultList.length === 50 }}" class="vip-bottom-hint">
          <view class="vip-bottom-content" bindtap="handleToVip">
            <text class="text">立即解锁全部收藏内容</text>
            <t-icon name="chevron-right-double" size="20" />
          </view>
        </view>
      </block>
    </scroll-view>
  </block>

  <view class="empty-container" wx:else>
    <t-empty image="{{ emptyImage }}" description="暂无收藏内容" />
    <t-button theme="primary" size="small" bindtap="handleView">{{ emptyButtonText }}</t-button>
  </view>
</view>
