import { CheckStatus } from '@/settings'
import { getPrivacyData, setAnonymousStatus, setResumeShowStatus } from './api/privacy'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    options: [
      {
        label: '隐藏简历',
        value: CheckStatus.uncheck,
        loading: false,
        tips: '打开隐藏开关后，您的简历将不会推荐给HR，也无法被搜索到'
      },
      {
        label: '匿名展示',
        value: CheckStatus.uncheck,
        loading: false,
        tips: '打开匿名展示开关，HR查看简历时，将无法看到你的真实头像和姓名'
      }
    ]
  },

  handleBack() {
    wx.navigateBack()
  },

  handleChange(event: WechatMiniprogram.CustomEvent) {
    const { detail, currentTarget } = event
    const { value } = detail
    const { index } = currentTarget.dataset
    const { options } = this.data
    const apis = [setResumeShowStatus, setAnonymousStatus]

    options[index].loading = true

    this.setData({ options }, async () => {
      await apis[index]()

      options[index].value = value
      options[index].loading = false

      this.setData({ options })
    })
  },

  async fetchData() {
    const { isHideResume, isAnonymous } = await getPrivacyData()
    const { options } = this.data

    options[0].value = isHideResume
    options[1].value = isAnonymous

    this.setData({ options })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
