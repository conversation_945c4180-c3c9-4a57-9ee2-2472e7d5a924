// import * as echarts from '../components/ec-canvas/echarts'

// eslint-disable-next-line @typescript-eslint/no-var-requires
const echarts = require('../components/ec-canvas/echarts')
const primaryColor = '#FFA000'
const labelBasicColor = '#5C5C5C'

export function exposureLineChart(options: any, callback: any) {
  const { canvas, width, height, canvasDpr: dpr, xAxisData, data: seriesData = [] } = options
  const chart = echarts.init(canvas, null, {
    width: width,
    height: height,
    devicePixelRatio: dpr // new
  })
  canvas.setChart(chart)

  const seriesOptions = {
    type: 'line',
    smooth: true,
    showSymbol: false
  }

  const data = seriesData.map((item: any) => {
    return {
      data: item,
      ...seriesOptions
    }
  })

  const option = {
    color: ['#FFA000', '#486CF5'],
    grid: {
      left: 20,
      right: 20,
      top: 20,
      bottom: 21
    },
    tooltip: {
      trigger: 'axis',
      showContent: true,
      axisPointer: {
        lineStyle: {
          color: primaryColor
        }
      },
      formatter: function (value: any) {
        const index = value[0].dataIndex
        const callbackData = value.map((item: any) => item.value)
        callback({ data: callbackData, index })
        return ''
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      triggerEvent: true,
      axisTick: {
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#EBEBEB'
        }
      },
      axisLabel: {
        color: labelBasicColor,
        fontSize: 10,
        interval: 0
      },
      splitLine: {
        show: true,
        interval: 0,
        lineStyle: {
          color: '#D8D8D8',
          type: 'dashed'
        }
      },
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: data
  }

  chart.setOption(option)
  return chart
}
