@use 'styles/variables' as *;

.collect-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: $page-background;
  overflow: hidden;

  .t-tabs__item-inner--active {
    color: $font-color;
    font-size: 32rpx;
  }

  .vip-hint {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    line-height: 1;
    background-color: $color-primary-background;

    &.is-hidden {
      visibility: hidden;
      pointer-events: none;
    }

    &.is-none {
      display: none;
    }

    .content {
      display: flex;
      flex-direction: column;
    }

    .title {
      margin-bottom: 20rpx;
      padding-left: 75rpx;
      color: #714e2f;
      font-size: 28rpx;
      font-weight: bold;
      background: url(#{$assets}/person/collect/vip.png) no-repeat left top / 73rpx 29rpx;
    }

    .description {
      color: #9f7a5a;
      font-size: 24rpx;
    }

    .button {
      width: 144rpx;
      height: 48rpx;
      color: #745220;
      font-size: 28rpx;
      font-weight: bold;
      text-align: center;
      line-height: 48rpx;
      background: linear-gradient(90deg, #f4c7a5, #fde0c3);
      border-radius: 24rpx;
    }
  }

  .vip-bottom-hint {
    padding: 0 30rpx 60rpx;
    color: $color-primary;
    font-size: 27rpx;
    text-align: center;

    .vip-bottom-content {
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }

    .text {
      padding-left: 50rpx;
      background: url(#{$assets}/person/collect/vip-member.png) no-repeat left center / 42rpx 30rpx;
    }
  }

  .scroll-view {
    overflow: auto;
  }

  .result-list {
    padding: 20rpx 0;
  }

  .t-swipe-cell {
    margin-right: 30rpx;
    margin-bottom: 20rpx;
    border-radius: 16rpx;
    transform: rotate(0);
  }

  .result-item {
    margin-left: 30rpx;
    background-color: $color-white;
    border-top-left-radius: 16rpx;
    border-bottom-left-radius: 16rpx;
    overflow: hidden;

    &.job-class-name {
      padding: 30rpx;
    }
  }

  .job-item {
    margin-top: 0;
  }

  .swipe-slot-right {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 45rpx;
    width: 56rpx;
    height: 100%;
    color: $color-white;
    font-size: 28rpx;
    line-height: 40rpx;
    background-color: $color-primary;
  }

  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 160rpx;
  }

  .t-image {
    width: 500rpx;
    height: 310rpx;
  }

  .t-button {
    margin-top: 80rpx;
    width: 300rpx;
  }
}
