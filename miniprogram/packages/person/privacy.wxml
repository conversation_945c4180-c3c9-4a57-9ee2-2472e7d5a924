<view class="privacy-container">
  <header title="隐私设置" bindback="handleBack" />

  <view class="privacy-main">
    <view class="card" wx:for="{{ options }}" wx:key="index">
      <view class="title">
        <text class="label">{{ item.label }}</text>

        <t-switch custom-value="{{ ['1', '2'] }}" value="{{ item.value }}" loading="{{ item.loading }}" data-index="{{ index }}" bindchange="handleChange" />
      </view>

      <view class="tips">{{ item.tips }}</view>
    </view>
  </view>
</view>
