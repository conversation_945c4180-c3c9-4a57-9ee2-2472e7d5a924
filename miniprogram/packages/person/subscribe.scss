@use 'styles/variables' as *;

page {
  background-color: $color-background;
}

.subscribe {
  padding: 0 30rpx;
  height: 100vh;
  background: url('//img.gaoxiaojob.com/uploads/mini/subscribe/subscribe-bg.png') no-repeat center top/ 750rpx 560rpx;
  box-sizing: border-box;

  .t-navbar__content {
    background: url('//img.gaoxiaojob.com/uploads/mini/subscribe/subscribe-bg.png') no-repeat center top/ auto 560rpx;
  }

  .t-input__control--disabled {
    font-weight: bold;
  }

  .subscribe-content {
    padding-top: 70rpx;
    margin-bottom: 40rpx;

    .title {
      font-size: 46rpx;
      color: $color-primary;
      font-weight: bold;
    }

    .text {
      margin-top: 35rpx;
      line-height: 42rpx;
    }
  }

  .subscribe-form {
    padding: 20rpx 30rpx 20rpx 0;
    background-color: $color-white;
    border-radius: $border-radius;
    margin-bottom: 20rpx;

    .t-input__label {
      font-size: 30rpx;
    }

    .t-checkbox__title {
      font-size: 30rpx;
    }

    .t-checkbox--block {
      padding-bottom: 0;
      padding-left: 26rpx;
    }

    .channel {
      margin: 25rpx 0 0 30rpx;
      font-size: 30rpx;
    }

    .required {
      &::before {
        content: '*';
        color: $color-point;
        margin-right: 8rpx;
      }
    }

    .follow {
      display: flex;
      justify-content: space-between;
      padding-left: 35rpx;
      padding-top: 18rpx;
      font-size: 24rpx;
      color: $font-color-label;
      margin-bottom: 30rpx;

      .tips {
        padding-left: 40rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/attention.png') no-repeat left center/24rpx;
      }

      .go {
        color: $color-primary;
        padding-right: 19rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/icon/into.png') no-repeat center right/10rpx 18rpx;
      }
    }
  }

  .push {
    padding-bottom: 50rpx;

    .t-checkbox__icon--left {
      margin-right: 10rpx;
    }

    .t-input {
      padding: 20rpx 30rpx;
    }

    .t-input__content {
      font-weight: bold;
    }

    .t-input__control--disabled {
      font-weight: normal;
    }
  }

  .seclet {
    .t-input {
      padding-right: 0;
    }

    .t-input__control--disabled {
      color: $font-color;
      max-width: 380rpx;
      @include utils-ellipsis;
    }
  }

  .submit {
    padding: 40rpx 50rpx;
    text-align: center;

    .cancel {
      margin-top: 40rpx;
      font-size: 32rpx;
      color: $font-color-basic;
    }
  }
}
