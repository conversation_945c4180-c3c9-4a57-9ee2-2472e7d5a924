<view class="subscribe">
  <t-navbar title="职位订阅" left-arrow bind:go-back="handleBack" />

  <view class="subscribe-content">
    <view class="title">{{ title }}</view>
    <view class="text">{{ content }}</view>
    <view>{{ tips }}</view>
  </view>

  <view class="subscribe-form seclet">
    <t-input align="right" class="required" readonly data-type="jobCategory" label="意向职位" suffixIcon="{{ { name: 'chevron-right' } }}" bind:tap="showPicker" value="{{ jobCategoryText }}" disabled> </t-input>

    <!-- 意向职位选择器 -->
    <picker-job-category model="{{ jobCategoryIds }}" visible="{{ jobCategoryVisible }}" bind:change="jobCategoryChange"> </picker-job-category>

    <t-input align="right" class="required" readonly data-type="area" label="意向城市" suffixIcon="{{ { name: 'chevron-right' } }}" value="{{ areaText }}" bind:tap="showPicker" disabled></t-input>

    <!-- 意向城市选择器 -->
    <picker-area model="{{ areaIds }}" visible="{{ areaVisible }}" bind:change="areaChange"> </picker-area>

    <t-input align="right" class="required" borderless readonly data-type="education" label="学历要求" suffixIcon="{{ { name: 'chevron-right' } }}" value="{{ educationText }}" bind:tap="showPicker" disabled> </t-input>
    <!-- 学历选择器 -->
    <picker-education model="{{ educationIds }}" visible="{{ educationVisible }}" bindchange="educationChange" limit="5" />
  </view>

  <view class="subscribe-form push">
    <view class="required channel">推送渠道</view>

    <t-checkbox label="微信服务号" icon="rectangle" value="{{ isSendWechat }}" bindchange="handleWechatChange" checked="{{ isSendWechat }}" />
    <view class="follow" wx:if="{{ isSubscribe === 2 }}">
      <view class="tips">请先关注服务号完成绑定哦～</view>
      <view class="go" bindtap="showServiceDialog">去关注</view>
    </view>
    <t-checkbox label="指定邮箱" icon="rectangle" value="{{ isSendEmail }}" bindchange="handleEmailChange" checked="{{ isSendEmail }}" />

    <t-input placeholder="请输入邮箱地址" bind:change="changeEmail" disabled="{{ !isSendEmail }}" value="{{ sendEmail }}"></t-input>
  </view>

  <view class="submit">
    <t-button theme="primary" size="large" block bind:tap="submit">{{ isEdit ? '保存修改' : '保存' }}</t-button>
    <view class="cancel" bind:tap="cancelSubmitShow" wx:if="{{ isEdit }}">取消订阅</view>
  </view>
</view>

<qrcode-popup visible="{{ serviceDialogVisible }}" />

<t-dialog visible="{{ cancelDialogShow }}" title="提示" content="取消订阅后，将无法第一时间获取最新的职位推送，确定要取消吗?" confirm-btn="{{ confirmBtn }}" cancel-btn="{{ cancelBtn }}" bind:confirm="cancelHide" bind:cancel="cancelSubmit" />
