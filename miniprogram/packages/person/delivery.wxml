<view class="delivery">
  <t-navbar class="block" title="我的投递" left-arrow bind:go-back="handleBack" />

  <view class="fixed">
    <hint-bar wx:if="{{ showHintBar }}" url="{{ hintBarUrl }}" />

    <view class="tab-panel">
      <view class="tab {{ site === '1' ? 'focus' : '' }}" bindtap="handleTabChange" data-type="1">站内投递</view>
      <view class="tab {{ site === '2' ? 'focus' : '' }}" bindtap="handleTabChange" data-type="2">站外投递</view>
      <view bindtap="handleQuestion" class="question"></view>
    </view>

    <view class="status-box">
      <view class="status-check" wx:if="{{ site === '1' }}">
        <view class="status-span {{ status === '' ? 'checked' : '' }}" bindtap="handleCheck" data-status=""
          >全部<view class="count" wx:if="{{ jobApplyAllCount }}">{{ jobApplyAllCount }}</view>
        </view>
        <view class="status-span {{ status === '1' ? 'checked' : '' }}" data-status="1" bindtap="handleCheck"
          >已投递<view class="count" wx:if="{{ jobApplyWaitCheckCount }}">{{ jobApplyWaitCheckCount }}</view>
        </view>
        <view class="status-span {{ status === '-1' ? 'checked' : '' }}" data-status="-1" bindtap="handleCheck"
          >已查看
          <view class="count" wx:if="{{ jobApplyCheckCount }}" bindtap="handleCheck">{{ jobApplyCheckCount }}</view>
        </view>
        <view class="status-span {{ status === '2' ? 'checked' : '' }}" data-status="2" bindtap="handleCheck"
          >通过初筛
          <view class="count" wx:if="{{ jobApplyPassCount }}">{{ jobApplyPassCount }}</view>
        </view>
        <view class="status-span {{ status === '3' ? 'checked' : '' }}" data-status="3" bindtap="handleCheck"
          >邀请面试
          <view class="count" wx:if="{{ jobApplyInviteCount }}">{{ jobApplyInviteCount }}</view>
        </view>
        <view class="status-span {{ status === '4' ? 'checked' : '' }}" data-status="4" bindtap="handleCheck"
          >不合适
          <view class="count" wx:if="{{ jobApplyNoPassCount }}">{{ jobApplyNoPassCount }}</view>
        </view>
        <view class="status-span {{ status === '5' ? 'checked' : '' }}" data-status="5" bindtap="handleCheck"
          >已录用
          <view class="count" wx:if="{{ jobApplyEmployedCount }}">{{ jobApplyEmployedCount }}</view>
        </view>
      </view>
      <view class="status-check" wx:if="{{ site === '2' }}">
        <view class="status-span {{ status === '' ? 'checked' : '' }}" bindtap="handleCheck" data-status="">全部</view>
        <view class="status-span {{ status === '7' ? 'checked' : '' }}" data-status="7" bindtap="handleCheck">已投递</view>
        <view class="status-span {{ status === '2' ? 'checked' : '' }}" data-status="2" bindtap="handleCheck">通过初筛 </view>
        <view class="status-span {{ status === '3' ? 'checked' : '' }}" data-status="3" bindtap="handleCheck">邀请面试 </view>
        <view class="status-span {{ status === '4' ? 'checked' : '' }}" data-status="4" bindtap="handleCheck">已面试 </view>
        <view class="status-span {{ status === '5' ? 'checked' : '' }}" data-status="5" bindtap="handleCheck">不合适 </view>
        <view class="status-span {{ status === '6' ? 'checked' : '' }}" data-status="6" bindtap="handleCheck">待应聘 </view>
      </view>
    </view>
  </view>

  <view class="delivery-list" wx:if="{{ list.length }}">
    <view class="job-item {{ item.jobStatus == 1 ? '' : 'offline' }}" wx:for="{{ list }}" wx:key="index" wx:item="item" data-info="{{ item }}" data-index="{{ index }}" bindtap="handleJobDetail">
      <view class="title">
        <view class="name {{ item.isCheckRemind === '2' ? 'unread' : '' }}">{{ item.jobName }}</view>
        <view class="salary" wx:if="item.jobStatus == 1">{{ item.wageInfo || item.salary }}</view>
        <view class="job-status" wx:if="item.jobStatus != 1">{{ item.jobNotice }}</view>
      </view>
      <view class="announcement" wx:if="{{ item.announcementName }}">{{ item.announcementName }}</view>
      <view class="tag-content">
        <view class="tag" wx:if="{{ item.educationText }}">{{ item.educationText }}</view>
        <view class="tag" wx:if="{{ item.amount }}">招{{ item.amount }}人</view>
        <view class="tag" wx:if="{{ item.experienceText }}">{{ item.experienceText }}</view>
        <view class="tag" wx:if="{{ item.areaName }}">{{ item.areaName }}</view>
      </view>
      <view class="unit">{{ item.companyName }}</view>
      <view class="list-bottom">
        <view class="time">投递日期：{{ item.applyDate }}</view>
        <view class="status {{ item.status === '3' ? 'invite' : '' }}" catchtap="handleView" data-info="{{ item }}"> {{ item.applyStatusTxt }}</view>
      </view>
    </view>
  </view>

  <view class="empty" wx:else>
    <view class="empty-text">暂无数据</view>
    <view class="empty-text">不如投递简历主动出击？</view>
    <t-button theme="primary" class="delivery-button" size="small" bindtap="handleGoHome">去投递</t-button>
  </view>
</view>

<t-popup visible="{{ visible }}" bind:visible-change="onVisibleChange" placement="bottom" close-btn>
  <view class="block">
    <view class="header">{{ title }}</view>
    <view class="content" wx:if="{{ content }}">
      <view>{{ content }}</view>
      <view wx:if="{{ tips }}">{{ tips }}</view>
    </view>
    <view class="interview" wx:else>
      <view>面试信息：{{ interviewInfo.jobName }}</view>
      <view>面试时间：{{ interviewInfo.interviewTime }}</view>
      <view>联系人：{{ interviewInfo.contact }}</view>
      <view>联系电话：{{ interviewInfo.telephone }}</view>
      <view>联系地址：{{ interviewInfo.address }}</view>
      <view wx:if="{{ interviewInfo.content }}">备注：{{ interviewInfo.content }}</view>
    </view>
  </view>
</t-popup>
