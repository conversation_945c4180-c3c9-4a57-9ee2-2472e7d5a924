<view class="view">
  <view class="fix">
    <t-navbar title="谁看过我" left-arrow bind:go-back="handleBack" />
    <view class="notify" wx:if="{{ !showHintBar && isShowTips }}">
      <view class="text">开启消息通知，及时接收重要消息。</view>
      <view class="open" bindtap="handleOpen">去开启</view>
      <view class="close" bindtap="closeBindTag"></view>
    </view>

    <hint-bar wx:if="{{ showHintBar }}" url="{{ hintBarUrl }}" button-text="去置顶简历" />
  </view>

  <view class="view-list" wx:if="{{ list.length }}">
    <view class="company-container" wx:for="{{ list }}" wx:key="key" wx:for-item="item" wx:for-index="parentIndex" bind:tap="clickCard" data-item="{{ item }}" data-parent-index="{{ parentIndex }}">
      <view class="company-card">
        <view class="logo-box {{ item.isResumeView !== '1' ? 'unread' : '' }} {{ item.companyPackageType === '2' ? 'vip' : '' }}">
          <image class="company-logo" src="{{ item.logoUrl }}" />
        </view>
        <view class="company-info">
          <view class="company-head">
            <view class="company-name">{{ item.fullName }}</view>
            <view class="date">{{ item.shortTime }}</view>
          </view>
          <view class="company-message">{{ item.typeTxt }}·{{ item.nature }}</view>
        </view>
      </view>

      <view wx:if="{{ item.jobList.length }}">
        <view class="company-job" wx:for="{{ item.jobList }}" wx:key="jobKey" wx:for-item="jobItem" data-type="job" data-parent-index="{{ parentIndex }}" data-item="{{ item }}" data-index="{{ index }}" catchtap="clickCard">
          <view class="title">
            <view class="name">{{ jobItem.name }}</view>
            <view class="salary">{{ jobItem.wage }}</view>
          </view>
          <view class="announcement">{{ jobItem.announcementTitle }}</view>
          <view class="list-bottom">
            <view class="tag-content">
              <view class="tag" wx:if="{{ jobItem.educationTxt }}">{{ jobItem.educationTxt }}</view>
              <view class="tag">招{{ jobItem.amount }}人</view>
              <view class="tag" wx:if="{{ jobItem.experience }}">{{ jobItem.experience }}</view>
            </view>

            <view class="location">{{ jobItem.area }}</view>
          </view>
        </view>
        <view class="check-all" wx:if="{{ item.jobList.length > 1 }}">查看全部{{ item.jobCount }}个在招职位</view>
      </view>
    </view>
  </view>

  <view class="empty" wx:else>
    <image class="empty-img" src="//img.gaoxiaojob.com/uploads/mini/empty/view-empty.png" />
    <view>暂无单位查看数据</view>
    <view>不如投递简历主动出击？</view>

    <t-button class="to" theme="primary" bindtap="toHome">去投递</t-button>
  </view>

  <qrcode-popup visible="{{ visible }}" />
</view>
