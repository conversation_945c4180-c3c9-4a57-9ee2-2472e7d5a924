@use 'styles/variables' as *;

page {
  background-color: #fff;
}

.login-container {
  padding: 90rpx 54rpx 80rpx;

  .title {
    font-size: 42rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }

  .tips {
    color: $font-color-label;
  }

  .login-form {
    display: block;
    padding-top: 90rpx;

    .login-form-item {
      height: 88rpx;
      border-radius: 88rpx;
      padding: 0 40rpx;
      background-color: #f9f9f9;
      margin-bottom: 40rpx;
    }

    .send-code {
      padding: 0;
      font-weight: normal;
      border: none;
      color: $font-color;

      &::after {
        border: none;
      }

      .text {
        display: inline-block;
        color: $color-primary;
      }

      .label {
        color: $font-color;
      }
    }

    .agreement {
      --td-checkbox-icon-size: 34rpx;

      display: flex;
      align-items: center;
      font-size: 24rpx;
      margin-top: 76rpx;
      margin-bottom: 50rpx;

      .checkbox-content {
        margin-top: 0;
      }
      .content {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        padding-top: 2rpx;
        color: $font-color;
      }

      .checkbox {
        padding: 0;
      }

      .t-checkbox__icon {
        margin-left: 0;
      }

      .link {
        color: $color-primary;
      }
    }

    .fast-login {
      margin-top: 22rpx;
      display: flex;
      justify-content: center;

      .fast-login-btn {
        &::after {
          border: none;
        }
      }
    }
  }
}
