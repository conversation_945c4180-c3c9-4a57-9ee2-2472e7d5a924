<view class="auth__container">
  <header title="{{ title }}" arrow="{{ arrow }}" bind:back="handleBack" />

  <block wx:if="{{ title }}">
    <view class="auth__logo text-align-left">
      <image class="auth__logo-image" src="{{ logo }}"></image>
    </view>

    <view class="auth__title text-align-left">欢迎来到高校人才网</view>

    <view class="auth__description text-align-left">登录后查看海量职位</view>

    <view class="auth__bottom">
      <t-button block theme="primary" size="large" open-type="{{ agree ? 'getPhoneNumber' : '' }}" bind:tap="handlePhoneNumber" bindgetphonenumber="handleGetPhoneNumber">立即登录</t-button>

      <view class="auth__agreement">
        <t-checkbox bind:change="handleAgree" data-key="isAgree" t-class="checkbox" t-class-content="checkbox-content" t-checkbox-icon="checkbox-icon" value="{{ isAgree }}">
        <view class="content" slot="content">
          <!-- <t-icon name="check-circle-filled {{ agree ? 'checked' : 'unchecked' }}" size="30rpx" t-class="checkbox-icon" /> -->
          我已阅读并同意
        </view>
      </t-checkbox>

        <navigator class="auth__link" url="/pages/link/index?url=https://agreement.gaoxiaojob.com/docs/service-agreement" hover-class="none" catch:tap>《用户协议》</navigator>
        和
        <navigator class="auth__link" url="/pages/link/index?url=https://agreement.gaoxiaojob.com/docs/privacy-policy" hover-class="none" catch:tap>《隐私条款》</navigator>
      </view>
    </view>
  </block>
</view>
