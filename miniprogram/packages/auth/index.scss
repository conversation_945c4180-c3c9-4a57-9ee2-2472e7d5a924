@use 'styles/variables.scss' as *;

.auth {
  &__container {
    padding: 146rpx 56rpx 0;
    text-align: center;

    .text-align-left {
      text-align: left;
    }
  }

  &__logo {
    margin-bottom: 48rpx;

    &-image {
      width: 82rpx;
      height: 82rpx;
      object-fit: contain;
    }
  }

  &__title {
    margin-bottom: 40rpx;
    color: $font-color;
    font-size: 48rpx;
    font-weight: bold;
  }

  &__description {
    margin-bottom: 298rpx;
    color: $font-color-basic;
    font-size: 28rpx;
  }

  &__bottom {
    padding: 0 30rpx;
  }

  &__agreement {
    --td-checkbox-icon-size: 34rpx;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-top: 44rpx;
    color: $font-color-label;
    font-size: 24rpx;

    .checkbox-content {
      margin-top: 0rpx !important;
    }

    .content {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      padding-top: 2rpx;
      color: $font-color;
    }

    .checkbox {
      padding: 0;
    }

    .t-checkbox__icon {
      margin-left: 0;
    }
  }

  &__link {
    color: $color-primary;
  }
}