import { mobileCodeLogin, scanCodeLogin, sendSceneCode } from '@/api/entry'
import { assetsURL } from '@/settings'
import { authSuccessCallback, checkLoginByCode, wxLogin } from '@/utils/auth'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    title: '',
    arrow: false,

    scene: '',

    logo: `${assetsURL}/../static/image/logo/logo_1.png`,

    agree: false
  },

  handleBack() {
    wx.switchTab({ url: '/pages/home/<USER>' })
  },

  handlePhoneNumber() {
    if (this.data.agree) return

    wx.showToast({
      title: '请先同意相关协议和政策',
      icon: 'none',
      duration: 3000
    })
  },

  handleAgree() {
    const { agree } = this.data

    this.setData({ agree: !agree })
  },

  updateUserData(data: any) {
    authSuccessCallback(data, this.handleBack)
  },

  async handleGetPhoneNumber(event: WechatMiniprogram.ButtonGetPhoneNumber) {
    const { /* iv, encryptedData, */ code: mobileCode } = event.detail

    if (!mobileCode) return

    const { scene } = this.data
    const unionCode = await wxLogin()

    const data = await mobileCodeLogin({ unionCode, mobileCode, scene })

    this.updateUserData(data)
  },

  handleShowLogin() {
    this.setData({ title: '登录', arrow: true })
  },

  async handleLogin(scene: string) {
    try {
      // * 接口会通知 PC 自动登录
      const data: any = await checkLoginByCode(scene)

      this.updateUserData(data)
    } catch {
      this.handleShowLogin()
    }
  },

  async handleLoad() {
    const { scene } = this.data
    const { fetchUserStatus } = getApp()

    // * 通知服务器扫码成功
    await scanCodeLogin({ scene })

    fetchUserStatus(async (isLogin: boolean) => {
      // * 携带 scene，服务端修改用户登录状态
      if (isLogin) {
        // * 通知 PC 自动登录
        await sendSceneCode({ scene })
        this.handleBack()
      } else {
        this.handleLogin(scene)
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(query) {
    const { scene = '' } = query

    this.data.scene = scene
    this.handleLoad()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
