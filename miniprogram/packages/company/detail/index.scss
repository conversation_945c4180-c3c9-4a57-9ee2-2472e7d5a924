@use 'styles/variables' as *;

.main-container {
  overflow: hidden;
  height: 100vh;

  #new-nav-bar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 5;
  }
}

.company-fixed {
  position: fixed;
  bottom: 320rpx;
  right: 26rpx;
  z-index: 9;

  .list {
    width: 70rpx;
    height: 70rpx;
    background: #ffffff;
    box-shadow: 0rpx 3rpx 18rpx 0rpx rgba(51, 51, 51, 0.21);
    border-radius: 50%;
  }

  .share {
    background: url('#{$assets}/icon/share.png') no-repeat center / 38rpx 38rpx #fff;

    .share-btn {
      opacity: 0;
    }
  }

  .collect {
    margin-top: 20rpx;
    background: url('#{$assets}/icon/collect.png') no-repeat center / 38rpx 38rpx #fff;

    &.collected {
      background: url('#{$assets}/icon/collected.png') no-repeat center / 38rpx 38rpx #fff;
    }
  }
}

.scroll-container {
  .company-container {
    display: flex;
    flex-direction: column;

    // .t-navbar__left {
    //   width: 100%;
    // }

    &.sticky {
      margin-left: 0;
      background: none;

      .content {
        margin-left: 0;
      }

      .company-title {
        display: block;
        flex: 1;
        max-width: 55%;
        @include utils-ellipsis;
        font-size: 32rpx;
        font-weight: bold;
      }

      .t-icon-chevron-left {
        color: $font-color;
      }

      .t-navbar__center {
        opacity: 1;
      }

      .t-navbar__content {
        background: $color-white;
      }
    }

    .t-navbar__capsule {
      &::before {
        background-color: #fff;
      }
    }

    .t-navbar__center {
      // width: var(--td-navbar-center-width) !important;
      opacity: 0;
    }

    .t-navbar__center-title {
      padding: 0 40rpx;
    }

    .t-navbar__content {
      background: transparent;
    }

    .t-icon-chevron-left {
      // color: $color-white;
    }

    .company-title {
      display: none;
    }

    .content {
      display: flex;
      justify-content: space-around;
      width: 87%;
      margin-left: 300rpx;
    }

    .operate {
      display: flex;
    }

    height: 100vh;
  }

  .banner {
    position: absolute;
    top: 0;
    width: 100%;

    .cover {
      position: absolute;
      height: 100%;
      width: 100%;
      top: 0;
      background-color: rgba($color: #000000, $alpha: 0.3);
    }

    image {
      width: 100%;
      height: 420rpx;
    }
  }

  .divider {
    height: 20rpx;
    background-color: $color-background;
  }

  .header {
    padding: 35rpx 30rpx 30rpx;
    background-color: $color-white;
    border-radius: 30rpx 30rpx 0 0;
    margin-top: 270rpx;
    z-index: 1;

    .infomation {
      display: flex;
      justify-content: space-between;
    }

    .name {
      font-size: 36rpx;
      font-weight: bold;
    }

    .natrue {
      font-size: 24rpx;
      line-height: 1.5;
      margin-top: 35rpx;

      text {
        &::after {
          content: ' | ';
        }

        &:last-child {
          &::after {
            content: '';
          }
        }
      }
    }

    .logo {
      position: relative;
      height: 120rpx;
      margin-left: 60rpx;

      .company-logo {
        width: 120rpx;
        height: 120rpx;
      }

      .vip {
        position: absolute;
        height: 28rpx;
        width: 28rpx;
        bottom: 0;
        right: 7rpx;
        background: url('#{$assets}/icon/certification.png') no-repeat center/contain;
      }
    }

    .tag-content {
      margin-top: 20rpx;
      height: 55rpx;
      overflow: hidden;

      .box {
        display: flex;
        white-space: nowrap;
        overflow: auto;
        height: 68rpx;

        .tag {
          padding: 6rpx 12rpx;
          height: 34rpx;
          background-color: $tag-primary-background;
          border-radius: 4rpx;
          color: $color-primary;
          font-size: 24rpx;
          margin-right: 10rpx;
        }
      }
    }
  }

  .company-detail {
    background-color: $color-white;
    padding: 40rpx 30rpx;
    padding-bottom: 150rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 2;
      padding-left: 60rpx;
      position: relative;

      .btn {
        position: absolute;
        right: 0;
        bottom: 10rpx;
        width: 88rpx;
        height: 36rpx;
        line-height: 36rpx;
        background: #ffffff;
        border-radius: 18rpx;
        border: 2rpx solid #ffa000;
        text-align: center;
        font-weight: 500;
        font-size: 24rpx;
        color: #ffa000;
      }
    }

    $title-list: introduce, picture, contact, colleges, net, address;

    @each $item in $title-list {
      .#{$item} {
        background: url('#{$assets}/company/#{$item}.png') no-repeat left top/50rpx 50rpx;
      }
    }

    .introduce-content {
      margin-top: 10rpx;
      word-break: break-all;
      line-height: 1.8;
    }

    .more {
      position: relative;
      margin-left: -20rpx;
      margin-top: -90rpx;
      padding-top: 100rpx;
      color: $color-primary;
      text-align: center;

      &::after {
        content: '';
        position: absolute;
        width: 20rpx;
        height: 20rpx;
        top: 110rpx;
        right: 260rpx;
        background: url('#{$assets}/company/drop-down.png') no-repeat center / 20rpx;
      }

      &.linear {
        background: linear-gradient(
          0deg,
          rgba($color: $color-white, $alpha: 0.9) 60%,
          rgba($color: $color-white, $alpha: 0.6)
        );
      }
    }

    .up {
      &::after {
        transform: rotate(180deg);
      }
    }

    .company-show {
      margin-top: 60rpx;
      margin-bottom: 40rpx;

      .picture-list {
        height: 180rpx;
        overflow: hidden;
        margin-top: 30rpx;
      }

      .picture-item {
        display: flex;
        white-space: nowrap;
        overflow: auto;
        height: 200rpx;

        .img {
          flex: none;
          object-fit: cover;
          width: 280rpx;
          height: 160rpx;
          border-radius: $border-radius;
          margin-right: 20rpx;
        }
      }
    }

    .distance {
      margin-top: 55rpx;
    }

    .container {
      padding: 30rpx 35rpx;
      background-color: $tag-info-background;
      border-radius: $border-radius;
      margin-top: 15rpx;

      $list: contacts, tel, fax, website, location;

      @each $name in $list {
        .#{$name} {
          padding-left: 47rpx;
          background: url('#{$assets}/company/#{$name}.png') no-repeat left center/32rpx;
        }

        .website {
          text-decoration: underline;
          @include utils-ellipsis;
        }

        view {
          margin-bottom: 20rpx;

          &:last-of-type {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .t-image-viewer {
    z-index: 8888;
  }

  .t-image-viewer__nav {
    background-color: transparent;
  }
}

.online-container {
  position: fixed;
  // top: 0;
  // left: 0;
  bottom: 0;
  width: 100%;
  height: 136rpx;
  transition: height 0.3s;
  z-index: 3;

  .cover {
    background-color: $color-black;
    opacity: 0;
  }

  .online-main {
    display: flex;
    flex-direction: column;
    border-radius: $border-radius;
    height: 100%;
    background-color: $color-white;
    z-index: 8;

    .touch-box {
      padding-bottom: 30rpx;
      background: url('#{$assets}/company/online-background.png') no-repeat top left / 100% auto;
      border-radius: 24rpx 24rpx 0 0;
    }

    .top {
      width: 77rpx;
      height: 10rpx;
      margin: 17rpx auto;
      margin-bottom: 30rpx;
      border-radius: 4rpx;
      background-color: #e6e6e6;
    }

    .tabs {
      display: flex;
      margin: 10rpx 30rpx;
    }

    .tab {
      display: flex;
      align-items: center;
      margin-right: 50rpx;

      &:last-child {
        margin-right: 0;
      }

      .tab-name {
        font-size: 32rpx;
        font-weight: bold;
        margin-right: 10rpx;

        &.active {
          color: $color-primary;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            left: 43rpx;
            bottom: -10rpx;
            width: 40rpx;
            height: 6rpx;
            background: linear-gradient(-90deg, #ffffff, #ffa000);
            border-radius: 3rpx;
          }
        }
      }

      .count {
        font-size: 24rpx;
        text-align: center;
        color: $color-point;
        background: #ffe5e4;
        padding: 2rpx;
        width: 54rpx;
        height: 24rpx;
        line-height: 24rpx;
        transform: skew(-12deg);
        border-radius: 4rpx;
      }
    }

    .filter-box {
      display: flex;
      align-items: center;
      margin: 0 30rpx;
      padding-bottom: 20rpx;

      .filter {
        display: flex;
        flex: 1;
        box-sizing: border-box;
        top: 0;
      }

      .item {
        position: relative;
        box-sizing: border-box;
        padding: 0 34rpx 0 20rpx;
        line-height: 48rpx;
        font-size: 24rpx;
        border-radius: 8rpx;
        max-width: 200rpx;
        background-color: #f4f6fb;
        border: 2rpx solid #f4f6fb;
        @include utils-ellipsis;

        &::after {
          content: '';
          position: absolute;
          width: 0;
          height: 0;
          right: 14rpx;
          bottom: 14rpx;
          display: block;
          border: 4rpx solid #c7c7c7;
          border-left-color: transparent;
          border-top-color: transparent;
        }

        & ~ .item {
          margin-left: 20rpx;
        }

        &.has-condition {
          color: $color-primary;
          background-color: $color-primary-background;
          border-color: $color-primary;

          &::after {
            border-right-color: $color-primary;
            border-bottom-color: $color-primary;
          }
        }
      }

      .city,
      .education {
        max-width: 108rpx;
      }

      .jobCategory,
      .major {
        max-width: 154rpx;
      }

      .clear {
        font-size: 24rpx;
        color: $font-color-basic;
      }
    }

    .list-box {
      position: relative;
      flex: 1 0 auto;
      background-color: $color-background;
    }

    .list {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      padding: 20rpx 0;
      box-sizing: border-box;
      background-color: $color-background;
      overflow: auto;

      .item {
        margin: 0 30rpx;
        padding: 25rpx 20rpx;
        border-radius: $border-radius;
        background-color: $color-white;
        margin-bottom: 20rpx;

        .title {
          display: flex;
        }

        .name {
          line-height: 48rpx;
          font-size: 32rpx;
          font-weight: bold;
        }

        .salary {
          flex: 1;
          text-align: right;
          color: $color-point;
          font-size: 32rpx;
          margin-left: 60rpx;
          font-weight: bold;
          white-space: nowrap;
        }

        .announcement {
          @include utils-ellipsis;
          margin-top: 20rpx;
        }

        .list-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 24rpx;

          .time {
            flex-shrink: 0;
            margin-top: 20rpx;
            padding-left: 34rpx;
            color: $font-color-tips;
            background: url(#{$assets}/icon/time.png) no-repeat left/24rpx;
          }
        }

        .tag-content {
          display: flex;
          flex-wrap: nowrap;
          margin-top: 20rpx;

          .tag {
            line-height: 44rpx;
            padding: 0 12rpx;
            border-radius: 4rpx;
            background-color: $tag-info-background;
            color: $font-color-basic;

            & + .tag {
              margin-left: 12rpx;
            }
          }
        }
      }

      .activity {
        padding: 25rpx 30rpx;
        &.offline-mark {
          .hd {
            opacity: 0.4;
          }
        }
        .hd {
          font-size: 24rpx;

          .step {
            display: inline;
            position: relative;
            top: -3rpx;
            padding-left: 15rpx;
            font-size: 24rpx;

            &::before {
              position: absolute;
              left: 0;
              top: 50%;
              content: '';
              width: 8rpx;
              height: 8rpx;
              border-radius: 50%;
              transform: translateY(-50%);
            }

            $font-colors: (-1: #ffa000, 1: #5386ff, 2: #fa635c, 3: #ffa000, 4: #333333);

            @each $label, $value in $font-colors {
              &.step#{$label} {
                color: $value;

                &::before {
                  position: absolute;
                  left: 0;
                  top: 50%;
                  content: '';
                  width: 8rpx;
                  height: 8rpx;
                  border-radius: 50%;
                  transform: translateY(-50%);
                  background-color: $value;
                }
              }
            }
          }

          .title {
            display: inline;
            font-weight: bold;
            font-size: 32rpx;
            line-height: 48rpx;
            margin: 0 10rpx 0 20rpx;
            // @include utils-ellipsis;
          }
          .tag {
            display: inline-block;
            position: relative;
            height: 38rpx;
            padding: 0 8rpx;
            top: -4rpx;
            line-height: 38rpx;
            border-radius: 12rpx 0rpx 12rpx 0rpx;

            $effect: (1, #5386ff, #e5edff), (2, #ffa000, #fff3e0), (3, #51bd69, #f0ffdc), (4, #51bd69, #f0ffdc),
              (5, #fa857f, #fff4f3), (6, #786afb, #f4f3fc);

            @each $index, $color, $bg in $effect {
              &.tag#{$index} {
                color: $color;
                background: $bg;
              }
            }
          }
        }
        .ft {
          $items: address date session;

          @each $var in $items {
            .#{$var} {
              margin-top: 4rpx;
              padding-left: 30rpx;
              color: rgba($color: #333, $alpha: 0.4);
              background: url(#{$assets}/icon/#{$var}.png) no-repeat left / 24rpx;
              @include utils-ellipsis;
            }
          }
        }
      }

      .offline {
        color: $font-color-label;
        background: $color-white url('#{$assets}/icon/offline.png') no-repeat top 30rpx right 30rpx / 109rpx 96rpx;

        .tag {
          opacity: 0.6;
        }

        .salary {
          display: none;
        }
      }
    }

    .notice-wrap {
      padding: 20rpx 0 40rpx;

      .text {
        text-align: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #ffa000;
      }

      .btn-wrap {
        display: flex;
        justify-content: center;
        margin-top: 39rpx;

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 480rpx;
          height: 80rpx;
          background: #ffa000;
          border-radius: 40rpx;
          font-weight: bold;
          font-size: 32rpx;
          color: #ffffff;
        }
      }
    }
  }
}
