<view class="main-container">
  <scroll-view class="scroll-container" scroll-y bindscroll="onScroll">
    <view class="company-container {{ isSticky ? 'sticky' : '' }}">
      <!-- <t-navbar left-arrow bind:go-back="handleBack" style="{{ coverStyle }}" id="company-nav-bar">
        <view class="content" slot="left">
          <view class="company-title">{{ info.companyName }}</view>
          <view class="operate">
            <t-button class="share" open-type="share"></t-button>
            <view class="collect {{ isCollect ? 'collected' : '' }}" ></view>
          </view>
        </view>
      </t-navbar> -->
      <!-- 调用此组件务必在引用处添加id="new-nav-bar"，因为在chat-socket.ts文件需要调用其方法 -->
      <new-nav-bar id="new-nav-bar" style="{{ coverStyle }}" title="{{ info.companyName }}" type="3" bind:clickNav="handleLogin"></new-nav-bar>

      <view class="online-container" style="{{ scollStyle }}" hidden="{{ !isShowOnline }}">
        <!-- company-fixed因zindex层级问题，需放置这里 -->
        <view class="company-fixed">
          <view class="list share">
            <t-button class="share-btn" open-type="share"></t-button>
          </view>
          <view bindtap="handleCollect" class="list collect {{ isCollect ? 'collected' : '' }}"></view>
        </view>
        <view class="online-main" direction="vertical">
          <view class="touch-box" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtap="onMoreBoxTouch">
            <view class="top"></view>
            <view class="tabs">
              <view class="tab" data-type="1" catchtap="handleTabChange">
                <view class="tab-name {{ type === '1' ? 'active' : '' }}">在招职位 </view>
                <view class="count">{{ info.jobAmount }}</view>
              </view>
              <view class="tab" data-type="2" catchtap="handleTabChange">
                <view class="tab-name {{ type === '2' ? 'active' : '' }}">在招公告 </view>
                <view class="count">{{ info.announcementAmount }}</view>
              </view>
              <view wx:if="{{ info.activityAmount != '0' }}" class="tab" data-type="3" catchtap="handleTabChange">
                <view class="tab-name {{ type === '3' ? 'active' : '' }}">引才活动 </view>
                <view class="count">{{ info.activityAmount }}</view>
              </view>
            </view>
          </view>

          <view wx:if="{{ type !== '3' }}" class="filter-box">
            <view class="filter">
              <view class="item city {{ cityId.length ? 'has-condition' : '' }}" data-type="area" bindtap="handlePick"> {{ cityText }}</view>
              <picker-base data-type="city" visible="{{ areaVisible }}" value="{{ cityId }}" keys="{{ keys }}" title="地区" options="{{ areaList }}" bindchange="handlePickerChange" />

              <view class="item jobCategory {{ jobCategoryId.length ? 'has-condition' : '' }}" data-type="jobCategory" bindtap="handlePick">{{ jobCategoryText }}</view>
              <picker-base data-type="jobCategory" visible="{{ jobCategoryVisible }}" value="{{ jobCategoryId }}" keys="{{ keys }}" title="职位类型" options="{{ jobTypeList }}" bindchange="handlePickerChange" />

              <view class="item education {{ educationId.length ? 'has-condition' : '' }}" data-type="education" bindtap="handlePick"> {{ educationText }}</view>
              <picker-base data-type="education" visible="{{ educationVisible }}" value="{{ educationId }}" keys="{{ keys }}" title="学历" options="{{ educationTypeList }}" bindchange="handlePickerChange" />

              <view class="item major {{ majorId.length ? 'has-condition' : '' }}" data-type="major" bindtap="handlePick"> {{ majorText }}</view>
              <picker-base data-type="major" visible="{{ majorVisible }}" value="{{ majorId }}" keys="{{ keys }}" title="专业" options="{{ majorList }}" bindchange="handlePickerChange" />
            </view>
            <view class="clear" bindtap="reset">清除筛选</view>
          </view>

          <view class="list-box">
            <scroll-view class="list" scroll-y bindscrolltolower="handleBottom">
              <block wx:if="{{ type !== '3' }}">
                <view class="item {{ item.status !== '1' ? 'offline' : '' }}" data-item="{{ item }}" wx:for="{{ list }}" wx:key="key" wx:item="item" bindtap="handleView">
                  <view class="title">
                    <view class="name">{{ item.name || item.title }}</view>
                    <view class="salary">{{ item.wageName }}</view>
                  </view>
                  <view class="announcement" wx:if="{{ item.title && type === '1' }}">{{ item.title }}</view>
                  <view class="list-bottom">
                    <view class="tag-content">
                      <view class="tag">{{ item.educationTypeName || item.minEducationName }}</view>
                      <view class="tag" wx:if="{{ item.amount }}">招{{ item.amount }}人</view>
                      <view class="tag" wx:if="{{ item.allJobNumber }}">{{ item.allJobNumber }}个职位</view>
                      <view class="tag" wx:if="{{ item.cityName }}">{{ item.cityName }}</view>
                      <view class="tag" wx:if="{{ item.announcementRecruitAmount }}">招{{ item.announcementRecruitAmount }}人 </view>
                    </view>

                    <view class="time">{{ item.refreshTime }}</view>
                  </view>
                </view>
              </block>
              <block wx:else>
                <view class="item activity {{ item.status === 4 ? 'offline-mark' : '' }}" data-item="{{ item }}" wx:for="{{ list }}" wx:key="key" wx:item="item" bindtap="handleView">
                  <view class="hd">
                    <view class="step {{ 'step' + item.status }}">{{ item.statusText }}</view>
                    <view class="title">{{ item.name }}</view>
                    <view class="tag {{ 'tag' + item.seriesType }}">{{ item.typeText }}</view>
                  </view>
                  <view class="ft">
                    <view class="date">活动时间:{{ item.time }}</view>
                    <view class="address">{{ item.seriesType == 2 ? '活动场次' : '活动地点' }}:{{ item.area }}</view>
                    <!-- <view class="session">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</view> -->
                  </view>
                </view>
              </block>
              <view wx:if="{{ !hasLogin }}" class="notice-wrap">
                <view class="text"> 更多{{ moreText }}，请登录查看 </view>
                <view class="btn-wrap">
                  <view bindtap="handleLogin" class="btn"> 立即登录 </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <view class="banner">
        <view class="cover"></view>
        <image src="{{ banner }}" mode="aspectFill" />
      </view>

      <view class="header">
        <view class="infomation">
          <view class="info">
            <view class="name">{{ info.companyName }}</view>
            <view class="natrue">
              <text wx:if="{{ info.typeName }}">{{ info.typeName }}</text>
              <text wx:if="{{ info.natureName }}">{{ info.natureName }}</text>
              <text wx:if="{{ info.companyScaleName }}">{{ info.companyScaleName }}</text>
            </view>
          </view>

          <div class="logo">
            <image class="company-logo" src="{{ info.logoUrl }}" />
            <view class="vip" wx:if="{{ info.companyPackageType === 2 }}"></view>
          </div>
        </view>

        <view class="tag-content" wx:if="{{ info.welfareLabel.length || info.companyLabel.length }}">
          <view class="box">
            <view class="tag" wx:if="{{ info.welfareLabel.length }}" wx:for="{{ info.welfareLabel }}" wx:key="index" wx:item="item">{{ item }}</view>
            <view class="tag" wx:if="{{ info.companyLabel.length }}" wx:for="{{ info.companyLabel }}" wx:key="index" wx:item="item">{{ item }}</view>
          </view>
        </view>
      </view>

      <view class="divider"></view>

      <view class="company-detail">
        <view wx:if="{{ info.introduce }}">
          <view class="title introduce">单位简介</view>
          <view class="introduce-content" id="introduce-text" style="{{ textStyle }}">
            <rich-text nodes=" {{ info.introduce }}"></rich-text>
          </view>
          <view class="more {{ more === '展开内容' ? '' : 'up' }} {{ !isLinear ? 'linear' : '' }}" wx:if="{{ showMore }}" bindtap="showText">{{ more }}</view>
        </view>

        <view class="company-show" wx:if="{{ info.styleAtlasList.length }}">
          <view class="title picture">单位风采</view>
          <view class="picture-list">
            <view class="picture-item">
              <image bind:tap="onClick" mode="aspectFill" wx:for="{{ info.styleAtlasList }}" wx:key="index" wx:item="item" class="img" src="{{ item }}" data-index="{{ index }}" />
            </view>
          </view>
        </view>

        <view wx:if="{{ info.contactName || info.contactTelephone || info.contactFax }}">
          <view class="title contact">联系方式</view>
          <view class="container">
            <view class="contacts" wx:if="{{ info.contactName }}">联系人：{{ info.contactName }}</view>
            <view class="tel" wx:if="{{ info.contactTelephone }}">联系方式：{{ info.contactTelephone }}</view>
            <view class="fax" wx:if="{{ info.contactFax }}">传真：{{ info.contactFax }}</view>
          </view>
        </view>

        <view class="distance" wx:if="{{ info.hasChildUnitList }}" wx:for="{{ info.childUnitList }}" wx:key="index" wx:item="item">
          <view class="title colleges">{{ item.name }}</view>
          <view class="container">
            <view class="contacts">联系人：{{ item.contact }}</view>
            <view class="tel" wx:if="{{ item.telephone }}">联系方式：{{ item.telephone }}</view>
            <view class="fax" wx:if="{{ item.fax }}">传真：{{ item.fax }}</view>
          </view>
        </view>

        <view class="distance" wx:if="{{ info.website }}">
          <view class="title net">单位网址</view>
          <view class="container">
            <view class="website" data-type="{{ 1 }}" data-content="{{ info.website }}" bindtap="handleCopy">{{ info.website }}</view>
          </view>
        </view>
        <view class="distance" wx:if="{{ info.address }}">
          <view class="title address">
            <view>单位地址</view>
            <view data-type="{{ 2 }}" data-content="{{ info.address }}" bindtap="handleCopy" class="btn">复制</view>
          </view>
          <view class="container">
            <view class="location">{{ info.address }}</view>
          </view>
        </view>
      </view>
    </view>
    <t-image-viewer initial-index="{{ imageIndex }}" visible="{{ visible }}" images="{{ info.styleAtlasList }}" bind:tap="onClose" bind:close="onClose"></t-image-viewer>
  </scroll-view>
</view>
<login-dialog visible="{{ loginDialogVisible }}" bind:loginSuccess="loginSuccess" />
