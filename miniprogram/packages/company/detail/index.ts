import {
  collect,
  getCompanyAnnouncementList,
  getCompanyDetail,
  getCompanyFilterList,
  getCompanyJobList,
  getActivityList
} from '@/api/company'
import { defaultDuration } from '@/settings'
import { showToast } from '@/utils/util'
import { checkLogin } from '@/utils/store'
import { jump, sceneStringToObj } from '@/utils/url'

// pages/companyDetail/index.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    info: <any>{},
    list: [],
    areaList: [],
    jobTypeList: [],
    majorList: [],
    educationTypeList: [],
    keys: {
      label: 'v',
      value: 'k'
    },
    visible: false,
    loginDialogVisible: false,
    areaVisible: false,
    jobCategoryVisible: false,
    majorVisible: false,
    educationVisible: false,
    isSticky: false,
    showMore: false,
    textStatus: false,
    isCollect: false,
    isEnded: false,
    isLinear: false,
    isShowOnline: true,
    type: '1',
    viewOffsetTop: 0,
    imageIndex: 0,
    textHeight: 0,
    textLineHeight: 0,
    menuHeight: 0,
    maxLine: 8,
    jobTotal: 0,
    page: 1,
    style: '',
    id: '',
    scollStyle: '',
    coverStyle: '',
    defaultBanner: '//img.gaoxiaojob.com/uploads/mini/company/company-primary-bg.png',
    banner: '',
    cityId: [],
    jobCategoryId: [],
    educationId: [],
    majorId: [],
    cityText: '地区',
    jobCategoryText: '职位类型',
    majorText: '专业',
    educationText: '学历',
    more: '展开内容',
    startY: 0,
    isCoverShow: false,
    hasLogin: false,
    moreText: '职位'
  },

  onClick(event: WechatMiniprogram.CustomEvent) {
    const { index } = event.currentTarget.dataset

    this.setData({ visible: true, isShowOnline: false, imageIndex: index })
  },

  onClose() {
    this.setData({ visible: false, isShowOnline: true })
  },

  onTouchStart(e: any) {
    this.setData({ startY: e.touches[0].clientY })
  },

  showMoreText() {
    let text = ''
    const { type } = this.data
    switch (type) {
      case '1':
        text = '职位'
        break
      case '2':
        text = '公告'
        break
      case '3':
        text = '活动'
        break
      default:
        break
    }
    this.setData({
      moreText: text
    })
  },

  handleCopy(event: WechatMiniprogram.CustomEvent) {
    const { content, type } = event.currentTarget.dataset
    let tit = type === 1 ? '链接已复制，请在浏览器中打开查看' : '内容已复制'
    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({ title: tit, icon: 'none', duration: defaultDuration })
      }
    })
  },

  handleCoverDown() {
    this.setData({ scollStyle: '', coverStyle: '', isCoverShow: false })
  },

  handleCoverUp() {
    this.setData({
      scollStyle: `height:calc(100% - ${this.data.menuHeight}px)`,
      coverStyle: 'background-color:rgba(51,51,51,0.6);transition: background-color .5s;',
      isCoverShow: true
    })
  },

  onTouchMove(e: any) {
    const { startY } = this.data
    const currentY = e.touches[0].clientY
    const deltaY = currentY - startY

    if (deltaY > 30) {
      this.handleCoverDown()
    } else if (deltaY < -30) {
      // 100%-胶囊高度
      this.handleCoverUp()
    }
  },

  getIntroduceHeight() {
    const query = wx.createSelectorQuery().select('#introduce-text')
    query
      .fields({ computedStyle: ['line-height'] }, (res) => {
        this.setData({ textLineHeight: parseFloat(res['line-height']) })
      })
      .exec()

    query
      .boundingClientRect((rect: any) => {
        const height = rect.height
        this.setData({ textHeight: height }, () => {
          // 超过8行显示展开按钮，设置高度隐藏多余内容
          if (height > this.data.textLineHeight * this.data.maxLine) {
            const showHeight = Math.floor(this.data.textLineHeight * this.data.maxLine)
            const textStyle = `height:${showHeight}px;overflow:hidden;`
            this.setData({ showMore: true, textStyle, style: textStyle })
          }
        })
      })
      .exec()
  },

  handlePick(event: WechatMiniprogram.CustomEvent) {
    const { type } = event.currentTarget.dataset
    this.setData({ [`${type}Visible`]: true })
  },

  handleView(event: WechatMiniprogram.CustomEvent) {
    const { type } = this.data
    const { item } = event.currentTarget.dataset
    const { id = '', miniUrl = {} } = item
    let url = ''
    if (type === '1') {
      url = `/packages/job/detail/index?id=${id}`
    } else if (type === '2') {
      url = `/packages/announcement/detail/index?id=${id}`
    } else {
      if (miniUrl.targetType === 0) {
        wx.setClipboardData({
          data: miniUrl.url,
          success() {
            showToast('该链接暂不支持小程序直接访问，已复制可浏览器打开')
          }
        })
      } else {
        jump(miniUrl.url, miniUrl.targetType)
      }
    }
    wx.navigateTo({ url })
  },

  handlePickerChange(event: WechatMiniprogram.CustomEvent) {
    const { type } = event.currentTarget.dataset

    const { label, value } = event.detail

    this.setData({ [`${type}Id`]: value })
    this.getCompanyList()
  },

  async handleCollect() {
    if (!checkLogin()) {
      this.setData({ loginDialogVisible: true })
      return
    }
    const { isCollect, id } = this.data
    await collect({ companyId: id })
    if (isCollect) {
      showToast({ title: '已取消关注', duration: defaultDuration })
    } else {
      showToast({ title: '关注成功', duration: defaultDuration })
    }
    this.setData({ isCollect: !isCollect })
  },

  showText() {
    const { style, textStatus } = this.data

    this.setData({
      textStatus: !textStatus,
      textStyle: textStatus ? 'height:auto;overflow:auto;' : style,
      more: textStatus ? '收起内容' : '展开内容',
      isLinear: textStatus ? true : false
    })
  },

  onScroll(event: WechatMiniprogram.CustomEvent) {
    const {
      detail: { scrollTop }
    } = event
    this.getOffsetTop()

    this.setData({ isSticky: scrollTop >= this.data.viewOffsetTop })
  },

  getNavBarHeight() {
    const { top, height } = wx.getMenuButtonBoundingClientRect()
    // 高度+距离顶部+4=胶囊的高度
    this.setData({ menuHeight: top + height + 4 })
  },

  async getCompanyList(replace = true) {
    const { page, id, type, cityId, educationId, majorId, jobCategoryId, isEnded } = this.data
    let api: any = ''
    if (isEnded && !replace) return

    switch (type) {
      case '1':
        api = getCompanyJobList
        break
      case '2':
        api = getCompanyAnnouncementList
        break
      case '3':
        api = getActivityList
        break
      default:
        break
    }

    const postData = {
      id,
      page,
      cityId: cityId.length ? cityId.join() : '',
      educationId: educationId.length ? educationId.join() : '',
      majorId: majorId.length ? majorId.join() : '',
      jobCategoryId: jobCategoryId.length ? jobCategoryId.join() : ''
    }
    const { list } = await api(postData)
    this.setData({ list: replace ? list : this.data.list.concat(list), isEnded: list.length < 10 ? true : false })
  },

  handleBottom() {
    // 如果未登录，停止加载更多
    if (!this.data.hasLogin) return
    const { page } = this.data
    this.setData({ page: page + 1 })
    this.getCompanyList(false)
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options: any) {
    let id = ''
    if (options.scene) {
      const sceneObj = sceneStringToObj(options.scene)
      id = sceneObj.id
    } else {
      id = options.id
    }

    const { info } = await getCompanyDetail({ id })
    if (info.headBannerUrl) {
      this.setData({ banner: info.headBannerUrl })
    } else {
      this.setData({ banner: this.data.defaultBanner })
    }
    this.setData({ info, id, isCollect: info.isCollect === 1 ? true : false }, () => {
      this.getIntroduceHeight()
    })

    this.getCompanyList()
    const { areaList, jobTypeList, majorList, educationTypeList } = await getCompanyFilterList({ id })
    this.setData({ areaList, jobTypeList, majorList, educationTypeList })

    const loginState = await checkLogin()
    this.setData({ hasLogin: loginState })
  },

  handleLogin(e: WechatMiniprogram.CustomEvent) {
    // console.log(e.detail.type);
    const { type } = e.detail
    if (!checkLogin()) {
      this.setData({ loginDialogVisible: true })
    }
  },

  loginSuccess() {
    this.setData({ hasLogin: true })
  },

  reset() {
    this.setData({
      page: 1,
      majorId: [],
      majorText: '专业',
      educationId: [],
      educationText: '学历',
      jobCategoryId: [],
      jobCategoryText: '职位类型',
      cityId: [],
      cityText: '地区'
    })
    this.getCompanyList()
  },

  handleTabChange(event: WechatMiniprogram.CustomEvent) {
    if (!this.data.isCoverShow) {
      this.handleCoverUp()
    }
    const { type } = event.currentTarget.dataset
    if (type === this.data.type) return
    this.setData(
      {
        type,
        list: []
      },
      () => {
        this.showMoreText()
      }
    )
    this.reset()
  },

  onMoreBoxTouch() {
    if (this.data.isCoverShow) {
      this.handleCoverDown()
    } else {
      this.handleCoverUp()
    }
  },

  getOffsetTop() {
    this.createSelectorQuery()
      .select('.header')
      .boundingClientRect()
      .exec(([rects]) => {
        this.setData({ viewOffsetTop: rects.top })
      })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getNavBarHeight()
    this.getOffsetTop()
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '这家单位正在火热招聘中，快来看看吧～'
    }
  },
  onShareTimeline() {
    return {
      title: this.data.info.companyName
    }
  }
})
