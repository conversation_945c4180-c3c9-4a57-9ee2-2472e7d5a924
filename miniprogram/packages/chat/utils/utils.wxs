// @ts-check

function getCenterMessage(data) {
  var centerMessageItemList = ['system', 'jobCard']
  var isCenter = false

  isCenter = centerMessageItemList.indexOf(data.type) === -1 ? false : true

  return isCenter
}

function getMessageClassName(data, id) {
  var className = ''

  if (getCenterMessage(data)) {
    className = 'item-center'
  } else {
    className = id === data.memberId ? 'item-myself' : 'item-friend'
  }

  return className
}

function isInviteCard(type) {
  var inviteCardType = ['inviteJobRequestCard', 'inviteJobRequestIgnore', 'inviteJobRequestInterest']

  return inviteCardType.indexOf(type) === -1 ? false : true
}

module.exports = {
  getMessageClassName: getMessageClassName,
  getCenterMessage: getCenterMessage,
  isInviteCard: isInviteCard
}
