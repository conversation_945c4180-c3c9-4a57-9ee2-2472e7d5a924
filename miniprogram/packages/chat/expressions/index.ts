import { delPhrase, getPhraseList } from '@/api/chat'

// packages/chat/expressions/index.wxml.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    delConfirm: false,
    expressionsVisible: false,
    isLimit: false,
    confirmBtn: '确定',
    cancelBtn: '取消',
    expressionText: '',
    id: '',
    maxCount: 0,
    phraseList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  async fetchData() {
    const { list, maxCount } = await getPhraseList()
    const isLimit = list.length === maxCount
    this.setData({ phraseList: list, maxCount, isLimit })
  },

  async handleDelete(event: WechatMiniprogram.CustomEvent) {
    const { id } = event.currentTarget.dataset

    this.setData({ delConfirm: true, id })
  },

  closeDialog() {
    this.setData({ delConfirm: false })
  },

  async confirmDel() {
    await delPhrase({ id: this.data.id })

    this.closeDialog()

    this.fetchData()
  },

  handleExpression(event: WechatMiniprogram.CustomEvent) {
    const { content, id } = event.currentTarget.dataset

    this.setData({ expressionsVisible: true, expressionText: content, id: id ?? '' })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载确定删除该常用语吗？


   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
