@use 'styles/variables' as *;
page {
  background-color: $page-background;
}

.expressions-container {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
}

.expressions-list {
  flex: 1;
  padding: 0 40rpx 20rpx;

  .expressions-item {
    background-color: $color-white;
    border-radius: 10px;
    margin-top: 20rpx;
    padding: 30rpx 20rpx 0;
  }

  .content {
    line-height: 1.5;
    border-bottom: 2rpx solid $border-color;
    padding-bottom: 10rpx;
  }

  .operation {
    text-align: right;
    padding: 10rpx;

    button {
      font-weight: normal;
    }

    .del {
      color: $color-point;
    }
  }
}

.expressions-bottom {
  position: sticky;
  bottom: 0;
  padding: 30rpx 55rpx;
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background-color: $color-white;
  border-top: 2rpx solid $border-color;
}
