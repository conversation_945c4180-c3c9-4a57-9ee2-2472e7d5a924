<view class="expressions-container">
  <t-navbar class="block" title="常用语设置" left-arrow />

  <view class="expressions-list">
    <view class="expressions-item" wx:for="{{ phraseList }}" wx:item="item" wx:key="id">
      <view class="content"> {{ item.content }} </view>

      <view class="operation">
        <t-button theme="primary" size="extra-small" variant="text" bindtap="handleExpression" data-content="{{ item.content }}" data-id="{{ item.id }}">编辑</t-button>
        <t-button class="del" size="extra-small" variant="text" bindtap="handleDelete" data-id="{{ item.id }}">删除</t-button>
      </view>
    </view>
  </view>

  <view class="expressions-bottom">
    <t-button theme="primary" block icon="add" bindtap="handleExpression" disabled="{{ isLimit }}">添加常用语({{ phraseList.length }}/{{ maxCount }})</t-button>
  </view>
</view>

<t-dialog visible="{{ delConfirm }}" title="提示" content="确定删除该常用语吗？" confirm-btn="{{ { content: '确定', variant: 'base' } }}" cancel-btn="取消" bind:confirm="confirmDel" bind:cancel="closeDialog" variant="outline" />
<common-expression visible="{{ expressionsVisible }}" text="{{ expressionText }}" pressionsId="{{ id }}" bind:update="fetchData" />
