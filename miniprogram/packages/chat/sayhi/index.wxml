<view class="expressions-container">
  <t-navbar class="block" title="打招呼语设置" left-arrow />
  <view class="switch">
    <t-cell title="启动打招呼语设置" bordered="{{false}}">
      <t-switch slot="note" custom-value="{{ [1, 2] }}" value="{{ isGreeting }}" bindchange="handleChange" />
    </t-cell>
    <t-cell t-class="cell-padding" title="" description="启用后，设定的打招呼语将会在您发起新招呼后自动发送给招聘方" />
  </view>
  <view wx:if="{{isGreeting === 1}}" class="expressions-list">
    <view class="expressions-item" wx:for="{{ phraseList }}" wx:item="item" wx:key="id">
      <view class="content"> {{ item.content }} </view>

      <view class="operation">
        <t-radio block="{{false}}" label="设为默认" data-index="{{index}}" checked="{{item.active}}" data-id="{{ item.id }}" data-type="{{ item.type }}" bindtap="radioChange" />
        <block wx:if="{{item.type === 2}}">
          <t-button theme="primary" size="extra-small" variant="text" bindtap="handleExpression" data-content="{{ item.content }}" data-id="{{ item.id }}">编辑</t-button>
          <t-button class="del" size="extra-small" variant="text" bindtap="handleDelete" data-id="{{ item.id }}">删除</t-button>
        </block>
      </view>
    </view>
  </view>

  <view wx:if="{{isGreeting === 1}}" class="expressions-bottom">
    <t-button theme="primary" block icon="add" bindtap="handleExpression" disabled="{{ isLimit }}">新增打招呼语</t-button>
  </view>
</view>

<t-dialog visible="{{ delConfirm }}" title="提示" content="确定删除该打招呼语吗？" confirm-btn="{{ { content: '确定', variant: 'base' } }}" cancel-btn="取消" bind:confirm="confirmDel" bind:cancel="closeDialog" variant="outline" />
<sayhi-cpn visible="{{ expressionsVisible }}" text="{{ expressionText }}" pressionsId="{{ id }}" bind:update="fetchData" />
