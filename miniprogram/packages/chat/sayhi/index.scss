@use 'styles/variables' as *;
page {
  background-color: $page-background;
}

.expressions-container {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
}

.switch {
  .t-cell__title-text {
    font-weight: bold;
  }
  .cell-padding {
    padding: var(--td-cell-vertical-padding, 0rpx) var(--td-cell-horizontal-padding, 32rpx) var(--td-cell-vertical-padding, 30rpx);
  }
}

.expressions-list {
  flex: 1;
  padding: 0 40rpx 20rpx;

  .expressions-item {
    background-color: $color-white;
    border-radius: 10px;
    margin-top: 20rpx;
    padding: 30rpx 20rpx 0;
  }

  .content {
    line-height: 1.5;
    border-bottom: 2rpx solid $border-color;
    padding-bottom: 10rpx;
  }

  .operation {
    text-align: right;
    padding: 10rpx;
    .t-radio {
      margin-top: 4rpx;
      margin-right: 10rpx;
      display: inline-flex;
      align-items: center;
    }
    .t-radio__icon {
      width: 30rpx;
      height: 30rpx;
      margin-right: 12rpx;
    }
    .t-radio__title {
      font-size: 28rpx;
      color: #333333;
      opacity: 0.8;
    }

    .t-radio__icon-circle {
      width: 30px;
      height: 30px;
      background-color: $color-white;
    }

    .t-radio__icon {
      font-size: 28rpx;
    }

    .t-radio__title--checked {
      color: #FFA000;
    }
    
    button {
      font-weight: normal;
    }

    .del {
      color: $color-point;
    }
  }
}

.expressions-bottom {
  position: sticky;
  bottom: 0;
  padding: 30rpx 55rpx;
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background-color: $color-white;
  border-top: 2rpx solid $border-color;
}
