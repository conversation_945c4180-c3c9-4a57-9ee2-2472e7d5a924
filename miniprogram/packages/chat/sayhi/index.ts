import { delGreeting, getGreetingList, editDefaultGreeting, setGreetingState } from '@/api/chat'
import { showToast } from '@/utils/util'
// packages/chat/expressions/index.wxml.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    delConfirm: false,
    expressionsVisible: false,
    isLimit: false,
    confirmBtn: '确定',
    cancelBtn: '取消',
    expressionText: '',
    id: '',
    maxCount: 0,
    isGreeting: 1,
    phraseList: [] as any
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  async fetchData() {
    const { list, maxCount, isGreeting, customCount } = await getGreetingList()
    const isLimit = customCount >= maxCount
    this.setData({ phraseList: list, maxCount, isLimit, isGreeting: isGreeting - 0 })
  },

  async handleDelete(event: WechatMiniprogram.CustomEvent) {
    const { id } = event.currentTarget.dataset

    this.setData({ delConfirm: true, id })
  },

  closeDialog() {
    this.setData({ delConfirm: false })
  },

  async confirmDel() {
    await delGreeting({ id: this.data.id })

    this.closeDialog()

    this.fetchData()
  },

  handleExpression(event: WechatMiniprogram.CustomEvent) {
    const { content, id } = event.currentTarget.dataset

    this.setData({ expressionsVisible: true, expressionText: content, id: id ?? '' })
  },

  async radioChange(event: WechatMiniprogram.CustomEvent) {
    const { index, id, type } = event.currentTarget.dataset;
    // console.log(index);
    // 使用 map 来创建一个新的数组
    let newArr = this.data.phraseList.map((item:any, i) => {
      // 判断当前的 index 是否与遍历的索引相同
      if (index === i) {
        item.active = true;
      } else {
        item.active = false;
      }
      return item;  // 返回修改后的 item
    });
    // console.log(newArr);
    const res = await editDefaultGreeting({ id, type})
    if (res) {
      showToast(`设置成功！`)
      // 更新数据到页面
      this.setData({
        phraseList: newArr
      });
    }
  },

  async handleChange(event: WechatMiniprogram.CustomEvent) {
    const res = await setGreetingState({})
    if (res) {
      this.setData({
        isGreeting: event.detail.value,
      });
    }
  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载确定删除该常用语吗？


   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
