<wxs src="../utils/utils.wxs" module="utils" />

<view class="chat-container" wx:if="{{ !loading }}">
  <view class="fix">
    <t-navbar left-arrow>
      <view slot="left" class="custom-title">
        <text class="name">{{ chatInfo.memberName }}</text>

        <text class="info">
          <text class="company">{{ chatInfo.companyName }}</text>
          <text class="department">｜{{ chatInfo.department }}</text>
        </text>
      </view>
    </t-navbar>

    <navigator class="job-info" url="/packages/job/detail/index?id={{ chatInfo.jobId }}">
      <view class="title">沟通职位：</view>
      <view class="name">{{ chatInfo.jobName }}</view>
      <view class="status" wx:if="{{ !chatInfo.onlineStatus }}">{{ chatInfo.jobStatusText }}</view>
    </navigator>

    <view wx:if="{{ chatInfo.markInfo && isShowTips}}">
      <view class="job-status" wx:if="{{ chatInfo.markInfo.eventKey === 'jobStop' }}"> {{ chatInfo.markInfo.text }} </view>
      <view class="resume" wx:else>
        <view class="text">
          <text>{{ chatInfo.markInfo.text }}</text>
          <navigator wx:if="{{chatInfo.markInfo.eventKey === 'resumeComplete'}}" url="/packages/resume/index" class="path">{{ chatInfo.markInfo.btnText }}</navigator>
          <text wx:elif="{{chatInfo.markInfo.eventKey === 'createBindQrcode'}}" class="path" bindtap="handleToOpen">{{ chatInfo.markInfo.btnText }}</text>
        </view>
        <view wx:if="{{chatInfo.markInfo.eventKey === 'createBindQrcode'}}" class="close" bindtap="handleCloseTips"></view>
      </view>
    </view>
  </view>

  <scroll-view scroll-y="{{ true }}" class="content" scroll-with-animation="{{ scrollWithAnimation }}" scroll-top="{{ scrollTop }}" bindscrolltoupper="handleScrollTop" bindtap="handleClick">
    <view class="scorll" id="scroll-view">
      <view wx:for="{{ messageList }}" wx:key="index" wx:item="item" class="message-item {{ utils.getMessageClassName(item, memberId) }}">
        <!-- 时间消息 start -->
        <view class="item-datetime" wx:if="{{ item.time }}">{{ item.time }}</view>
        <!-- 时间消息 end -->

        <view class="center-message" wx:if="{{ utils.getCenterMessage(item) }}">
          <!-- 系统消息 start -->
          <view wx:if="{{ item.type === 'system' }}" class="system-message">{{ item.content.text }}<text class="to-edit" wx:if="{{item.content.eventKey === 'systemGreetingEvent'}}" bindtap="handleToEdit">去修改</text></view>
          <!-- 系统消息 end -->

          <!-- 职位卡片消息 start -->
          <navigator wx:if="{{ item.type === 'jobCard' }}" class="is-job-card" url="/packages/job/detail/index?id={{ item.jobId }}">
            <view class="info">
              <view class="name">{{ item.content.jobName }}</view>
              <view class="salary">{{ item.content.wage }}</view>
            </view>

            <view class="announcement" wx:if="{{ item.content.announcementTitle }}">{{ item.content.announcementTitle }}</view>

            <view class="tag-content">
              <view class="tag" wx:if="{{ item.content.education }}">{{ item.content.education }}</view>
              <view class="tag" wx:if="{{ item.content.amount }}">{{ item.content.amount }}</view>
              <view class="tag" wx:if="{{ item.content.experience }}">{{ item.content.experience }}</view>
              <view class="tag" wx:if="{{ item.content.cityName }}">{{ item.content.cityName }}</view>
            </view>

            <view class="company">
              <view class="member">{{ item.content.companyName }}</view>
              <view class="time">{{ item.content.simpleRefreshDate }}</view>
            </view>

            <view class="from">{{ item.content.footerText }}</view>
          </navigator>
          <!-- 职位卡片消息 end -->
        </view>

        <view class="message" wx:else>
          <!-- 用户头像 start -->
          <image wx:if="{{ item.avatar }}" class="avatar" src="{{ item.avatar }}" />
          <!-- 用户头像 end -->

          <!-- 文本消息 start -->
          <text class="is-text" wx:if="{{ item.type === 'text' }}" user-select="{{ true }}">{{ item.content.text }}</text>
          <!-- 文本消息 end -->

          <!-- 在线简历卡片 start -->
          <navigator wx:if="{{ item.type === 'resumeCard' }}" url="/packages/resume/index" class="resume-card">
            <view class="resume-header">
              <view class="title">{{ item.content.title }}</view>
              <view class="description">{{ item.content.content }}</view>
            </view>

            <t-button hover-class="none" bindtap="handleResume" block>{{ item.content.btnText }}</t-button>
          </navigator>
          <!-- 在线简历卡片 end -->

          <!-- 附件补充卡片 start -->
          <view wx:if="{{ item.type === 'agreeRequestFileCard' }}" class="agree-card">
            <view class="content-tips">{{ item.content.content }}</view>

            <t-button hover-class="none" block bindtap="handleSendAttachement">{{ item.content.btnText }}</t-button>
          </view>
          <!-- 附件补充卡片 end -->

          <!-- 文件消息 start -->
          <view class="file-card" wx:if="{{ item.type === 'file' }}" bindtap="handlePreview" data-url="{{ item.content.miniUrl }}" data-type="{{ item.content.fileType }}">
            <view class="name">{{ item.content.fileName }}</view>

            <view class="size">{{ item.content.size }}</view>
          </view>
          <!-- 文件消息 end -->

          <!-- 完善简历卡片 start -->
          <navigator wx:if="{{ item.type === 'resumeCompleteCard' }}" url="/packages/resume/index" class="resume-complete-card">
            <view class="resume-header">
              <view class="title">{{ item.content.title }}</view>
              <view class="description">{{ item.content.content }}</view>
            </view>

            <t-button hover-class="none" bindtap="handleResume" block>{{ item.content.btnText }}</t-button>
          </navigator>
          <!-- 完善简历卡片 end -->

          <!-- 邀请投递卡片 start -->
          <view class="invite-card" wx:if="{{ utils.isInviteCard(item.type) }}">
            <view class="invited-header">{{ item.content.title }}</view>

            <view class="title">{{ item.content.content }}</view>

            <navigator class="job-card" url="/packages/job/detail/index?id={{ item.content.jobId }}" target="_blank">
              <view class="job">
                <view class="job-name">{{ item.content.jobCardInfo.jobName }}</view>
                <view class="salary">{{ item.content.jobCardInfo.wage }}</view>
              </view>

              <view class="info" wx:if="{{ item.content.jobCardInfo.announcementTitle }}">
                {{ item.content.jobCardInfo.announcementTitle }}
              </view>

              <view class="tag-box">
                <view class="tag">{{ item.content.jobCardInfo.cityName }}</view>
                <view class="tag">{{ item.content.jobCardInfo.education }}</view>
                <view class="tag">{{ item.content.jobCardInfo.amount }}</view>
              </view>
            </navigator>

            <view class="button">
              <t-button wx:for="{{ item.content.btnList }}" wx:index="index" wx:key="btn" wx:for-item="btn" class="{{ btn.type }}" disabled="{{ btn.disabled }}" data-messageId="{{ item.messageId }}" data-type="{{ btn.type }}" bindtap="handleInviteClick"> {{ btn.text }} </t-button>
            </view>
          </view>

          <!-- 邀请投递卡片 end -->

          <text class="item-status {{ item.status === '2' ? 'is-read' : '' }}"></text>
        </view>
      </view>
    </view>
  </scroll-view>

  <chat-press id="chatPress" bottomHeight="{{ bottomHeight }}" currentChatId="{{ chatId }}" currentData="{{ chatInfo }}" />

  <t-dialog visible="{{ chatDialogVisible }}" title="{{ title }}" cancel-btn="{{ cancelBtn }}" bind:cancel="handleChatClose">
    <rich-text class="dialog-content" slot="content" nodes="{{ content }}" />
  </t-dialog>

  <qrcode-popup visible="{{ serviceDialogVisible }}" />
</view>
