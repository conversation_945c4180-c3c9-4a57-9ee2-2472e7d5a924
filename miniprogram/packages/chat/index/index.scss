@use 'styles/variables' as *;

.chat-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.t-navbar__left {
  width: 92%;
}

.custom-title {
  max-width: 89%;
  @include utils-ellipsis;

  .name {
    font-weight: bold;
    font-size: 36rpx;
  }

  .info {
    margin-left: 10rpx;
    font-size: 26rpx;
  }
}

.fix {
  position: sticky;
  top: 0;
  background-color: $color-white;

  .job-info {
    display: flex;

    padding: 15rpx 30rpx;
    font-size: 24rpx;

    .title {
      white-space: nowrap;
      &::before {
        content: '•';
        display: inline-block;
        width: 8rpx;
        height: 8rpx;
        margin-right: 15rpx;
        color: $color-primary;
        border-radius: 50%;
      }
    }

    .name {
      color: $color-primary;

      @include utils-ellipsis;
    }

    .status {
      color: $font-color-label;
    }
  }

  .resume {
    display: flex;
    padding: 18rpx 30rpx;
    font-size: 24rpx;
    background-color: #fff3e0;

    .text {
      flex: 1;
      display: flex;
    }

    .path {
      color: $color-primary;
      padding-right: 18rpx;
      background: url('#{$assets}/icon/into.png') no-repeat center right / 10rpx 18rpx;
    }

    .close {
      width: 28rpx;
      height: 28rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/delete-round.png') no-repeat center / contain;
    }
  }

  .job-status {
    padding: 18rpx 30rpx;
    font-size: 24rpx;
    background-color: #fff0ef;
    color: $color-point;
    white-space: nowrap;
  }
}

.content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow-y: auto;
  background-color: $color-background;

  .message-item {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    word-break: break-word;

    .is-text {
      max-width: 72%;
      padding: 20rpx;
    }
  }

  .item-center {
    justify-content: center;
  }

  .item-status {
    align-self: flex-end;
    margin-right: 8px;
    flex: none;

    &::before {
      content: '送达';
      color: $color-primary;
      font-size: 24rpx;
    }
  }

  .message {
    display: flex;
  }

  .is-read {
    &::before {
      content: '';
      display: block;
      width: 22rpx;
      height: 22rpx;
      background: url('#{$assets}/chat/is-read.png') no-repeat center / contain;
    }
  }

  .item-myself {
    flex-direction: row-reverse;

    .message {
      flex-direction: row-reverse;
    }

    .is-text {
      background-color: $color-primary;
      color: $color-white;
      margin-right: 16rpx;
      border-radius: 20rpx 20rpx 0rpx 20rpx;
    }
  }

  .resume-card,
  .resume-complete-card {
    padding: 30rpx 24rpx;
    margin-right: 16rpx;
    width: 480rpx;
    line-height: 1;
    background: linear-gradient(180deg, #fff9f0, $color-white);
    border: 1rpx solid #ffdea6;
    border-radius: $border-radius;
    user-select: none;

    .resume-header {
      display: flex;
      flex-direction: column;
      padding-left: 100rpx;
      background: url('#{$assets}/chat/resume.png') no-repeat left top/80rpx;
    }

    .title {
      @include utils-ellipsis;

      margin-bottom: 20rpx;
      font-size: 28rpx;
      font-weight: bold;
    }

    .description {
      color: $font-color-basic;
      font-size: 24rpx;
      line-height: 40rpx;
    }

    .t-button {
      border-radius: 8rpx;
      height: 70rpx;
      background-color: #fff3e0;
      color: $color-primary;
      margin-top: 36rpx;
      font-size: 28rpx;
    }
  }

  .resume-complete-card {
    margin: 0 0 0 16rpx;
  }

  .invite-card {
    width: 480rpx;
    padding: 20rpx 32rpx;
    background: linear-gradient(180deg, #f9faff, $color-white);
    border: 1rpx solid #e1e3ff;
    border-radius: $border-radius;
    user-select: none;
    margin-left: 16rpx;

    .invited-header {
      font-weight: bold;
      font-size: 32rpx;
      padding: 20rpx 0 20rpx 80rpx;
      background: url('#{$assets}/chat/letter.png') no-repeat left center / 60rpx;
    }

    .title {
      margin-top: 10rpx;
      color: $font-color-basic;
      line-height: 1.5;
      font-size: 26rpx;
    }

    .job-card {
      display: block;
      padding: 20rpx;
      line-height: 1;
      margin-top: 20rpx;
      background: #f4f6fc;
      border-radius: $border-radius;

      .job {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;
        font-size: 28rpx;

        .job-name {
          @include utils-ellipsis;

          font-weight: bold;
        }

        .salary {
          flex: none;
          font-size: 24rpx;
          margin-left: 20rpx;
          color: $color-point;
          font-weight: bold;
        }
      }

      .info {
        @include utils-ellipsis;

        font-size: 24rpx;
        margin-bottom: 20rpx;
      }

      .tag-box {
        color: $font-color-basic;
        display: flex;
        font-size: 20rpx;

        .tag {
          padding: 10rpx;
          background-color: $color-white;
          margin-right: 12rpx;
          border-radius: 8rpx;
        }
      }
    }
    .button {
      display: flex;
      margin-top: 10px;

      .t-button {
        width: 100%;
        height: 70rpx;
        border: none;
        font-weight: bold;
        font-size: 28rpx;
        border-radius: 8rpx;

        & + .t-button {
          margin-left: 20rpx;
        }
      }

      .ignore {
        background-color: #f5f5f5;
        color: $font-color-label;
      }

      .interest {
        background-color: #f4f6fc;
        color: #5760e8;

        &.t-button--disabled {
          color: rgba(#5760e8, 0.6);
        }
      }
    }
  }

  .item-friend {
    .is-text {
      background-color: $color-white;
      border-radius: 20rpx 20rpx 20rpx 0rpx;
      color: $font-color;
      margin-left: 16rpx;
    }

    .item-status {
      visibility: hidden;
    }

    .resume-card {
      margin-left: 16rpx;
    }

    .agree-card {
      padding: 30rpx 24rpx;
      margin-left: 16rpx;
      width: 480rpx;
      border: 1rpx solid #ffdea6;
      border-radius: $border-radius;
      user-select: none;
      background: linear-gradient(180deg, #fff9f0, $color-white);

      .content-tips {
        padding-left: 100rpx;
        background: url('#{$assets}/chat/resume.png') no-repeat left top/80rpx;
      }

      .t-button {
        border-radius: 8rpx;
        height: 70rpx;
        background-color: #fff3e0;
        color: $color-primary;
        margin-top: 36rpx;
        font-size: 28rpx;
      }
    }
  }

  .file-card {
    padding: 30rpx 24rpx 30rpx 122rpx;
    width: 480rpx;
    box-sizing: border-box;
    border: 1rpx solid #e1e3ff;
    border-radius: $border-radius;
    margin-right: 16rpx;
    line-height: 1;

    background:
      url('#{$assets}/chat/attachement.png') no-repeat 24rpx center / 80rpx,
      linear-gradient(180deg, #f9faff, #ffffff);

    .name {
      @include utils-ellipsis;
      margin-bottom: 24rpx;
      font-weight: bold;
    }

    .size {
      color: $font-color-basic;
      font-size: 24rpx;
    }
  }

  .item-datetime {
    margin-bottom: 40rpx;
    width: 100%;
    color: $font-color-tips;
    font-size: 24rpx;
    text-align: center;
    line-height: 1;
  }

  .system-message {
    width: 100%;
    color: $font-color-tips;
    font-size: 24rpx;
    text-align: center;
    line-height: 1;
    .to-edit {
      margin-left: 6rpx;
      color: #ffa000;
    }
  }

  .center-message {
    display: flex;
    width: 100%;
  }

  .is-center-message {
    width: 100%;
  }

  .is-job-card {
    width: calc(100% - 60rpx);
    flex: none;
    padding: 30rpx;
    background-color: $color-white;
    border-radius: $border-radius;
    line-height: 1;

    .info {
      display: flex;
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 30rpx;

      .name {
        flex: 1;

        @include utils-ellipsis;
      }

      .salary {
        color: $color-point;
        white-space: nowrap;
      }
    }

    .announcement {
      @include utils-ellipsis;
      margin-bottom: 20rpx;
    }

    .tag-content {
      display: flex;
      flex-wrap: nowrap;
      font-size: 24rpx;

      .tag {
        margin-bottom: 20rpx;
        line-height: 44rpx;
        padding: 0 12rpx;
        border-radius: 4rpx;
        background-color: $tag-info-background;
        color: $font-color-basic;

        & + .tag {
          margin-left: 12rpx;
        }
      }
    }

    .company {
      display: flex;
      margin-bottom: 30rpx;
      font-size: 24rpx;

      .member {
        flex: 1;

        @include utils-ellipsis;
      }

      .time {
        white-space: nowrap;
        padding-left: 30rpx;
        color: $font-color-tips;
        background: url(#{$assets}/icon/time.png) no-repeat left/24rpx;
      }
    }

    .from {
      padding-top: 18rpx;
      font-size: 24rpx;
      border-top: 1px solid $border-color;
      color: $font-color-label;
    }
  }

  .avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    flex: none;
  }
}

.dialog-content {
  margin-top: 40rpx;
  text-align: center;
  color: $font-color-label;
}
