import { getChatInfo, getHistoryList } from '@/api/chat'
import { baseURL } from '@/settings'
import { getAuthorization, getUserInfo } from '@/utils/store'
import { showLoading, showToast } from '@/utils/util'

Page({
  /**
   * 页面的初始数据
   */

  chatPress: <any>{},

  data: {
    loading: true,
    focus: false,

    renderMessage: true,
    scrollWithAnimation: false,

    chatId: '',
    jobId: '',
    memberId: '',

    showExpress: false,
    showEmoji: false,
    isMaxCount: false,
    expressionsVisible: false,

    chatDialogVisible: false,
    title: '',
    content: '',
    cancelBtn: {},

    scrollTop: 0,
    bottomHeight: '',
    scrollHeight: '100%',

    chatInfo: {},
    emojiList: getApp().emojiList,
    messageList: <any>[],
    phraseList: [],
    serviceDialogVisible: false,
    isShowTips: true
  },

  handleToOpen() {
    //去开启
    this.setData({ serviceDialogVisible: true })
  },

  handleToEdit() {
    //去修改打招呼语
    wx.navigateTo({
      url: '/packages/chat/sayhi/index'
    })
  },

  updateMessage(data: any) {
    const { chatId, jobId, messageId } = data

    if (this.data.chatId !== chatId) return

    const index = this.data.messageList.findIndex((item: any) => item.messageId === messageId)

    if (index !== -1) {
      this.data.messageList.splice(index, 1, data)
    } else {
      this.data.messageList.push(data)
    }

    this.setData({ messageList: this.data.messageList }, async () => {
      if (index !== -1) return

      const height = await this.getScrollViewHeight()

      this.setData({ scrollTop: height })
    })

    if (jobId !== '' && jobId !== this.data.jobId) {
      this.fetchPageData()
      return
    }

    const { socketInstance } = getApp()

    socketInstance.handleChatSetRead(this.data.chatId)
  },

  updateReadStatus(data: any) {
    const { messageList } = this.data
    this.setData({ messageList: messageList.map((item: any) => ({ ...item, status: data.status })) })
  },

  handleScrollTop() {
    if (this.data.renderMessage) return
    this.fetchMessageRecode()
  },

  handleClick() {
    this.chatPress.handlePackUp()
  },

  getScrollViewHeight() {
    return new Promise((resolve) => {
      wx.createSelectorQuery()
        .select('#scroll-view')
        .boundingClientRect((rect) => {
          resolve(rect.height)
        })
        .exec()
    }) as Promise<number>
  },

  async updateMessageList(messageList: any, scrollWithAnimation: boolean, callback: () => void) {
    const height = await this.getScrollViewHeight()

    this.setData({ messageList, scrollWithAnimation }, async () => {
      const newHeight = await this.getScrollViewHeight()
      this.setData({ scrollTop: newHeight - height }, callback)
    })
  },

  showErrorMessage(data: any) {
    const { title, content, buttonText } = data
    const cancel = { content: buttonText, variant: 'base' }
    this.setData({ chatDialogVisible: true, title, content, cancelBtn: cancel })
  },

  handleChatClose() {
    this.setData({ chatDialogVisible: false })
  },

  async fetchMessageRecode(callback = () => {}) {
    const { chatId, messageList } = this.data
    const messageId = messageList.length ? messageList[0].messageId : ''
    const history = await getHistoryList({ chatId, messageId })
    const result = [...history, ...messageList]

    this.updateMessageList(result, messageId !== '', callback)
  },

  handlePreview(event: WechatMiniprogram.CustomEvent) {
    const { token } = getAuthorization()
    const { url, type } = event.currentTarget.dataset
    const path = baseURL + url

    showLoading()

    wx.downloadFile({
      url: path,
      header: { 'Authorization-Token': token },

      success: (res: any) => {
        wx.hideLoading()
        // 特殊处理
        if (res.dataLength === 0) {
          showToast('文件已过期')
          return
        }

        const filePath = res.tempFilePath

        wx.openDocument({
          filePath,
          fileType: type,

          fail: function () {
            showToast('受小程序限制，当前文件格式不支持预览')
          }
        })
      },

      fail: () => {
        wx.hideLoading()
        wx.showToast({ title: '加载失败', icon: 'error', duration: 3000 })
      }
    })
  },

  handleInviteClick(event: WechatMiniprogram.CustomEvent) {
    const { messageid, type } = event.currentTarget.dataset

    const { socketInstance } = getApp()

    type === 'ignore'
      ? socketInstance.handleChatIgnoreInviteJob(this.data.chatId, messageid)
      : socketInstance.handleChatInterestInviteJob(this.data.chatId, messageid)
  },

  handleResume() {
    wx.navigateTo({ url: '/packages/resume/index' })
  },

  handleSendAttachement() {
    this.chatPress.handleAttachement()
  },

  async fetchPageData(callback = () => {}) {
    const { socketInstance } = getApp()
    const { memberId } = getUserInfo()
    const { chatId, jobId } = this.data
    const info = await getChatInfo({ chatId })

    this.setData(
      {
        loading: false,
        chatInfo: info,
        memberId
      },
      () => {
        socketInstance.handleChatSendJobId(chatId, jobId)
        socketInstance.handleChatSetRead(chatId)
        callback()
      }
    )
  },

  getChatPress() {
    this.chatPress = this.selectComponent('#chatPress')
  },

  handleCloseTips() {
    this.setData({ isShowTips: false })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(data: any) {
    const { chatId, jobId } = data

    this.setData({ chatId, jobId })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.fetchPageData(() => {
      this.setData({ renderMessage: true, messageList: [] }, () => {
        this.fetchMessageRecode(() => {
          this.setData({ renderMessage: false })
          this.getChatPress()
        })
      })
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
