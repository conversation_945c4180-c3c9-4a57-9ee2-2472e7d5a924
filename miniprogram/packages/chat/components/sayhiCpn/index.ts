import { addGreeting, editGreeting } from '@/api/chat'

const defaultPlaceholder = '请输入内容，最多200字'

Component({
  /**
   * 页面的初始数据
   */
  properties: {
    visible: { type: Boolean, value: false },
    text: { type: String, value: '' },
    pressionsId: { type: String, value: '' }
  },

  data: {
    dialogVisible: false,
    blockVisible: false,
    expression: '',
    title: '新增打招呼语',
    placeholder: ''
  },

  observers: {
    visible(value: boolean) {
      this.setData(
        { dialogVisible: value, title: this.properties.pressionsId !== '' ? '编辑打招呼语' : '新增打招呼语' },
        () => {
          this.transitionEnd(() => {
            this.setData({
              placeholder: value ? defaultPlaceholder : ''
            })
          })
        }
      )
    },

    text(value: string) {
      this.transitionEnd(() => {
        this.setData({ expression: value })
      })
    }
  },

  methods: {
    transitionEnd(callback: () => void) {
      setTimeout(callback, 350)
    },

    handleClose() {
      this.setData({ dialogVisible: false, expression: '', placeholder: '' })
      this.triggerEvent('update')
    },

    handleChange(val: any) {
      const { value } = val.detail

      this.setData({ expression: value })
    },

    async handleConfirm() {
      if (this.properties.pressionsId) {
        await editGreeting({ id: this.properties.pressionsId, content: this.data.expression })
        this.handleClose()
      } else {
        await addGreeting({ content: this.data.expression })
        this.handleClose()
      }
    }
  }
})
