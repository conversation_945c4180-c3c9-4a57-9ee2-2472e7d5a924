<view class="operation {{ bottomHeight === 0 ? '' : 'is-pinned' }}" style="--container-bottom-padding:{{ bottomHeight }}px " id="operation">
  <view class="press-container">
    <view class="delivery-oprate">
      <view class="delivery" bindtap="handleDelivery">发简历</view>
      <view class="attachement" bindtap="handleAttachement">发附件</view>
    </view>
    <view class="operate">
      <t-button t-class="press-button" theme="primary" bindtap="handleExpressClick">常用语</t-button>
      <t-textarea value="{{ text }}" autosize="{{ true }}" focus="{{ focus }}" maxlength="{{ 600 }}" disable-default-padding="{{ true }}" adjust-position="{{ false }}" confirm-type="send" show-confirm-bar="{{ false }}" t-class="external-class" t-class-textarea="textarea-class" placeholder-style="font-size:28rpx" placeholder="请输入消息内容" bindchange="handleTextChange" bindblur="handleBlur" bindfocus="handleFocus" bindenter="handleSend" />
      <view class="emoji" bindtap="handleEmojiClick"></view>
      <view class="send" bindtap="handleSend" wx:if="{{ showSendButton }}"></view>
      <view class="add" bindtap="handlePlus" wx:else></view>
    </view>
  </view>

  <view class="express" wx:if="{{ showExpress }}">
    <view class="express-list">
      <view class="express-item" wx:for="{{ phraseList }}" wx:key="index" wx:item="item" bindtap="handleExpress" data-text="{{ item.content }}">{{ item.content }}</view>
    </view>

    <view class="express-operate">
      <text class="edit" bindtap="handleOpenExpress">添加常用语</text>
      <text class="set" bindtap="handleToExpress">常用语设置</text>
    </view>
  </view>

  <view class="emoji-list" wx:if="{{ showEmoji }}">
    <view class="list">
      <view class="emoji" wx:for="{{ emojiList }}" wx:key="index" wx:for-item="item" bindtap="handleCheckEmoji" data-value="{{ item }}">
        {{ item }}
      </view>

      <view class="back-space" bindtap="handleBackspace"></view>
    </view>

    <view class="send">
      <t-button theme="primary" bindtap="handleSend" disabled="{{ sendDisabled }}">发送</t-button>
    </view>
  </view>

  <view class="plus" wx:if="{{ showPlus }}">
    <view class="tool-title">更多功能入口</view>

    <view class="tool-content">
      <navigator url="/packages/resume/index" class="item resume">编辑简历 </navigator>
      <navigator url="/packages/chat/sayhi/index" class="item sayhi">打招呼语设置 </navigator>
      <view class="item tool-item" wx:for="{{ toolList }}" wx:key="index" style="background-image: url({{ item.background }});" data-params="{{ item }}" bindtap="handleTool">
        <text class="badge" wx:if="{{ item.badge }}">{{ item.badge }}</text>
        {{ item.label }}
      </view>
    </view>
  </view>
</view>

<delivery-popup id="deliveryPopup" bindclose="handleSuccess" />

<t-dialog visible="{{ beforeApplyDialogVisible }}" title="{{ beforeApplyDialogTitle }}" content="{{ beforeApplyDialogContent }}" confirm-btn="{{ beforeApplyDialogConfirmBtn }}" cancel-btn="{{ beforeApplyDialogCancelBtn }}" bind:confirm="handleCheckDialogConfirm" bind:cancel="handleCheckDialogCancel" />

<t-dialog t-class="attachement-dialog" visible="{{ attachementDialog }}" title="{{ attachementTitle }}" confirm-btn="{{ attachementConfirmBtn }}" cancel-btn="{{ attachementCancelBtn }}" bind:confirm="handleAttachementConfirm" bind:cancel="closeAttachementConfirm">
  <view class="content" slot="content">{{ attachementContent }}</view>
  <rich-text class="tips" slot="content" nodes="{{ attachementTips }}" />
</t-dialog>

<common-expression visible="{{ expressionsVisible }}" bind:update="fetchPhraseList" />

<!-- 竞争力分析 -->
<t-dialog visible="{{ reportDialogVisible }}" title="{{ reportDialogTitle }}" confirm-btn="{{ reportConfirmButton }}" cancel-btn="{{ reportCancelButton }}" bind:confirm="handleReportConfirm" bind:cancel="handleReportCancel">
  <view slot="content">
    <view class="report-dialog-content">{{ reportDialogContent }}</view>
    <view wx:if="{{ reportDialogDescription }}" class="report-dialog-description">{{ reportDialogDescription }}</view>
  </view>
</t-dialog>
