import { beforeFileCheck, getPhraseList } from '@/api/chat'
import { checkJobReport, createJobReport } from '@/api/jobDetail'
import { getVipInfo } from '@/api/person'
import beforeDeliveryMixin from '@/mixins/beforeDeliveryMixin'
import { assetsURL, h5 } from '@/settings'
import { handleUploadMessageFile } from '@/utils/upload'
import { toWebPage } from '@/utils/util'

// packages/chat/components/press/index.ts
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    currentChatId: { type: String, default: '' },
    currentData: { type: Object, default: {} }
  },

  behaviors: [beforeDeliveryMixin],

  /**
   * 组件的初始数据
   */
  data: {
    focus: false,
    showExpress: false,
    showEmoji: false,
    showPlus: false,
    showConfirm: false,
    attachementDialog: false,
    expressionsVisible: false,
    sendDisabled: true,
    showSendButton: false,

    bottomHeight: 0,

    text: '',
    chatId: '',
    companyId: 0,
    jobId: '',
    jobApplyId: '',

    attachementTitle: '',
    attachementContent: '',
    attachementTips: '',
    attachementConfirmBtn: { content: '确定', variant: 'base' },
    attachementCancelBtn: { content: '取消' },

    extension: ['doc', 'docx', 'pdf', 'xls', 'xlsx', 'rar', 'zip', 'jpg', 'png'],
    url: '/chat/upload',

    deliveryInfo: <any>{},
    phraseList: [],
    emojiList: getApp().emojiList,
    deliveryPopup: <any>{},

    toolList: [
      { label: '高才VIP', value: 1, badge: '极速求职', background: `${assetsURL}/chat/vip.png` },
      { label: '竞争力分析', value: 2, badge: '知己知彼', background: `${assetsURL}/chat/competitive-analysis.png` },
      { label: '简历曝光', value: 3, badge: '曝光翻倍', background: `${assetsURL}/chat/resume-exposure.png` }
    ],

    reportDialogVisible: false,
    reportDialogTitle: '',
    reportDialogContent: '',
    reportDialogDescription: '',
    reportConfirmButton: <any>null,
    reportCancelButton: <any>null,
    reportConfirmCallback: () => {}
  },

  pageLifetimes: {
    show() {
      this.setData({ showExpress: false, showEmoji: false, expressionsVisible: false, showPlus: false })
    }
  },

  observers: {
    currentChatId(value: string) {
      this.setData({ chatId: value })
    },

    text(value: string) {
      this.setData({ sendDisabled: !value.trim().length })
      this.setData({ showSendButton: value !== '' })
    },

    currentData(value: any) {
      this.setData({ companyId: value.companyId, jobId: value.jobId })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    load() {
      this.data.deliveryPopup = this.selectComponent('#deliveryPopup')
    },

    handleExpressClick() {
      const { showExpress } = this.data
      const handler = () => this.setData({ showExpress: !showExpress, showEmoji: false, showPlus: false })

      if (showExpress) {
        handler()
        return
      }

      this.fetchPhraseList(handler)
    },

    handleTextChange(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.detail

      this.setData({ text: value })
    },

    handleEmojiClick() {
      this.setData({ showEmoji: !this.data.showEmoji, showExpress: false, showPlus: false })
    },

    handleCheckEmoji(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.currentTarget.dataset
      this.setData({ text: this.data.text + value })
    },

    handleExpress(event: WechatMiniprogram.CustomEvent) {
      const { text } = event.currentTarget.dataset
      this.setData({ text, focus: true })
    },

    handlePlus() {
      this.setData({ showPlus: !this.data.showPlus, showEmoji: false, showExpress: false })
    },

    handleReportConfirm() {
      this.data.reportConfirmCallback()
      this.handleReportCancel()
    },

    handleReportCancel() {
      this.setData({ reportDialogVisible: false })
    },

    async checkJobReport(params: any = {}) {
      const { jobId } = this.data
      const { jumpType, jumpUrl, isConfirm, title, confirmBtnTxt, cancelBtnTxt, tips1, tips2 } = await checkJobReport({
        jobId,
        ...params
      })

      const reportConfirmButton = confirmBtnTxt ? { content: confirmBtnTxt, variant: 'base' } : null
      const reportCancelButton = cancelBtnTxt ? { content: cancelBtnTxt, variant: 'base' } : null

      const reportDialogData = {
        reportDialogVisible: true,
        reportDialogTitle: title,
        reportDialogContent: tips1,
        reportDialogDescription: tips2,
        reportConfirmButton,
        reportCancelButton
      }

      // 跳转类型，1:有弹窗带跳转链接,2:无弹窗带跳转链接,-1:有弹窗无跳转链接,9:可能是简历前三步未完善（🤷）

      if (jumpType === 1) {
        const callback =
          params.isConfirm || isConfirm === 0
            ? async () => {
                const { jumpUrl } = await createJobReport(jobId)

                toWebPage(`${h5}${jumpUrl}`)
              }
            : () => {
                this.checkJobReport({ isConfirm: '1' })
              }

        this.setData({
          ...reportDialogData,
          reportConfirmCallback: callback
        })
        return
      }

      if (jumpType === 2) {
        toWebPage(`${h5}${jumpUrl}`)
        return
      }

      if (jumpType === 9) {
        this.setData({
          ...reportDialogData,
          reportConfirmCallback: () => wx.navigateTo({ url: '/packages/resume/required' })
        })
        return
      }

      if (jumpType === -1) {
        this.setData({
          ...reportDialogData,
          reportConfirmCallback: () => {}
        })
        return
      }
    },

    async handleTool(event: WechatMiniprogram.CustomEvent) {
      const { value } = event.currentTarget.dataset.params

      const { buyUrl } = await getVipInfo()
      const { vip, jobFast } = buyUrl

      const handler = {
        1: () => {
          toWebPage(`${h5}${vip}`)
        },
        2: () => {
          this.checkJobReport()
        },
        3: () => {
          toWebPage(`${h5}${jobFast}`)
        }
      }

      handler[<1 | 2 | 3>value]()
    },

    handleOpenExpress() {
      this.setData({ expressionsVisible: true })
    },

    handleBlur() {
      this.setData({ bottomHeight: 0 })
    },

    handleFocus(event: WechatMiniprogram.CustomEvent) {
      const { height } = event.detail

      this.setData({ bottomHeight: height })

      this.handlePackUp()
    },

    handleToExpress() {
      wx.navigateTo({ url: '/packages/chat/expressions/index' })
    },

    handleBackspace() {
      const { text } = this.data
      const value = [...text]

      if (!text) return

      value.splice(-1, 1)

      this.setData({ text: value.join('') })
    },

    handleSend() {
      const { socketInstance } = getApp()
      const text = this.data.text.trim()

      if (text) {
        socketInstance.handleChatSendText(this.data.chatId, text)

        this.setData({ text: '' })
      }
    },

    handleSuccess(event: WechatMiniprogram.CustomEvent) {
      const { socketInstance } = getApp()
      const { jobApplyId } = event.detail

      socketInstance.handleChatSendResume(this.data.chatId, jobApplyId)
    },

    handleUploadFile() {
      const { socketInstance } = getApp()
      const { url, extension } = this.data

      const callback = (data: any) => {
        socketInstance.handleChatSendFile(this.data.chatId, data[0].data.id)
      }

      const formData = { chatId: this.data.chatId }

      handleUploadMessageFile({ url, count: 1, extension, formData }, callback)
    },

    async fetchPhraseList(callback = () => {}) {
      const { list, maxCount } = await getPhraseList()
      this.setData({ phraseList: list, maxCount: list.length === maxCount }, callback)
    },

    async handleDelivery() {
      this.load()
      this.handleCheckJobInfo(this.data.jobId)
    },

    openDeliveryPopup(data: any) {
      const { jobId } = this.data

      this.data.deliveryPopup.open({ ...data, jobId })
    },

    async handleAttachement() {
      const { title, content, tips, applyId } = await beforeFileCheck({
        jobId: this.data.jobId,
        chatId: this.data.chatId
      })

      if (title) {
        this.setData({
          attachementTitle: title,
          attachementContent: content,
          attachementTips: tips,
          jobApplyId: applyId,
          attachementDialog: true
        })
        return
      }

      this.handleUploadFile()
    },

    closeAttachementConfirm() {
      this.setData({ attachementDialog: false })
    },

    handlePackUp() {
      this.setData({ showEmoji: false, showExpress: false, showPlus: false })
    },

    handleAttachementConfirm() {
      const { socketInstance } = getApp()
      const { chatId, jobApplyId } = this.data

      socketInstance.handleChatRequestFile(chatId, jobApplyId)

      this.closeAttachementConfirm()
    }
  }
})
