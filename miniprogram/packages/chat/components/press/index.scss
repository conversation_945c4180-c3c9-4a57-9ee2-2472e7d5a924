@use 'styles/variables' as *;

.operation {
  position: sticky;
  display: flex;
  flex-direction: column;
  bottom: 0;
  background-color: $color-white;
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;

  &.is-pinned {
    padding-bottom: var(--container-bottom-padding, 0px);
  }

  button {
    width: 87rpx;
    height: 48rpx;
    border-radius: 8rpx;
    font-size: 20rpx;
  }

  .delivery-oprate {
    display: flex;
    margin-bottom: 20rpx;
    font-size: 22rpx;
    line-height: 1;

    view {
      padding: 15rpx 20rpx 15rpx 60rpx;
      border-radius: 26rpx;
      border: 2rpx solid #f6f6f6;
    }

    .delivery {
      background: url(#{$assets}/icon/resume.png) no-repeat left 20rpx center / 28rpx;
    }

    .attachement {
      margin-left: 20rpx;
      background: url(#{$assets}/icon/attachement.png) no-repeat left 20rpx center / 28rpx;
    }
  }

  .press-container {
    padding: 20rpx 30rpx;

    .operate {
      display: flex;
      align-items: center;
    }

    .external-class {
      flex: 1;
      margin: 0 20rpx;
      padding: 14rpx 24rpx;
      background-color: #f7f8fa;
      border-radius: 34rpx;
    }

    .textarea-class {
      max-height: 96rpx;
      font-size: 28rpx;
      line-height: 32rpx;
    }

    .emoji {
      width: 50rpx;
      height: 50rpx;
      background: url(#{$assets}/chat/emoji.png) no-repeat center / contain;
    }

    .add {
      width: 50rpx;
      height: 50rpx;
      margin-left: 16rpx;
      background: url(#{$assets}/icon/add-black.png) no-repeat center / contain;
    }

    .send {
      width: 50rpx;
      height: 50rpx;
      margin-left: 16rpx;
      background: url(#{$assets}/chat/send.png) no-repeat center / contain;
    }
  }

  .express {
    border-top: 2rpx solid $border-color;
    padding: 0 30rpx;
    font-size: 26rpx;
  }

  .express-list {
    max-height: 300rpx;
    overflow-y: auto;
  }

  .express-item {
    padding: 30rpx 0;
    border-bottom: 2rpx solid $border-color;
    @include utils-ellipsis;
  }

  .express-operate {
    padding: 30rpx 0;
    display: flex;
    justify-content: center;
    text {
      display: inline-block;
      box-sizing: border-box;
      text-align: center;
      width: 50%;
      padding: 0 90rpx;
    }

    .edit {
      border-right: 2rpx solid $border-color;
      background: url(#{$assets}/icon/edit-primary.png) no-repeat center left 50rpx / 40rpx;
    }

    .set {
      background: url(#{$assets}/icon/setting-primary.png) no-repeat center left 85rpx / 40rpx;
      padding-left: 130rpx;
      padding-right: 80rpx;
    }
  }

  .emoji-list {
    flex: 1;
    padding: 0 15rpx;
    border-top: 2rpx solid $border-color;

    .list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      position: relative;
      max-height: 370rpx;
      overflow-y: auto;
    }

    .emoji {
      padding: 0 15rpx;
      font-size: 48rpx;
    }

    .back-space {
      position: sticky;
      bottom: 0;
      left: 90%;
      width: 77rpx;
      height: 77rpx;
      background: url(#{$assets}/chat/back-space.png) no-repeat center / contain;
    }

    .send {
      text-align: right;
      border-top: 2rpx solid $border-color;
      padding: 30rpx 0;

      button {
        width: 110rpx;
        height: 56rpx;
        font-size: 24rpx;
      }
    }
  }

  .plus {
    padding: 30rpx;
    font-size: 22rpx;
    border-top: 2rpx solid $border-color;

    .tool-title {
      margin-bottom: 30rpx;
      font-size: 28rpx;
      font-weight: bold;
    }

    .tool-content {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      // padding: 0 20rpx;
      .item  {
        width: 150rpx;
        margin-bottom: 40rpx;
      }
    }

    .resume {
      padding-top: 116rpx;
      width: 100rpx;
      text-align: center;
      background: url(#{$assets}/chat/resume-black.png) no-repeat top / 100rpx;
    }

    .sayhi {
      padding-top: 116rpx;
      width: 100rpx;
      text-align: center;
      background: url(#{$assets}/chat/sayhi.png) no-repeat top / 100rpx;
    }

    .tool-item {
      position: relative;
      padding-top: 116rpx;
      min-width: 100rpx;
      text-align: center;
      background-repeat: no-repeat;
      background-position: top;
      background-size: 100rpx 100rpx;
    }

    .badge {
      position: absolute;
      top: -12rpx;
      right: -3%;
      padding: 0 5rpx;
      color: $color-white;
      font-size: 18rpx;
      line-height: 24rpx;
      background: linear-gradient(90deg, $color-primary, $color-point);
      border-radius: 12rpx 12rpx 12rpx 0px;
    }
  }
}

.attachement-dialog {
  text-align: center;

  .content {
    margin: 20rpx 0;
  }

  .tips {
    font-size: 24rpx;
    color: $font-color-basic;
  }
}

.report-dialog-content {
  color: $font-color-basic;
  font-size: 32rpx;
  text-align: center;
  line-height: 48rpx;
}

.report-dialog-description {
  margin-top: 30rpx;
  color: $color-primary;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
}
