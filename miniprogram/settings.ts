const envOptions = {
  // 正式环境
  release: {
    base: 'https://mini.gaoxiaojob.com',
    h5: 'https://m.gaoxiaojob.com',
    chat: 'wss://chat.gaoxiaojob.com'
  },
  // 测试环境
  test: {
    base: 'https://test.mini.gcjob.jugaocai.com',
    h5: 'https://test.m.gcjob.jugaocai.com',
    chat: 'wss://test.wss.gcjob.jugaocai.com'
  },
  // 开发环境
  dev: {
    base: 'https://dev.mini.gcjob.jugaocai.com',
    h5: 'https://dev.m.gcjob.jugaocai.com',
    chat: 'wss://dev.wss.gcjob.jugaocai.com'
  },
  // 灰度环境
  gray: {
    base: 'https://gray.mini.gcjob.jugaocai.com',
    h5: 'https://gray.m.gcjob.jugaocai.com',
    chat: 'wss://gray.wss.gcjob.jugaocai.com'
  },
  // 预发布环境
  pre: {
    base: 'https://pre.mini.gcjob.jugaocai.com',
    h5: 'https://pre.m.gcjob.jugaocai.com',
    chat: 'wss://pre.wss.gcjob.jugaocai.com'
  },
  // 栋本地
  dong: {
    base: 'https://mini.gaoxiaojob.dong',
    h5: 'https://m.gaoxiaojob.dong',
    chat: 'ws://127.0.0.1:7272'
  }
}

declare type Env = keyof typeof envOptions

/**
 * * 在微信开发者工具中点击“上传”将自动修改为正式环境
 */
export const env: Env = 'dong'

export const { base: baseURL, h5, chat } = envOptions[env]

const accountInfoMiniProgram = (() => {
  const { miniProgram } = wx.getAccountInfoSync()

  return miniProgram
})()

export const version = accountInfoMiniProgram.version

export const envVersionRelease = accountInfoMiniProgram.envVersion === 'release'

export const assetsURL = '//img.gaoxiaojob.com/uploads/mini'

export const timeout = 60000

export const defaultDuration = 3000

export const dictionaryType: { [key: string]: any } = {
  jobType: 21,
  companyNature: 20,
  companyType: 25,
  areaId: 101,
  majorId: 102,
  groupType: 100,
  announcementType: 103
}

export const enum ResumeStatus {
  normal = '1',
  abnormal = '9'
}

export const enum CheckStatus {
  checked = '1',
  uncheck = '2'
}
