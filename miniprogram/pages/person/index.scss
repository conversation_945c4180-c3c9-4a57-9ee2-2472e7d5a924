@use 'styles/variables' as *;

$namespace: person;

@mixin margin-bottom {
  margin-bottom: 20rpx;
}

@mixin background-radius {
  background-color: $color-white;
  border-radius: 16rpx;
}

.#{$namespace}-container {
  padding: 168rpx 0 calc(98rpx + env(safe-area-inset-bottom));
  // height: 100vh;
  line-height: 1;
  background: $color-background url(#{$assets}/person/background.png) no-repeat 0 0 / 100% auto;
  // overflow: hidden;
  box-sizing: border-box;
}

.#{$namespace}-main {
  padding: 0 30rpx 60rpx;
  // height: 100%;
  box-sizing: border-box;
  // overflow: auto;
}

.#{$namespace}-header {
  padding: 4px 0 0 10rpx;
}

.#{$namespace}-basic {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;

  .t-avatar--medium {
    --td-avatar-medium-width: 130rpx;

    margin-right: 20rpx;
    box-shadow: 0 0 0 4px $color-white;
  }

  &.vip-badge {
    .t-avatar__wrapper::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: calc(100% - 57rpx);
      width: 30rpx;
      height: 24rpx;
      background: url(#{$assets}/person/avatar-badge.png) no-repeat center / contain;
    }
  }

  .information {
    flex: 1;
    max-width: calc(100% - 130rpx - 20rpx);
  }

  .name {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 26rpx;
    font-size: 40rpx;
    font-weight: bold;
  }

  .text {
    @include utils-ellipsis;

    max-width: 38%;
  }

  .percent {
    margin-left: 10rpx;
    padding: 6rpx 12rpx;
    color: $color-point;
    font-size: 24rpx;
    font-weight: normal;
    border-radius: 18rpx;
    box-shadow: 0 0 0 2rpx $color-point;
  }

  .edit {
    position: absolute;
    top: -9rpx;
    right: 0;
    padding: 0 36rpx 0 18rpx;
    color: $color-white;
    font-size: 26rpx;
    text-align: center;
    line-height: 56rpx;
    background:
      url(#{$assets}/person/to.png) no-repeat 128rpx center / 10rpx 18rpx,
      linear-gradient(90deg, #ffa000, #ffb73f);
    border-radius: 28rpx;
  }

  .tips {
    font-size: 24rpx;
    line-height: 36rpx;
  }
}

.#{$namespace}-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  text-align: center;

  .stats-item {
    &:last-of-type {
      margin-right: 20rpx;
    }
  }

  .num {
    position: relative;
    padding-top: 20rpx;
    font-size: 40rpx;
    font-weight: bold;

    @include margin-bottom;
  }

  .badge {
    position: absolute;
    top: 0;
    padding: 0 8rpx;
    height: 26rpx;
    color: $color-white;
    font-size: 20rpx;
    line-height: 26rpx;
    background-color: $color-point;
    border-radius: 13rpx;

    &.is-special {
      width: 60rpx;
      height: 32rpx;
      line-height: 32rpx;
      border-radius: 10rpx 10rpx 10rpx 0;
    }
  }

  .txt {
    color: $font-color-basic;
    font-size: 24rpx;
  }
}

.#{$namespace}-content {
  .#{$namespace}-vip {
    margin-top: -14rpx;
    margin-bottom: 20rpx;
    height: 168rpx;
    color: #f3ece2;
    font-size: 24rpx;
    background: url(#{$assets}/person/vip-mark-primary.png?v=1.9) no-repeat 0 0 / 100% auto;

    &.is-not-apple-device {
      background-image: url(#{$assets}/person/vip-mark.png?v=1.9);
    }

    @for $i from 1 to 3 {
      &.is-level-#{$i} {
        margin-bottom: 30rpx;
        padding-top: 34rpx;
        height: 93rpx;
        text-align: center;
        background-image: url(#{$assets}/person/vip-mark-#{$i}.png);
        box-sizing: border-box;
      }
    }
  }

  .#{$namespace}-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include margin-bottom;

    .#{$namespace}-box {
      @include background-radius;

      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      width: 335rpx;
      height: 142rpx;
      box-sizing: border-box;
    }

    .title {
      margin-bottom: 24rpx;
      font-size: 32rpx;
      font-weight: bold;
    }

    .subtitle {
      font-size: 24rpx;
    }

    .image {
      width: 70rpx;
      height: 70rpx;
      object-fit: contain;
    }
  }

  .resume-improved {
    @include margin-bottom;
    @include background-radius;

    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    background-color: $color-primary-background;

    .title {
      @include margin-bottom;

      font-size: 28rpx;
      font-weight: bold;
    }

    .subtitle {
      font-size: 24rpx;
    }

    .txt {
      color: $color-point;
    }

    .to-improved {
      padding: 0 67rpx 0 25rpx;
      height: 56rpx;
      color: $color-white;
      font-size: 26rpx;
      font-weight: bold;
      line-height: 56rpx;
      background: $color-primary url(#{$assets}/person/arrow.png) no-repeat 114rpx center / 30rpx;
      border-radius: 28rpx;
    }
  }

  .#{$namespace}-tool {
    padding: 30rpx;

    @include margin-bottom;
    @include background-radius;

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      font-size: 32rpx;
      font-weight: bold;

      &::after {
        content: '';
        margin-left: 6rpx;
        width: 32rpx;
        height: 32rpx;
        background: url(#{$assets}/person/hot.png) no-repeat right center / contain;
      }
    }

    .tools {
      display: flex;
      flex-wrap: wrap;

      .item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 40rpx;
        width: 25%;
      }

      .icon {
        margin-bottom: 16rpx;
        width: 44rpx;
        height: 44rpx;
        object-fit: contain;
      }

      .name {
        font-size: 26rpx;
      }

      .tips {
        position: absolute;
        top: 24rpx;
        left: 60%;
        padding: 0 6rpx;
        color: $color-white;
        font-size: 20rpx;
        line-height: 24rpx;
        white-space: nowrap;
        background-color: $color-point;
        border-radius: 10rpx 0 10rpx 0;

        &.is-disabled {
          background-color: #ffb0ac;
        }
        &.is-new {
          background: #fff3e0;
          color: $color-primary;
        }
      }
    }
  }

  .#{$namespace}-link {
    margin-bottom: 40rpx;

    @include background-radius;

    .link {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 30rpx;
      height: 108rpx;
    }

    .name {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
    }

    .icon {
      margin-right: 20rpx;
      width: 40rpx;
      height: 40rpx;
      object-fit: contain;
    }

    .hot {
      width: 24rpx;
      height: 28rpx;
      margin-left: 8rpx;
    }

    .aside {
      padding-right: 40rpx;
      background: url(#{$assets}/person/forward.png) no-repeat right center / 26rpx;

      .tips {
        font-size: 24rpx;
        color: $color-point;
        background-color: #fff0ef;
        line-height: 32rpx;
        padding: 0 8rpx;
        border-radius: 10rpx;
      }
    }

    .tips {
      min-height: 28rpx;
      font-size: 28rpx;

      &.is-arrow {
        padding-right: 40rpx;
        background: url(#{$assets}/person/forward.png) no-repeat right center / 26rpx;
      }
    }
  }

  .logout-button {
    color: $font-color-label;
    background-color: $color-white;
  }
}

.refresh-resume {
  position: fixed;
  right: 20rpx;
  bottom: 240rpx;
  padding-left: 57rpx;
  padding-right: 28rpx;
  font-size: 24rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: $color-white url(#{$assets}/person/refresh.png) no-repeat 26rpx center / 24rpx;
  box-shadow: 0rpx 4rpx 10rpx 3rpx rgba(51, 51, 51, 0.14);
  border-radius: 40rpx;
}

.refresh-dialog-content {
  font-size: var(--td-dialog-content-font-size, 32rpx);
  color: var(--td-dialog-content-color, var(--td-font-gray-2, rgba(0, 0, 0, 0.6)));
  text-align: center;
  line-height: var(--td-dialog-content-line-height, 48rpx);
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}
