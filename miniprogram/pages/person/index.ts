import { getResumeCompletePopInfo, setResumeCompletePopInfo } from '@/api/home'
import {
  getJobToolList,
  getPersonConfig,
  getUserInfo,
  getVipInfo,
  refreshResume,
  setResumeShowStatus
} from '@/api/person'
import { checkMemberComplete } from '@/api/apply'
import { envVersionRelease } from '@/settings'
import { ResumeStatus, assetsURL, defaultDuration, h5 } from '@/settings'
import { removeAuthorization } from '@/utils/store'
import { handleTabBar } from '@/utils/tabBar'
import { toWebPage } from '@/utils/util'

const assets = `${assetsURL}/person`

const defaultAvatar = `${assets}/avatar.png`

const refreshDialogConfirmButton = { content: '确定', variant: 'base' }

const refreshDialogConfirmButtonKnow = { content: '我知道了', variant: 'base' }

const refreshDialogConfirmButtonComplete = { content: '去完善', variant: 'base' }

const refreshDialogConfirmButtonOptimization = { content: '立即优化', variant: 'base' }

const refreshDialogCancelButton = { content: '取消', variant: 'base' }

const to = (url: string) => wx.navigateTo({ url })

Page({
  /**
   * 页面的初始数据
   */
  data: {
    showScanLink: !envVersionRelease,

    isLogin: false,

    notLoginCallback: () => {},

    avatar: defaultAvatar,

    name: '',

    defaultName: '',

    intro: '',

    defaultIntro: '',

    percent: 0,

    configPercent: 0,

    resumeStatus: '',

    isAppleDevice: getApp().globalData.isAppleDevice,

    vipMarkText: '',
    vipMarkLevel: '',
    isVip: false,

    showResumeImprovedCard: false,

    unCompleteItem: 0,

    showRefreshButton: false,

    statsList: [
      { name: '我的投递', label: 'delivery', open: true, value: 0, badge: 0, text: '有反馈' },
      { name: '收藏&关注', label: 'collect', open: true, value: 0, badge: 0, text: '' },
      { name: '职位邀约', label: 'invite', open: true, value: 0, badge: 0, text: '' },
      { name: '谁看过我', label: 'view', open: true, value: 0, badge: 0, text: '' }
    ],

    cardToolList: [
      {
        title: '附件简历',
        label: 'attachment',
        check: true,
        subtitle: '提高求职效率',
        image: `${assets}/attachment.png`
      },
      {
        title: '职位订阅',
        label: 'subscribe',
        check: false,
        subtitle: '职位实时推荐',
        image: `${assets}/subscribe.png`
      }
    ],

    jobToolList: [],

    resumePopupData: {},

    linkToolList: [
      { name: '求职意向', label: 'intention', auth: true, icon: `${assets}/intention.png`, tips: '', arrow: true },
      { name: '关注服务号', label: 'service', auth: true, icon: `${assets}/service.png`, tips: '', arrow: true, badge: '' },
      {
        name: '免费领VIP会员',
        label: 'freeVip',
        auth: true,
        icon: `${assets}/free-vip.png`,
        tips: '8大求职权益加速就业',
        arrow: true
      },
      { name: '隐私设置', label: 'privacy', auth: true, icon: `${assets}/privacy.png`, tips: '', arrow: true },
      { name: '联系客服', label: 'contact', auth: false, icon: `${assets}/contact.png`, tips: '', arrow: false }
    ],

    loginDialogVisible: false,

    serviceDialogVisible: false,

    logoutDialogVisible: false,

    refreshDialogVisible: false,
    refreshUrl: '',
    refreshTitle: '',
    refreshContent: '',
    refreshCancelButton: <any>null,
    refreshConfirmButton: refreshDialogConfirmButtonKnow,
    refreshDialogConfirmButtonCallback: () => {},

    resumeTopVisible: false,
    resumeExposureVisible: false,
    resumeCompleteVisible: false
  },

  handleCallback(callback = () => {}, toast = true) {
    const { isLogin } = this.data

    if (isLogin) {
      callback()
    } else {
      this.data.notLoginCallback = () => callback()
      toast && this.setData({ loginDialogVisible: true })
    }
  },

  handleCheckResume(success = () => {}, failure?: () => void) {
    const { resumeStatus } = this.data

    if (resumeStatus === ResumeStatus.normal) {
      success()
    } else {
      typeof failure === 'function' ? failure() : this.handleResumeRequired()
    }
  },

  handleResumeRequired() {
    to('/packages/resume/required')
  },

  handleEditResume() {
    this.handleCheckResume(() => to('/packages/resume/index'))
  },

  handleStats(event: WechatMiniprogram.CustomEvent) {
    const { key } = event.currentTarget.dataset

    this.handleCallback(() => to(`/packages/person/${key}`))
  },

  handleVip() {
    this.handleCallback(async () => {
      const { buyUrl } = await getVipInfo()
      toWebPage(`${h5}${buyUrl.vip}`)
    })
  },

  // 看用户的简历是否完善
  async checkMemberComplete() {
    const res = await checkMemberComplete({})
    const { resumeStepNum } = res
    if (resumeStepNum < 4) {
      wx.navigateTo({ url: '/packages/resume/required' })
    } else {
      wx.navigateTo({ url: '/packages/person/subscribe' })
    }
  },

  handleCardTool(event: WechatMiniprogram.CustomEvent) {
    const { key, check } = event.currentTarget.dataset
    this.handleCallback(() => {
      check
        ? this.handleCheckResume(() => {
            to(`/packages/resume/${key}`)
          })
        : this.checkMemberComplete()
    })
  },

  handleScanLink() {
    wx.scanCode({
      success(data) {
        const { result: url } = data

        wx.navigateTo({ url })
      }
    })
  },

  handleLinkTool(event: WechatMiniprogram.CustomEvent) {
    const { key, value, auth } = event.currentTarget.dataset
    const events = {
      intention: () => {
        this.handleCheckResume(
          () => {
            to('/packages/resume/intention')
          },
          () => {
            to('/packages/resume/tempIntention/index')
          }
        )
      },

      service: () => {
        this.setData({ serviceDialogVisible: true })
      },

      freeVip: () => {
        toWebPage(`${h5}/new-resume-activity/share-detail`)
      },

      privacy: () => {
        to('/packages/person/privacy')
      },

      contact: () => {
        wx.makePhoneCall({ phoneNumber: value })
      }
    }
    const handler = events[<keyof typeof events>key]

    auth ? this.handleCallback(handler) : handler()
  },

  handleCompleteResume() {
    this.handleCheckResume(this.handleEditResume)
  },

  handleRefreshResume() {
    this.handleCallback(this.fetchRefreshResume)
  },

  handleToolClick(event: WechatMiniprogram.CustomEvent) {
    const { params } = event.currentTarget.dataset

    const { name, url } = params
    const popupKey = ['resumeTop', 'resumeExposure']
    const isPopupKey = popupKey.includes(name)

    if (name === 'discovery') {
      wx.navigateTo({ url: '/packages/discover/index/index' })
      return
    }

    this.handleCallback(() => {
      if (isPopupKey) {
        this.setData({
          [`${name}Visible`]: true
        })
        return
      }

      if (url) {
        toWebPage(`${h5}${url}`)
      }
    })
  },

  handleRefreshDialogCancel() {
    this.setData({ refreshDialogVisible: false })
  },

  handleRefreshDialogConfirm() {
    const { refreshDialogConfirmButtonCallback } = this.data
    refreshDialogConfirmButtonCallback()
    this.handleRefreshDialogCancel()
  },

  handleLogin() {
    this.handleCallback()
  },

  handleLoginSuccess() {
    this.data.notLoginCallback()
  },

  handleLoginClose() {
    this.fetchData()
    this.getTabBar().checkMessage()
  },

  handleLogout() {
    this.getTabBar().checkMessage()
    this.setData({ logoutDialogVisible: true })
  },

  // * 取消按钮 为 确认退出
  handleLogoutDialogCancel() {
    const { socketInstance } = getApp()

    removeAuthorization()
    socketInstance?.handleChatClose()
    wx.reLaunch({ url: '/pages/person/index' })
  },

  // * 确认按钮 为 取消退出
  handleLogoutDialogConfirm() {
    this.setData({ logoutDialogVisible: false })
  },

  async fetchRefreshResume() {
    const { type, title, message, url = '' } = await refreshResume()

    this.setData({ refreshUrl: url })

    // 1第一次刷新，2博士研究生完善简历，3简历中心，4再次刷新，5开放简历再刷新一次，9其他
    let options = {}
    let callback = () => {}

    if (type === 9) {
      // 1.8版本后，这种情况下，和之前的弹窗不一样的，所以采取了直接跳转的方式
      wx.showToast({
        title: message,
        icon: 'none',
        duration: defaultDuration,
        success: () => {
          // 设置定时关闭并跳转
          setTimeout(() => {
            toWebPage(`${h5}${url}`)
          }, defaultDuration)
        }
      })
      return
    }

    if (type === 1 || type === 4) {
      options = {
        refreshCancelButton: null,
        refreshConfirmButton: refreshDialogConfirmButtonKnow
      }
    }

    if (type === 2) {
      options = {
        refreshCancelButton: refreshDialogCancelButton,
        refreshConfirmButton: refreshDialogConfirmButtonOptimization
      }
      callback = () => {
        to(url)
      }
    }

    if (type === 3) {
      options = {
        refreshCancelButton: refreshDialogCancelButton,
        refreshConfirmButton: refreshDialogConfirmButtonComplete
      }
      callback = () => {
        to(url)
      }
    }

    if (type === 5) {
      options = {
        refreshCancelButton: refreshDialogCancelButton,
        refreshConfirmButton: refreshDialogConfirmButton
      }
      callback = async () => {
        await setResumeShowStatus()
        this.fetchRefreshResume()
      }
    }

    this.setData({
      ...options,
      refreshTitle: title,
      refreshContent: message,
      refreshDialogConfirmButtonCallback: callback,
      refreshDialogVisible: true
    })
  },

  async fetchJobToolList() {
    const jobToolList = await getJobToolList()

    // 1.9版本加入了新的样式，考虑到兼容性，所以在这里计算
    jobToolList.map((item: any) => {
      const tipsClass = 'tips'
      if (item.status === '1') {
        item.tipsClass = tipsClass
      } else if (item.status === '2') {
        item.tipsClass = tipsClass + ' is-new'
      } else {
        item.tipsClass = tipsClass + ' is-disabled'
      }

      return item
    })

    this.setData({ jobToolList })
  },

  async fetchResumeCompletePopInfo() {
    const data = await getResumeCompletePopInfo()
    // 有头像返回，需要显示，并且需要设置一下已弹窗
    if (data.avatar) {
      data.isVip = this.data.isVip
      this.setData({ resumePopupData: data, resumeCompleteVisible: true })
      setResumeCompletePopInfo()
    }
  },

  async fetchUserInfo() {
    const data = await getUserInfo()
    // 这里可以考虑异步(弹窗可以考虑异步)
    this.fetchResumeCompletePopInfo()
    const { statsList, linkToolList } = this.data
    const {
      avatar,
      username,
      baseInfo,
      unCompleteItem,
      configCompletePercent,
      resumeCompletePercent,
      resumeStatus,
      intentionInfo,
      isVip,
      vipLevel,
      vipExpireDate
    } = data.userInfo
    const {
      collectAmount,
      companyViewCount,
      jobApplyAllCount,
      jobApplyAmount,
      jobInviteAmount,
      jobInviteCount,
      resumeViewAmount
    } = data.statInfo
    const showResumeImprovedCard = resumeCompletePercent < configCompletePercent
    const showRefreshButton = resumeStatus === ResumeStatus.normal

    // 我的投递
    statsList[0].value = jobApplyAmount
    statsList[0].badge = jobApplyAllCount
    // 收藏&关注
    statsList[1].value = collectAmount
    // 职位邀约
    statsList[2].value = jobInviteAmount
    statsList[2].badge = jobInviteCount
    // 谁看过我
    statsList[3].value = resumeViewAmount
    statsList[3].badge = companyViewCount

    linkToolList[0].tips = intentionInfo

    this.setData({
      avatar,
      name: username,
      intro: baseInfo,
      percent: resumeCompletePercent,
      configPercent: configCompletePercent,
      showResumeImprovedCard,
      unCompleteItem,
      showRefreshButton,
      resumeStatus,
      linkToolList,
      statsList,
      isVip,
      vipMarkText: isVip === '1' ? `您的服务将于${vipExpireDate}到期` : '',
      vipMarkLevel: vipLevel
    })
  },

  async fetchData() {
    const { linkToolList } = this.data
    const { isLogin, unLoginText, unLoginTip, followTip, servicePhone, subscriptionServiceIcon } = await getPersonConfig()

    linkToolList[1].tips = followTip
    linkToolList[1].badge = `${assets}/${subscriptionServiceIcon}`
    linkToolList[4].tips = servicePhone

    this.setData({
      isLogin,
      defaultName: unLoginText,
      defaultIntro: unLoginTip,
      linkToolList
    })

    this.handleCallback(this.fetchUserInfo, false)
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getTabBar().checkMessage()

    this.fetchData()
    this.fetchJobToolList()

    handleTabBar(this, { value: 'person' })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.fetchData()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
})
