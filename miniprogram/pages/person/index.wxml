<view class="person-container">
  <view class="person-main">
    <view class="person-header">
      <view class="person-basic {{ vipMarkText ? 'vip-badge' : '' }}" bindtap="handleLogin">
        <t-avatar image="{{ avatar }}" />

        <view class="information">
          <block wx:if="{{ isLogin }}">
            <view class="name">
              <text class="text">{{ name }}</text>

              <text class="percent" wx:if="{{ percent < 100 }}">完善度{{ percent }}%</text>

              <text class="edit" bindtap="handleEditResume">编辑简历</text>
            </view>

            <view class="tips">{{ intro }}</view>
          </block>
          <block wx:else>
            <view class="name">{{ defaultName }}</view>

            <view class="tips">{{ defaultIntro }}</view>
          </block>
        </view>
      </view>

      <view class="person-stats">
        <view class="stats-item" wx:for="{{ statsList }}" wx:key="index" data-key="{{ item.label }}" data-open="{{ item.open }}" bindtap="handleStats">
          <view class="num">
            <text>{{ item.value > 99 ? '99+' : item.value }}</text>
            <text class="badge {{ item.text ? 'is-special' : '' }}" wx:if="{{ item.badge > 0 }}">{{ item.text ? item.text : item.badge > 99 ? '99+' : item.badge }}</text>
          </view>

          <view class="txt">{{ item.name }}</view>
        </view>
      </view>
    </view>

    <view class="person-content">
      <view class="person-vip {{ isAppleDevice ? '' : 'is-not-apple-device' }} is-level-{{ vipMarkLevel }}" bindtap="handleVip">
        <text wx:if="{{ vipMarkText }}" class="vip-level">{{ vipMarkText }}</text>
      </view>

      <view class="person-cell">
        <view class="person-box" wx:for="{{ cardToolList }}" wx:key="index" data-key="{{ item.label }}" data-check="{{ item.check }}" bindtap="handleCardTool">
          <view class="box-text">
            <view class="title">{{ item.title }}</view>
            <view class="subtitle">{{ item.subtitle }}</view>
          </view>

          <image class="image" src="{{ item.image }}" />
        </view>
      </view>

      <view class="resume-improved" wx:if="{{ showResumeImprovedCard }}" bindtap="handleCompleteResume">
        <view class="content">
          <view class="title">当前简历不完善，请前往完善</view>
          <view class="subtitle">简历完善度达<text class="txt">75%</text>方可投递</view>
        </view>

        <text class="to-improved">去完善</text>
      </view>

      <view class="resume-improved" wx:if="{{ showResumeImprovedCard === false && unCompleteItem }}" bindtap="handleCompleteResume">
        <view class="content">
          <view class="title">您的简历有{{ unCompleteItem }}个待优化项</view>
          <view class="subtitle">正在严重影响求职效率</view>
        </view>

        <text class="to-improved">去优化</text>
      </view>

      <view class="person-tool">
        <view class="title"><text>求职工具</text></view>

        <view class="tools">
          <view class="item" wx:for="{{ jobToolList }}" wx:key="index" data-params="{{ item }}" bindtap="handleToolClick">
            <image class="icon" src="{{ item.icon }}" />

            <text class="name">{{ item.title }}</text>

            <text wx:if="{{ item.tips }}" class="{{ item.tipsClass }}">{{ item.tips }}</text>
          </view>
        </view>
      </view>

      <view class="person-link">
        <!-- scan start -->
        <view wx:if="{{ showScanLink }}" class="link" bind:tap="handleScanLink">
          <view class="name">
            <t-icon name="scan" size="20" style="margin-right: 20rpx" />
            <text class="text">Scan</text>
          </view>

          <view class="tips is-arrow"></view>
        </view>
        <!-- scan end -->

        <view class="link" wx:for="{{ linkToolList }}" wx:key="index" data-key="{{ item.label }}" data-value="{{ item.tips }}" data-auth="{{ item.auth }}" bindtap="handleLinkTool">
          <view class="name">
            <image class="icon" src="{{ item.icon }}" />
            <text class="text">{{ item.name }}</text>
            <image wx:if="{{ item.badge }}" class="hot" src="{{ item.badge }}" />
          </view>

          <block wx:if="{{ item.label === 'freeVip' }}">
            <view class="aside">
              <view class="tips">{{ item.tips }}</view>
            </view>
          </block>
          <block wx:else>
            <view class="tips {{ item.arrow ? 'is-arrow' : '' }}">{{ item.tips }}</view>
          </block>
        </view>
      </view>

      <t-button class="logout-button" size="large" block="{{ true }}" wx:if="{{ isLogin }}" bindtap="handleLogout">退出 </t-button>
      <t-button theme="primary" size="large" block="{{ true }}" wx:else bindtap="handleLogin">立即登录</t-button>
    </view>

    <view class="refresh-resume" wx:if="{{ showRefreshButton }}" bindtap="handleRefreshResume">刷新简历</view>
  </view>

  <login-dialog visible="{{ loginDialogVisible }}" bind:close="handleLoginClose" bind:loginSuccess="handleLoginSuccess" />

  <qrcode-popup visible="{{ serviceDialogVisible }}" />

  <t-dialog visible="{{ logoutDialogVisible }}" title="提示" content="是否退出当前账号" cancel-btn="{{ { content: '确定', variant: 'base' } }}" confirm-btn="{{ { content: '取消', variant: 'base' } }}" bindcancel="handleLogoutDialogCancel" bindconfirm="handleLogoutDialogConfirm" />

  <t-dialog visible="{{ refreshDialogVisible }}" title="{{ refreshTitle }}" cancel-btn="{{ refreshCancelButton }}" confirm-btn="{{ refreshConfirmButton }}" bindcancel="handleRefreshDialogCancel" bindconfirm="handleRefreshDialogConfirm">
    <rich-text class="refresh-dialog-content" slot="content" nodes="{{ refreshContent }}"></rich-text>
  </t-dialog>

  <exposure-popup visible="{{ resumeExposureVisible }}"></exposure-popup>
  <resume-top-popup visible="{{ resumeTopVisible }}"></resume-top-popup>
  <resume-complete-popup visible="{{ resumeCompleteVisible }}" data="{{ resumePopupData }}"></resume-complete-popup>
</view>
