import { throttle } from 'throttle-debounce'

import {
  getHomeShowcase,
  getRecommendJobList,
  getTempIntention,
  getResumeIntention,
  getIntentionJob,
  checkShowQrcode
} from '@/api/home'
import { handleTabBar } from '@/utils/tabBar'
import { jump } from '@/utils/url'
import { getIntentionId, getIntentionChanged, setIntentionChanged, checkLogin, getAreaAreaInfo } from '@/utils/store'
import { getCommonShareInfo } from '@/utils/store'
import { checkMemberComplete } from '@/api/apply'
import { showLoading, getElClientRect } from '@/utils/util'
import { clickAdvert, getResumeStepNum, getVipInfo } from '@/api/person'

import { toWebPage } from '@/utils/util'
import { h5 } from '@/settings'

let _this = <any>null
Page({
  /**
   * 页面的初始数据
   */
  data: <any>{
    limit: 5,
    scrollTop: 0,
    // 是否完成首次加载
    isFirstLoadComplete: false,
    // scroll-view高度
    remainHeight: '100%',
    header: {
      style: getApp().globalData.headerStyle,
      opacity: 0
    },
    defaultInterval: 5000,
    // 推荐模块距离swiper高度
    recommendTop: 0,
    // 吸顶元素距离swiper高度
    offsetTop: 0,
    // 是否吸顶
    isSticky: false,
    // 距离顶部多少吸顶
    fixedTop: getApp().globalData.headerOffsetHeight,

    inputSwiperPlaceholder: ['搜索职位、公告、单位', '「编制」岗位搜索'],

    adSwiper: {
      interval: 3000,
      originalList: [],
      // 这个list是给图片显示的
      list: []
    },

    adUnits: {
      adUnitsStyle: '--swiper-height: 144rpx',
      current: 0,
      list: [[]]
    },
    navTab: [],

    matchSwiper: {
      current: 0
    },

    // 是否登录
    isLogin: false,
    loginDialogVisible: false,

    // 是否点击了添加意向
    addIntentionFlag: false,

    // 是否有临时求职意向
    tempIntention: false,
    // 是否完成简历前三步
    completeResume: false,
    resumeCompletePercent: 0,

    // 临时求职意向
    temporary: {
      id: '',
      name: ''
    },
    // 简历求职意向-最多5个
    resumeIntention: [],

    // 暂无求职意向---》通过条件筛选获取职位
    jobParams: {
      areaId: '',
      majorId: '',
      jobType: '',
      educationType: '',
      groupType: '',
      isEstablishment: '',
      applyHeat: '',
      p: 1
    },

    jobParamsTxt: {
      areaTxt: '',
      educationTxt: '',
      majorTxt: '',
      jobTypeTxt: ''
    },

    visible: {
      announcementType: false,
      majorType: false,
      companyType: false,
      groupType: false,

      area: false,
      major: false,
      jobType: false,
      education: false
    },

    qrcodeVisible: false,

    // 通过求职意向获取职位
    intentionJobParams: {
      intentionId: '',
      page: 1,
      type: 1 // 1推荐，2最新
    },
    // 未登录的职位推荐或者登录后匹配规则的数据
    jobList: [],
    // 登录后推荐列表(匹配不到规则的候补数据)
    recommendList: [],

    isRequesting: false,
    isEnd: false,
    isFirstOpen: true,
    isPullDownRefresh: false,
    triggered: true,
    // 一些文本信息,
    jobParamsAreaIdTxt: '地区',
    jobParamsEducationText: '学历',
    jobParamsMajorIdTxt: '学科',
    jobParamsJobTypeTxt: '职位类型',
    jobParamsGroupTypeTxt: '更多',

    isVip: false,
    buyUrl: {
      vip: '',
      jobFast: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    _this = this

    this.setData({
      isLogin: checkLogin()
    })

    showLoading()
    await this.getShowcase()
    // 设置推荐单位轮播高度
    this.setAdUnitsHeight()
    wx.nextTick(() => {
      // 获取吸顶元素距离头部大小
      this.getOffsetTop()
      this.getRemainHeight()
    })

    const { isShow } = await checkShowQrcode()

    this.setData({ qrcodeVisible: isShow === 1 })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    this.getTabBar().checkMessage()
    this.getTabBar().updateCallback(() => {
      this.setData({ scrollTop: 0 })
    })

    handleTabBar(this, { value: 'home' })

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    const { isFirstOpen } = this.data
    if (isFirstOpen) {
      await this.resetList()
      wx.nextTick(() => {
        this.setData({ isFirstOpen: false, isFirstLoadComplete: true })
      })
    } else {
      // 改变了登陆状态
      const isLogin = checkLogin()
      if (isLogin != this.data.isLogin) {
        // 根据状态来判断是否需要重置
        this.setData({ isLogin })
        this.resetList()
      } else {
        if (getIntentionChanged()) {
          this.data.intentionJobParams.intentionId = ''
          // 清空data里面临时求职意向的信息
          this.resetList()
        }
      }
    }

    if (!this.data.isLogin) {
      // this.setData({ limit: 1 })
    } else {
      // 看完善到了第几步
      getResumeStepNum().then((res: any) => {
        if (res.step <= 3) {
          // this.setData({ limit: 1 })
        }
        this.setData({ resumeCompletePercent: res.resumeCompletePercent })
      })
    }

    this.fetchVipInfo()
  },

  fetchVipInfo() {
    this.handleCallback(async () => {
      const { isVip, buyUrl } = await getVipInfo()

      this.setData({ isVip: isVip === '1', buyUrl })
    }, false)
  },

  // 重置获取整个list,正常只在第一次打开还有切换登录状态的时候调用
  async resetList() {
    showLoading()

    if (this.data.isFirstOpen) {
      const areaInfo = getAreaAreaInfo()
      this.setData({
        'jobParams.areaId': areaInfo.id,
        jobParamsAreaIdTxt: areaInfo.name
      })
    }

    const { isLogin } = this.data
    this.setData({
      isEnd: false
    })
    const tempIntentionId = getIntentionId()
    // 重置这个状态
    setIntentionChanged(false)

    if (isLogin) {
      if (!this.data.isPullDownRefresh) {
        const list = await getResumeIntention()
        this.setData({
          resumeIntention: list,
          completeResume: !!list.length
        })

        if (list.length) {
          const [first] = list
          const { id } = first
          this.setData({
            'intentionJobParams.intentionId': id
          })
        }
      } else {
        // 下拉刷新.p变q
        this.setData({
          'intentionJobParams.page': 1
        })
      }
    } else {
      this.setData({
        'intentionJobParams.intentionId': ''
      })
    }
    this.setData({
      tempIntention: !!tempIntentionId
    })

    if (!this.data.intentionJobParams.intentionId && tempIntentionId) {
      await getTempIntention({ id: tempIntentionId }).then((resp: any) => {
        const { jobCategoryName } = resp

        this.setData({
          'temporary.id': tempIntentionId,
          'temporary.name': jobCategoryName,
          'intentionJobParams.intentionId': tempIntentionId
        })
      })
    }

    this.setData(
      {
        'jobParams.p': 1,
        'intentionJobParams.page': 1,
        isEnd: false
      },
      async () => {
        this.setData({
          isPullDownRefresh: false
        })
        await this.getList()
      }
    )
  },

  async getList(isInit = true) {
    if (this.data.isEnd) {
      return
    }
    showLoading()
    // 正在加载中
    this.setData({
      isRequesting: true
    })
    const {
      intentionJobParams,
      intentionJobParams: { intentionId }
    } = this.data

    if (intentionId) {
      const resp = await getIntentionJob(intentionJobParams)
      const { showcaseInfo } = resp
      const { position: index, list: caseInfoList } = showcaseInfo

      let list = resp.list.map((item: any) => {
        item.id = item.jobId
        return item
      })

      if (caseInfoList) {
        list.splice(index, 0, { caseInfoList })
      }

      const recommendList = resp.recommendList.map((item: any) => {
        item.id = item.jobId
        return item
      })

      const { jobList: oJobList, recommendList: oRecommendList } = this.data
      // 这里其实是看page,如果是首页,就直接替换就可以了
      if (this.data.intentionJobParams.page == 1) {
        this.setData({
          jobList: list,
          recommendList
        })
      } else {
        const newJobList = oJobList.concat(list)
        const newRecommendList = oRecommendList.concat(recommendList)
        this.setData({
          jobList: newJobList,
          recommendList: newRecommendList
        })
      }

      if (resp.list.length < 20 && recommendList.length < 20) {
        this.setData({
          isEnd: true
        })
      } else {
        // page + 1
        this.setData({
          'intentionJobParams.page': intentionJobParams.page + 1
        })
      }
    } else {
      const { jobParams } = this.data
      const params: any = {}
      Object.keys(jobParams).forEach((key: string) => {
        const value = jobParams[key]
        params[key] = typeof value === 'object' ? JSON.stringify(value) : value
        // 如果是数组,就变成逗号隔开
        if (Array.isArray(value)) {
          params[key] = value.join(',')
        }
      })

      const result = await getRecommendJobList(params)

      let {
        jobList: list,
        jobList: { length },
        showcaseInfo = {}
      } = result
      const { position: index, list: caseInfoList } = showcaseInfo

      if (caseInfoList) {
        list.splice(index, 0, { caseInfoList })
      }

      list = list.map((item: any) => {
        if (item.jobId) {
          item.id = item.jobId
        }
        return item
      })
      const { jobList } = result

      if (length < 20) {
        this.setData({
          isEnd: true
        })
      } else {
        // page + 1
        this.setData({
          'jobParams.p': jobParams.p + 1
        })
      }
      const { jobList: oJobList } = this.data
      const newJobList = isInit ? jobList : oJobList.concat(list)
      this.setData({
        jobList: newJobList
      })
    }

    wx.hideLoading()
    this.setData({
      isRequesting: false
    })
  },

  handleMatchClick(e: WechatMiniprogram.CustomEvent) {
    const { url, targetLinkType, id } = e.currentTarget.dataset.item
    jump(url, targetLinkType, id)
  },

  handleSuperior(e: WechatMiniprogram.CustomEvent) {
    const { detail } = e
    const { jobParams } = this.data

    this.setData(
      {
        jobParams: {
          ...jobParams,
          ...detail,
          p: 1
        },
        isEnd: false
      },
      () => {
        this.getList()
      }
    )
  },

  async getRemainHeight() {
    const rect: any = await getElClientRect('.flex-grow')
    this.setData({ remainHeight: rect.height + 'px' })
  },

  async getOffsetTop() {
    const announcementFilterEl: any = await getElClientRect('.required-sticky')
    const { top } = announcementFilterEl

    const recommendOutsideEl: any = await getElClientRect('.recommend-outside')
    const { top: recommendTop } = recommendOutsideEl
    this.setData({
      offsetTop: top,
      recommendTop
    })
  },

  adUnitsChange(e: any) {
    const {
      detail: { current }
    } = e
    this.setData({
      ['adUnits.current']: current
    })
  },

  handleMatchChange(e: any) {
    const {
      detail: { current }
    } = e
    this.setData({
      ['matchSwiper.current']: current
    })
  },

  async handleToolClick(e: any) {
    const { isLogin } = this.data
    const {
      currentTarget: {
        dataset: { item }
      }
    } = e

    const { type, url } = item

    let key = ''
    if (type == 4) {
      if (isLogin) {
        // 看用户的简历是否完善
        const res = await checkMemberComplete({})
        const { resumeStepNum } = res
        if (resumeStepNum < 4) {
          wx.navigateTo({ url: '/packages/resume/required' })
          return
        }
        wx.navigateTo({
          url: `/packages/person/subscribe`
        })
      } else {
        this.setData({ loginDialogVisible: true })
      }
      return
    }

    if (type == 5) {
      this.handleCallback(() => {
        const {
          buyUrl: { jobFast }
        } = this.data
        toWebPage(`${h5}${jobFast}`)
      })
      return
    }

    switch (type) {
      case 1:
        key = 'visible.announcementType'
        break
      case 2:
        key = 'visible.majorType'
        break
      case 3:
        key = 'visible.companyType'
        break
      case 6:
        wx.navigateTo({ url })
        break
      default:
        break
    }
    this.setData({ [key]: true })
  },

  filterChange(e: any) {
    const {
      detail: { value },
      currentTarget: {
        dataset: { key, type }
      }
    } = e
    if (type == '2') {
      // wx.switchTab()不支持url传参，所以用reLaunch
      wx.reLaunch({ url: `/pages/announcement/index?announcementType=${value.join()}` })
    } else if (type == '3') {
      wx.reLaunch({ url: `/pages/announcement/index?tabType=2&${key}=${value.join()}` })
    } else {
      wx.navigateTo({
        url: `/packages/search/result/index?searchType=${type}&${key}=${value.join()}`
      })
    }
  },

  jumpToSearch() {
    wx.navigateTo({
      url: '/packages/search/index/index'
    })
  },

  jump(e: any) {
    const {
      currentTarget: {
        dataset: { item }
      }
    } = e
    jump(item.url, item.targetLinkType, item.id)
  },

  onTapBanner(e: any) {
    const { index } = e.detail
    const { originalList } = this.data.adSwiper
    const { url, targetLinkType, id } = originalList[index]
    jump(url, targetLinkType, id)
  },

  onTapAd(e: any) {
    const {
      currentTarget: {
        dataset: {
          item: { url, targetLinkType, id }
        }
      }
    } = e
    jump(url, targetLinkType, id)
  },

  async getShowcase() {
    await getHomeShowcase().then((r: any) => {
      const { banner, quickLink, recommend } = r
      const showBanner = banner.reduce((acc: any, cur: any) => {
        if (cur.imageUrl) {
          acc.push(cur.imageUrl)
        }
        return acc
      }, [])

      this.setData({
        navTab: quickLink,
        'adSwiper.list': showBanner,
        'adSwiper.originalList': banner,
        'adUnits.list': recommend
      })
    })
  },

  setAdUnitsHeight() {
    const {
      adUnits: {
        list: [first = []]
      }
    } = this.data
    const { length = 0 } = first
    const row = Math.ceil(length / 2)
    this.setData({
      'adUnits.adUnitsStyle': `--swiper-height: ${row * 144}rpx`
    })
  },

  handleLoginGuide() {
    const { isLogin } = this.data

    if (isLogin) {
      this.addIntention()
    } else {
      this.setData({
        loginDialogVisible: true,
        addIntentionFlag: true
      })
    }
  },

  handleLoginClose(event: WechatMiniprogram.CustomEvent) {
    const { addIntentionFlag } = this.data
    if (addIntentionFlag) {
      this.data.addIntentionFlag = false
      this.addIntention()
    }
  },

  addIntention() {
    wx.navigateTo({
      url: '/packages/resume/tempIntention/index'
    })
  },

  handleShow(e: any) {
    const {
      currentTarget: {
        dataset: { key }
      }
    } = e
    this.setData({
      [key]: true
    })
  },

  pickerChange(e: any) {
    const {
      currentTarget: {
        dataset: { value: dataValue }
      },
      detail: { label }
    } = e

    let {
      detail: { value }
    } = e

    let defaultText = ''
    // 这个需要赋值
    let keyText = ''

    switch (dataValue) {
      case 'jobParams.areaId':
        defaultText = '地区'
        keyText = 'jobParamsAreaIdTxt'

        break
      case 'jobParams.educationType':
        defaultText = '学历'
        keyText = 'jobParamsEducationText'
        break
      case 'jobParams.majorId':
        defaultText = '学科'
        keyText = 'jobParamsMajorIdTxt'
        break
      case 'jobParams.jobType':
        defaultText = '职位类型'
        keyText = 'jobParamsJobTypeTxt'
        break
      case 'jobParams.groupType':
        defaultText = '更多'
        keyText = 'jobParamsGroupTypeTxt'
        break
      default:
        break
    }

    let newText = ''

    if (dataValue == 'jobParams.areaId') {
      // label虽然是字符串,但是有可能是多个地区逗号隔开,如果是这种情况就值需要是defaultText

      if (label.length > 1) {
        newText = defaultText
      } else {
        if (label[0]) {
          newText = label
        }
      }
    } else {
      newText = defaultText
    }

    if (!newText) {
      newText = defaultText
      value = ''
    }

    this.setData(
      {
        [dataValue]: value,
        [keyText]: newText,
        'jobParams.p': 1,
        'intentionJobParams.page': 1,
        'intentionJobParams.type': 1,
        isEnd: false
      },
      () => {
        this.getList()
      }
    )
  },

  intentionSwitch(e: any) {
    const {
      currentTarget: {
        dataset: { id }
      }
    } = e
    this.setData(
      {
        'intentionJobParams.intentionId': id,
        'intentionJobParams.page': 1,
        isEnd: false
      },
      async () => {
        await this.getList()
        this.scrollToListTop()
      }
    )
  },

  typeSwitch(e: any) {
    const {
      currentTarget: {
        dataset: { type }
      }
    } = e
    this.setData(
      {
        'intentionJobParams.type': type,
        'intentionJobParams.page': 1,
        isEnd: false
      },
      async () => {
        await this.getList()
        this.scrollToListTop()
      }
    )
  },

  scrollToListTop() {
    const { offsetTop, fixedTop, isSticky } = this.data
    if (!isSticky) return
    this.setData({ scrollTop: offsetTop - fixedTop })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  async loginSuccess() {
    wx.reLaunch({ url: '/pages/home/<USER>' })
  },

  // 下拉加载更多
  scrolltolower() {
    const { isLogin, isRequesting } = this.data

    // 没登录只展示一页或者正在请求return出去
    if (!isLogin || isRequesting) return
    this.getList(false)
  },

  onScroll(e: any) {
    const {
      currentTarget: { offsetTop },
      detail: { scrollTop }
    } = e
    this.throttleFn({ offsetTop, scrollTop })
  },

  throttleFn: throttle(5, ({ offsetTop, scrollTop }) => {
    _this.handleSticky(offsetTop, scrollTop)
    _this.handleHeaderInput(offsetTop, scrollTop)
  }),

  // 处理头部吸顶
  handleSticky(offsetTop: number, scrollTop: number) {
    const { offsetTop: top, isSticky } = this.data
    const flag = offsetTop + scrollTop >= top
    // 加个判断，减少频繁setData影响性能
    if (flag !== isSticky) {
      this.setData({ isSticky: flag })
      this.getTabBar().updateBacktop(flag)
    }
  },

  // 处理头部搜索框显隐
  handleHeaderInput(offsetTop: number, scrollTop: number) {
    const distance = 50
    const {
      recommendTop,
      header: { opacity: realOpacity }
    } = this.data
    const totalTop = offsetTop + scrollTop
    const contain = totalTop >= recommendTop && totalTop <= recommendTop + distance
    // 加个判断，减少频繁setData影响性能
    if (contain) {
      const opacity = (totalTop - recommendTop) / distance
      this.setData({
        'header.opacity': opacity
      })
    } else {
      const opacity = totalTop < recommendTop ? 0 : 1
      if (opacity != realOpacity) {
        this.setData({ 'header.opacity': opacity })
      }
    }
  },

  handleCallback(callback = () => {}, toast = true) {
    const { isLogin } = this.data

    if (isLogin) {
      callback()
    } else {
      toast && this.setData({ loginDialogVisible: true })
    }
  },

  handleShowcase(e: WechatMiniprogram.CustomEvent) {
    const isLogin = checkLogin()

    if (!isLogin) {
      this.setData({ loginDialogVisible: true })
      return
    }

    const {
      currentTarget: {
        dataset: { url }
      }
    } = e

    toWebPage(`${h5}${url}`)
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  onRefresh() {
    this.setData({ isPullDownRefresh: true, triggered: false })
    this.resetList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */

  async onShareAppMessage() {
    const rs = await getCommonShareInfo()
    return {
      title: rs.title,
      path: rs.path,
      imageUrl: rs.imageUrl
    }
  }
})
