@use 'styles/variables' as *;

.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  --td-spacer-1: 30rpx;
  .t-navbar__left {
    width: 100%;
  }

  .content {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: space-between;
  }

  .header-logo {
    display: block;
    width: 208rpx;
    height: 52rpx;
    background: url('//img.gaoxiaojob.com/uploads/static/image/logo/mini_logo.png') no-repeat left/contain;
  }

  .search-content-init {
    box-sizing: border-box;
    border-radius: 64rpx;
    width: 154rpx;
    height: 60rpx;
    line-height: 64rpx;
    overflow: hidden;
    margin-right: 20rpx;
    padding-left: 72rpx;
    background: url(#{$assets}/icon/search.png) no-repeat 30rpx center/28rpx, #fff;
    text {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      opacity: 0.4;
    }
  }

  .search-content {
    box-sizing: border-box;
    border-radius: 64rpx;
    width: 482rpx;
    height: 64rpx;
    position: absolute;
    top: 0;
    overflow: hidden;
    padding-left: 72rpx;
    background: url(#{$assets}/icon/search.png) no-repeat 30rpx center/28rpx, #fff;

    swiper {
      height: 100%;
      width: 100%;
    }

    .placeholder {
      font-size: 26rpx;
      height: 100%;
      display: flex;
      align-items: center;
      color: $font-color-tips;
    }
  }

  .t-navbar__content {
    align-items: flex-start;
    background: url(#{$assets}/nav/primary.png) no-repeat center top/cover;
  }
}

@mixin sticky-content {
  .intention-job {
    box-sizing: border-box;
    width: 100%;
    padding: 12rpx 30rpx 20rpx;

    .switch-intention {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 20rpx;
    }

    .scroll {
      flex-grow: 1;
      overflow: hidden;
    }

    .intention-list {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
    }

    .item {
      flex-shrink: 0;
      padding: 10rpx 0;
      max-width: 242rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      & ~ .item {
        margin-left: 30rpx;
      }

      &.active {
        font-size: 36rpx;
        font-weight: bold;
        position: relative;

        &::before {
          position: absolute;
          left: 50%;
          bottom: 0;
          content: '';
          display: block;
          width: 40rpx;
          height: 6rpx;
          border-radius: 6rpx;
          transform: translateX(-50%);
          background: linear-gradient(-90deg, #ffffff, #ffa000);
        }
      }
    }

    .aside {
      flex-shrink: 0;
      width: 60rpx;
      display: flex;
      justify-content: flex-end;

      .edit-btn {
        width: 36rpx;
        height: 36rpx;
        flex-shrink: 0;
        margin-left: 30rpx;
        background: url(#{$assets}/icon/edit.png) no-repeat left/36rpx;
      }
    }

    .filter {
      display: flex;
    }

    .filter-item {
      font-size: 24rpx;
      padding: 0 24rpx;
      line-height: 38rpx;
      border-radius: 20rpx;
      border: 1px solid transparent;

      &.active {
        color: $color-primary;
        border-color: $color-primary;
      }
    }
  }

  .temporary-intention {
    width: 100%;
    box-sizing: border-box;
    padding: 12rpx 30rpx 20rpx;

    &.is-logout {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &.is-login {
      .temporary-job {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20rpx;
      }

      .filter {
        display: flex;
      }

      .filter-item {
        font-size: 24rpx;
        padding: 0 24rpx;
        line-height: 38rpx;
        border-radius: 20rpx;
        border: 1px solid transparent;

        &.active {
          color: $color-primary;
          border-color: $color-primary;
        }
      }
    }

    .label {
      padding: 10rpx 0;
      max-width: 244rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 36rpx;
      font-weight: bold;
      white-space: nowrap;
      position: relative;

      &::before {
        position: absolute;
        left: 50%;
        bottom: 0;
        content: '';
        display: block;
        width: 40rpx;
        height: 6rpx;
        border-radius: 6rpx;
        transform: translateX(-50%);
        background: linear-gradient(-90deg, #ffffff, #ffa000);
      }
    }

    .edit-btn {
      width: 36rpx;
      height: 36rpx;
      flex-shrink: 0;
      margin-left: 30rpx;
      background: url(#{$assets}/icon/edit.png) no-repeat left/36rpx;
    }
  }

  .filter-content {
    display: flex;
    flex-wrap: nowrap;
    box-sizing: border-box;
    padding: 20rpx 30rpx 20rpx;
    overflow-x: scroll;

    .item {
      position: relative;
      flex-shrink: 0;
      padding: 0 30rpx 0 18rpx;
      line-height: 48rpx;
      font-size: 24rpx;
      border-radius: 8rpx;
      position: relative;
      background-color: #f4f6fb;
      border: 2rpx solid #f4f6fb;
      color: $font-color-basic;
      max-width: 96rpx;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 20rpx;
      color: $font-color-basic;

      &.more {
        position: relative;
        &::before {
          content: '';
          width: 50rpx;
          height: 20rpx;
          top: -2rpx;
          right: -2rpx;
          position: absolute;
          background: url(//img.gaoxiaojob.com/uploads/mini/icon/vip.png) no-repeat center/100% 100%;
        }
      }

      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        right: 14rpx;
        bottom: 14rpx;
        display: block;
        border: 4rpx solid #c7c7c7;
        border-left-color: transparent;
        border-top-color: transparent;
      }

      &.has-condition {
        color: $color-primary;
        background-color: $color-primary-background;
        border-color: $color-primary;

        &::after {
          border-right-color: $color-primary;
          border-bottom-color: $color-primary;
        }
      }
    }
  }
}

.header-sticky {
  position: fixed;
  display: none;
  background-color: $color-white;
  box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(51, 51, 51, 0.04);
  z-index: 99;
  left: 0;
  right: 0;
  top: var(--fixed-top);

  @include sticky-content;

  &.fixed {
    display: block;
  }
}

.space-content {
  padding: 0 30rpx 40rpx;
  background: url(#{$assets}/nav/primary.png) no-repeat left -136rpx/100% auto;
}

.flex-grow {
  flex-grow: 1;
  overflow: hidden;
}

.scroll-content {
  box-sizing: border-box;
  overflow: hidden;
}

.swiper {
  --td-swiper-nav-dot-size: 10rpx;
  --td-swiper-nav-dots-bar-active-width: 20rpx;
  --td-swiper-radius: #{$border-radius};

  .t-swiper-nav__dots-bar-item {
    margin: 0 5rpx;
  }
}

.ad-swiper {
  margin-bottom: 20rpx;

  .t-swiper-nav--bottom {
    bottom: 10rpx;
  }
}

.nav-tools {
  border-radius: $border-radius;
  background-color: #fff;
  padding: 0 8rpx;
  display: flex;
  margin-bottom: 20rpx;
  font-size: 24rpx;

  .nav-item {
    width: 20%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx 0 26rpx;

    .icon {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: 16rpx;
    }
  }
}

.ad-units {
  --swiper-height: 144rpx;
  --td-swiper-nav-dot-color: #c7c7c7;
  --td-swiper-nav-dot-active-color: #{$color-primary};

  position: relative;

  .ad-units-swiper {
    height: var(--swiper-height);
  }

  .ad-units-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .ad-unit-item {
      width: 335rpx;
      height: 122rpx;
      padding: 22rpx 10rpx 24rpx 20rpx;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: $border-radius;
      display: flex;
      margin-bottom: 20rpx;

      .logo {
        width: 76rpx;
        height: 76rpx;
        border-radius: 50%;
        margin-right: 11rpx;
        flex-shrink: 0;
      }

      .ad-unit-detail {
        flex-grow: 1;
        overflow: hidden;

        .name {
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .desc {
          font-size: 24rpx;
          color: $font-color-label;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .ad-unit-nav {
    position: relative;
    height: 10rpx;
    margin-bottom: 20rpx;

    .t-swiper-nav--bottom {
      top: 0rpx;
    }
  }
}

.card {
  background-color: #fff;
  border-radius: $border-radius;
  overflow: hidden;
}

.recommend-outside {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;

  .wrapper-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 22rpx 30rpx 12rpx;

    .label {
      font-size: 36rpx;
      font-weight: bold;
      padding-left: 42rpx;
      background: url(#{$assets}/icon/title.png) no-repeat left/28rpx;
    }

    .aside {
      display: flex;
      align-items: center;
    }

    .organization {
      font-size: 24rpx;
      color: $color-point;
      line-height: 40rpx;
      height: 40rpx;
      padding: 0 14rpx 0 63rpx;
      background: url(#{$assets}/icon/hot.png) no-repeat 12rpx center/46rpx 20rpx,
        url(#{$assets}/home/<USER>/cover;
      margin-right: 7rpx;
    }

    .search-btn {
      width: 36rpx;
      height: 36rpx;
      background: url(#{$assets}/icon/search.png) no-repeat left/36rpx;
    }
  }

  @include sticky-content;

  .add-intention-tips {
    background-color: $color-primary-background;
    padding: 22rpx 20rpx;
    margin: 0 30rpx;
    border-radius: $border-radius;
    display: flex;
    align-items: center;

    .tips {
      font-size: 26rpx;
      flex-grow: 1;

      .title {
        color: $color-primary;
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .add-btn {
      background-color: $color-primary;
      padding: 0 22rpx;
      line-height: 56rpx;
      height: 56rpx;
      font-size: 26rpx;
      color: #fff;
      box-shadow: none;
      border: none;
      border-radius: 56rpx;
      font-weight: normal;
    }
  }
}

.result-content {
  .match-job {
    padding: 0 30rpx;
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    .match-swiper-wrapper {
      padding: 24rpx 0rpx;
      border-bottom: 2rpx solid #ebebeb;

      &:last-of-type {
        border-bottom: 2rpx solid transparent;
      }
    }

    .match-swiper {
      height: 136rpx;
      border-radius: $border-radius;
      overflow: hidden;

      .match-list {
        width: 242rpx;
        height: 144rpx;
        .img {
          width: 100%;
          height: 136rpx;
        }
      }
    }

    .match-unit-nav {
      position: relative;
      // height: 10rpx;
      bottom: 26rpx;

      .t-swiper-nav--bottom {
        top: 0rpx;
      }
    }
  }

  .empty {
    padding-bottom: 40rpx;

    .search-btn {
      margin: 30rpx auto 12rpx;
      width: 146rpx;
      height: 56rpx;
      background: $color-primary;
      text-align: center;
      line-height: 56rpx;
      border-radius: 28rpx;
      font-weight: bold;
      color: #ffffff;
    }
  }

  .item {
    padding: 24rpx 0rpx;
    border-bottom: 2rpx solid $border-color;
    margin-top: 0;

    &:last-child {
      border-bottom: none;
    }
  }

  .showcase {
    margin-left: -30rpx;
    margin-right: -30rpx;

    .image {
      display: block;
      width: 100%;
    }

    &::after {
      content: '';
      display: block;
      width: 630rpx;
      height: 2rpx;
      background-color: #ebebeb;
      margin: 0 auto;
    }

    &:last-of-type {
      &::after {
        display: none;
      }
    }
  }

  .recomment-title {
    height: 30rpx;
    margin: 40rpx 0;
    background: url(#{$assets}/home/<USER>/312rpx 30rpx;
  }

  .recommend-list {
    padding: 0 30rpx;
  }
}

.login-dialog {
  padding-top: 40rpx;
}
