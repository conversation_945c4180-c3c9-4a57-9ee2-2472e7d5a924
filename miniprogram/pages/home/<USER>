<layout c-class="layout">
  <t-navbar style="{{ header.style }}" title="" left-arrow="{{ false }}" class="header">
    <view class="content" slot="left">
      <navigator style="opacity: {{ 1 - header.opacity }};" class="header-logo" url="/pages/home/<USER>" open-type="switchTab"></navigator>

      <navigator style="opacity: {{ 1 - header.opacity }};" class="search-content-init" url="/packages/search/index/index?searchType=1" hover-class="none">
        <text>搜索</text>
      </navigator>

      <navigator wx:if="{{ header.opacity > 0 }}" class="search-content" url="/packages/search/index/index?searchType=1" hover-class="none" style="opacity: {{ header.opacity }};">
        <swiper autoplay interval="{{ defaultInterval }}" vertical circular>
          <swiper-item wx:for="{{ inputSwiperPlaceholder }}" wx:key="index">
            <view class="placeholder">{{ item }}</view>
          </swiper-item>
        </swiper>
      </navigator>
    </view>
  </t-navbar>

  <!-- 吸顶 -->
  <view class="header-sticky {{ isSticky ? 'fixed' : '' }}" style="--fixed-top:{{ fixedTop }}px">
    <!-- 已登录 -->
    <block wx:if="{{ isLogin }}">
      <!-- 已完成简历前三步 -->
      <block wx:if="{{ completeResume }}">
        <view class="intention-job">
          <view class="switch-intention">
            <scroll-view class="scroll" scroll-x enhanced show-scrollbar="{{ false }}">
              <view class="intention-list">
                <view class="item {{ item.id == intentionJobParams.intentionId ? 'active' : '' }}" bind:tap="intentionSwitch" wx:for="{{ resumeIntention }}" wx:for-item="item" wx:key="key" data-id="{{ item.id }}" data-id="{{ item.id }}">{{ item.name }}</view>
              </view>
            </scroll-view>
            <view class="aside">
              <navigator wx:if="{{ resumeIntention.length < 5 }}" url="/packages/resume/intention" hover-class="none">
                <t-icon name="add" size="48rpx" />
              </navigator>
              <navigator wx:else url="/packages/resume/intentionItem?id={{ intentionJobParams.intentionId }}" hover-class="none">
                <view class="edit-btn"></view>
              </navigator>
            </view>
          </view>
          <view class="filter">
            <view data-type="1" class="filter-item {{ intentionJobParams.type == 1 ? 'active' : '' }}" bind:tap="typeSwitch">推荐</view>
            <view data-type="2" class="filter-item {{ intentionJobParams.type == 2 ? 'active' : '' }}" bind:tap="typeSwitch">最新</view>
          </view>
        </view>
      </block>

      <!-- 未完成简历前三步 -->
      <block wx:else>
        <!-- 有临时求职意向 -->
        <view wx:if="{{ tempIntention }}" class="temporary-intention is-login">
          <view class="temporary-job">
            <view class="label">{{ temporary.name }}</view>
            <view class="edit-btn" bind:tap="addIntention"></view>
          </view>
          <view class="filter">
            <view data-type="1" class="filter-item {{ intentionJobParams.type == 1 ? 'active' : '' }}" bind:tap="typeSwitch">推荐</view>
            <view data-type="2" class="filter-item {{ intentionJobParams.type == 2 ? 'active' : '' }}" bind:tap="typeSwitch">最新</view>
          </view>
        </view>

        <!-- 筛选条件---无求职意向 -->
        <view wx:if="{{ !tempIntention }}" class="filter-content">
          <view class="item {{ jobParams.areaId ? 'has-condition' : '' }}" data-key="visible.area" bind:tap="handleShow">{{ jobParamsAreaIdTxt }}</view>
          <view class="item {{ jobParams.jobType ? 'has-condition' : '' }}" data-key="visible.jobType" bind:tap="handleShow">{{ jobParamsJobTypeTxt }}</view>
          <view class="item {{ jobParams.educationType ? 'has-condition' : '' }}" data-key="visible.education" bind:tap="handleShow"> {{ jobParamsEducationText }}</view>
          <view class="item {{ jobParams.majorId ? 'has-condition' : '' }}" data-key="visible.major" bind:tap="handleShow">{{ jobParamsMajorIdTxt }}</view>
          <view class="item more {{ jobParams.groupType.length ? 'has-condition' : '' }}" data-key="visible.groupType" bind:tap="handleShow">{{ jobParamsGroupTypeTxt }}</view>
          <!-- <superiorFilterPopup is-card-trigger bind:change="handleSuperior" type="{{ 1 }}" value="{{ { isEstablishment: jobParams.isEstablishment, heat: jobParams.applyHeat } }}" /> -->
        </view>
      </block>
    </block>
    <!-- 未登录 -->
    <block wx:else>
      <!-- 有临时求职意向 -->
      <view wx:if="{{ tempIntention && !completeResume }}" class="temporary-intention is-logout">
        <view class="label">{{ temporary.name }}</view>
        <view class="edit-btn" bind:tap="addIntention"></view>
      </view>
    </block>
  </view>

  <view class="flex-grow">
    <scroll-view style="height: {{ remainHeight }}" class="scroll-content" scroll-y scroll-with-animation lower-threshold="50" scroll-top="{{ scrollTop }}" bind:scrolltolower="scrolltolower" bind:scroll="onScroll" refresher-triggered="{{ triggered }}" bindrefresherrefresh="onRefresh" refresher-enabled="true">
      <view class="space-content">
        <t-swiper wx:if="{{ adSwiper.list.length }}" t-class="ad-swiper swiper" height="140rpx" autoplay interval="{{ defaultInterval }}" navigation="{{ { type: 'dots-bar' } }}" list="{{ adSwiper.list }}" bind:click="onTapBanner"> </t-swiper>

        <view class="nav-tools">
          <view class="nav-item" wx:for="{{ navTab }}" wx:key="key" wx:for-item="item" data-item="{{ item }}" bind:tap="handleToolClick">
            <image class="icon" src="{{ item.imageUrl }}" mode="aspectFit" />
            <view>{{ item.title }} </view>
          </view>
        </view>

        <view wx:if="{{ adUnits.list.length }}" class="ad-units swiper" style="{{ adUnits.adUnitsStyle }}">
          <swiper class="ad-units-swiper" bindchange="adUnitsChange" autoplay circular>
            <swiper-item class="ad-units-list" wx:for="{{ adUnits.list }}" wx:for-item="list" wx:for-index="index" wx:key="index">
              <view class="ad-unit-item" wx:for="{{ list }}" wx:key="key" data-item="{{ item }}" bind:tap="onTapAd">
                <image class="logo" src="{{ item.imageUrl }}" mode="aspectFill" />
                <view class="ad-unit-detail">
                  <view class="name">{{ item.title }}</view>
                  <view class="desc">{{ item.subTitle }}</view>
                </view>
              </view>
            </swiper-item>
          </swiper>
          <view wx:if="{{ adUnits.list.length > 1 }}" class="ad-unit-nav">
            <t-swiper-nav total="{{ adUnits.list.length }}" current="{{ adUnits.current }}" type="dots-bar"> </t-swiper-nav>
          </view>
        </view>

        <!-- 推荐职位 -->
        <view class="recommend-outside card">
          <!-- 模块标题-----无临时求职意向或未完成简历前三步显示 -->
          <view wx:if="{{ !(tempIntention || completeResume) }}" class="wrapper-title">
            <view class="label">推荐职位</view>
            <view class="aside">
              <view class="organization">试试“编制查询”</view>
              <view class="search-btn" bindtap="jumpToSearch"></view>
            </view>
          </view>

          <!-- 需要吸顶 -->
          <view class="required-sticky">
            <!-- 已登录 -->
            <block wx:if="{{ isLogin }}">
              <!-- 已完成简历前三步 -->
              <block wx:if="{{ completeResume }}">
                <view class="intention-job">
                  <view class="switch-intention">
                    <scroll-view class="scroll" scroll-x enhanced show-scrollbar="{{ false }}">
                      <view class="intention-list">
                        <view class="item {{ item.id == intentionJobParams.intentionId ? 'active' : '' }}" bind:tap="intentionSwitch" wx:for="{{ resumeIntention }}" wx:for-item="item" wx:key="key" data-id="{{ item.id }}" data-id="{{ item.id }}">{{ item.name }}</view>
                      </view>
                    </scroll-view>
                    <view class="aside">
                      <navigator wx:if="{{ resumeIntention.length < 5 }}" url="/packages/resume/intention" hover-class="none">
                        <t-icon name="add" size="48rpx" />
                      </navigator>
                      <navigator wx:else url="/packages/resume/intentionItem?id={{ intentionJobParams.intentionId }}" hover-class="none">
                        <view class="edit-btn"></view>
                      </navigator>
                    </view>
                  </view>
                  <view class="filter">
                    <view data-type="1" class="filter-item {{ intentionJobParams.type == 1 ? 'active' : '' }}" bind:tap="typeSwitch">推荐</view>
                    <view data-type="2" class="filter-item {{ intentionJobParams.type == 2 ? 'active' : '' }}" bind:tap="typeSwitch">最新</view>
                  </view>
                </view>
              </block>

              <!-- 未完成简历前三步 -->
              <block wx:else>
                <!-- 有临时求职意向 -->
                <view wx:if="{{ tempIntention }}" class="temporary-intention is-login">
                  <view class="temporary-job">
                    <view class="label">{{ temporary.name }}</view>
                    <view class="edit-btn" bind:tap="addIntention"></view>
                  </view>
                  <view class="filter">
                    <view data-type="1" class="filter-item {{ intentionJobParams.type == 1 ? 'active' : '' }}" bind:tap="typeSwitch">推荐</view>
                    <view data-type="2" class="filter-item {{ intentionJobParams.type == 2 ? 'active' : '' }}" bind:tap="typeSwitch">最新</view>
                  </view>
                </view>

                <!-- 筛选条件---无求职意向 -->
                <view wx:if="{{ !tempIntention }}" class="filter-content">
                  <view class="item {{ jobParams.areaId.length ? 'has-condition' : '' }}" data-key="visible.area" bind:tap="handleShow">{{ jobParamsAreaIdTxt }}</view>
                  <view class="item {{ jobParams.jobType.length ? 'has-condition' : '' }}" data-key="visible.jobType" bind:tap="handleShow">{{ jobParamsJobTypeTxt }}</view>
                  <view class="item {{ jobParams.educationType.length ? 'has-condition' : '' }}" data-key="visible.education" bind:tap="handleShow"> {{ jobParamsEducationText }}</view>
                  <view class="item {{ jobParams.majorId.length ? 'has-condition' : '' }}" data-key="visible.major" bind:tap="handleShow">{{ jobParamsMajorIdTxt }}</view>
                  <view class="item more {{ jobParams.groupType.length ? 'has-condition' : '' }}" data-key="visible.groupType" bind:tap="handleShow">{{ jobParamsGroupTypeTxt }}</view>
                  <!-- <superiorFilterPopup is-card-trigger bind:change="handleSuperior" type="{{ 1 }}" value="{{ { isEstablishment: jobParams.isEstablishment, heat: jobParams.applyHeat } }}" /> -->
                </view>
              </block>
            </block>
            <!-- 未登录 -->
            <block wx:else>
              <!-- 有临时求职意向 -->
              <view wx:if="{{ tempIntention && !completeResume }}" class="temporary-intention is-logout">
                <view class="label">{{ temporary.name }}</view>
                <view class="edit-btn" bind:tap="addIntention"></view>
              </view>
            </block>
          </view>

          <!-- 添加意向职位模块------没有临时意向职位并且未完成简历前三步显示 -->
          <view wx:if="{{ !(tempIntention || completeResume) }}" class="add-intention-tips">
            <view class="tips">
              <view class="title">你想找什么工作？</view>
              根据地区、职位类型等精准推荐职位
            </view>
            <button class="add-btn" bind:tap="handleLoginGuide">添加意向</button>
          </view>
        </view>

        <view class="result-content">
          <view class="card match-job">
            <block wx:if="{{ jobList.length }}">
              <block wx:for="{{ jobList }}" wx:key="key" wx:for-item="item" wx:for-index="index">
                <block wx:if="{{ item.id }}">
                  <job-item c-class="item" detail="{{ item }}" isLogin="{{ isLogin }}" showExperience="{{ false }}" showCity="{{ false }}" />
                </block>

                <block wx:else>
                  <view wx:if="{{ item.caseInfoList.length > 1 }}" class="match-swiper-wrapper">
                    <swiper class="match-swiper" bindchange="handleMatchChange" autoplay circular>
                      <swiper-item class="match-list" bindtap="handleMatchClick" wx:for="{{ item.caseInfoList }}" wx:for-item="caseInfoItem" wx:for-index="index" wx:key="index" data-item="{{ caseInfoItem }}">
                        <image class="img" src="{{ caseInfoItem.imageUrl }}" mode="aspectFill" />
                      </swiper-item>
                    </swiper>
                    <view wx:if="{{ item.caseInfoList.length > 1 }}" class="match-unit-nav">
                      <t-swiper-nav total="{{ item.caseInfoList.length }}" current="{{ matchSwiper.current }}" type="dots-bar"> </t-swiper-nav>
                    </view>
                  </view>

                  <!-- <view class="showcase" bindtap="handleShowcase" data-url="{{ item.url }}">
                    <image class="image" src="{{ item.src }}" mode="widthFix" />
                  </view> -->
                </block>
              </block>
            </block>
            <block wx:else>
              <view class="empty">
                <empty description="对不起，暂无符合条件的职位，试试搜索吧" />
                <navigator class="search-btn" target="" url="/packages/search/index/index" hover-class="none" open-type="navigate"> 去搜索 </navigator>
              </view>
            </block>
          </view>

          <block wx:if="{{ recommendList.length }}">
            <view class="recomment-title"></view>

            <view class="card recommend-list">
              <job-item c-class="item" wx:for="{{ recommendList }}" wx:key="key" wx:for-item="item" showExperience="{{ false }}" showCity="{{ false }}" detail="{{ item }}" />
            </view>
          </block>
        </view>

        <block wx:if="{{ !isLogin }}">
          <login-dialog c-class="login-dialog" showTrigger visible="{{ loginDialogVisible }}" bind:loginSuccess="loginSuccess" bind:close="handleLoginClose" />
        </block>
      </view>
    </scroll-view>
  </view>

  <!-- 获取完信息再判断是否展示，避免一开始就显示 -->
  <block wx:if="{{ isFirstLoadComplete }}">
    <qrcode-popup visible="{{ qrcodeVisible }}" trigger="auto" />

    <resume-perfect visible="{{ isLogin && resumeCompletePercent < 75 }}" />
  </block>

  <!-- 加个判断，页面加载完再展示，提升渲染速度 -->
  <block wx:if="{{ isFirstLoadComplete }}">
    <picker-announcement-type model="{{ [] }}" limit="5" data-type="2" data-key="type" title="公告类型" visible="{{ visible.announcementType }}" bind:change="filterChange" unlimit />
    <picker-major model="{{ [] }}" limit="5" data-type="1" data-key="majorId" title="选择学科" visible="{{ visible.majorType }}" bind:change="filterChange" unlimit />
    <picker-company-type model="{{ [] }}" limit="5" data-type="3" data-key="companyType" title="单位类型" visible="{{ visible.companyType }}" bind:change="filterChange" unlimit />
  </block>

  <!-- 加个判断，已登录、页面加载完页面加载完并且没有求职意向(包括临时)再展示，提升渲染速度 -->
  <block wx:if="{{ isLogin && isFirstLoadComplete && !tempIntention && !completeResume }}">
    <picker-area title="选择地区" type="intention" visible="{{ visible.area }}" model="{{ jobParams.areaId }}" type="intention" bind:change="pickerChange" data-value="jobParams.areaId" data-label="jobParamsTxt.areaTxt" limit="{{ limit }}" unlimit />

    <picker-education visible="{{ visible.education }}" model="{{ jobParams.educationType }}" data-value="jobParams.educationType" data-label="jobParamsTxt.educationTxt" bind:change="pickerChange" limit="5" />

    <picker-major column="{{ 2 }}" title="选择学科" visible="{{ visible.major }}" model="{{ jobParams.majorId }}" bind:change="pickerChange" data-value="jobParams.majorId" data-label="jobParamsTxt.majorTxt" limit="5" unlimit />
    <picker-job-category title="选择职位类型" visible="{{ visible.jobType }}" model="{{ jobParams.jobType }}" bind:change="pickerChange" data-value="jobParams.jobType" data-label="jobParamsTxt.jobTypeTxt" limit="5" unlimit />
    <picker-group-type limit="{{ 12 }}" data-value="jobParams.groupType" title="更多筛选" defaultLabel="更多" visible="{{ visible.groupType }}" model="{{ jobParams.groupType }}" bind:change="pickerChange" />
  </block>
  <add-desk-top />
</layout>
