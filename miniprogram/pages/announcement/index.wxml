<layout c-class="layout">
  <t-navbar style="{{ headerStyle }}" title="" class="header-container">
    <view class="content" slot="left">
      <view class="tabs">
        <view class="item {{ tabType == 1 ? 'active' : '' }}" bind:tap="switchType" data-type="{{ 1 }}"> 招聘公告 </view>
        <view class="item {{ tabType == 2 ? 'active' : '' }}" bind:tap="switchType" data-type="{{ 2 }}"> 单位 </view>
      </view>
      <navigator class="search" url="/packages/search/index/index?searchType={{ tabType == 1 ? 2 : 3 }}" hover-class="none"></navigator>
    </view>
  </t-navbar>

  <view class="flex-grow">
    <scroll-view style="height: {{ remainHeight + 'px' }}" hidden="{{ tabType != 1 }}" class="scroll-content" scroll-y upper-threshold="100" bindscroll="onScroll" bindscrolltolower="announcementLoadMore" refresher-enabled="true" refresher-triggered="{{ triggeredAnnouncement }}" bindrefresherrefresh="onRefreshAnnouncement" scroll-top="{{ scrollTo.announcement }}" scroll-with-animation>
      <!-- 公告的banner -->
      <view class="announcement-container">
        <view class="top-content" style="background-position: left -{{ fixedTop }}px;">
          <view class="banner-content" wx:if="{{ announcementBannerSwiper.list.length }}">
            <t-swiper t-class="banner swiper" height="140rpx" autoplay interval="{{ announcementBannerSwiper.interval }}" navigation="{{ { type: 'dots-bar' } }}" list="{{ announcementBannerSwiper.list }}" bind:click="jumpAnnouncementBanner"> </t-swiper>
          </view>

          <view class="root-portal-filter" id="announcement-filter">
            <root-portal class="root-portal" enable="{{ announcementFilterFixed && tabType == 1 }}">
              <view class="filter-content {{ announcementFilterFixed ? 'fixed' : '' }}" style="top:{{ fixedTop }}px">
                <view bind:tap="showPicker" data-type="announcementAreaId" class="item {{ announcementParams.areaId ? 'has-condition' : '' }}"> {{ announcementPickerAreaIdTxt }} </view>
                <view bind:tap="showPicker" data-type="announcementType" class="item {{ announcementParams.type ? 'has-condition' : '' }}">{{ announcementPickerTypeTxt }} </view>
                <view bind:tap="showPicker" data-type="educationType" class="item {{ announcementParams.educationType ? 'has-condition' : '' }}">{{ announcementPickerEducationTxt }} </view>
                <view bind:tap="showPicker" data-type="announcementMajorType" class="item {{ announcementParams.majorId ? 'has-condition' : '' }}">
                  {{ announcementPickerMajorIdTxt }}
                </view>
                <!-- <view bind:tap="showPicker" data-type="announcementCompanyType" class="item {{ announcementParams.companyType ? 'has-condition' : '' }}"> {{ announcementPickerCompanyTypeTxt }}</view> -->
                <!-- <superiorFilterPopup is-card-trigger c-class="superior-filter" bind:change="handleSuperior" type="{{ 2 }}" value="{{ { isEstablishment: announcementParams.isEstablishment, heat: announcementParams.announcementHeat } }}" bind:loginSuccess="loginSuccess" /> -->
                <view bind:tap="showPicker" data-type="groupType" class="item more {{ announcementParams.groupType ? 'has-condition' : '' }}">{{ announcementPickerGroupTxt }} </view>
              </view>
            </root-portal>
          </view>
        </view>

        <view class="result-content">
          <block wx:for="{{ announcementList }}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <block wx:if="{{ item.id }}">
              <announcement-item c-class="item" detail="{{ item }}" />
            </block>

            <block wx:else>
              <view class="showcase" bindtap="handleShowcase" data-url="{{ item.url }}">
                <image class="image" src="{{ item.src }}" mode="widthFix" />
              </view>
            </block>
          </block>
        </view>

        <empty wx:if="{{ !showListLoading && !announcementList.length }}" c-class="empty-content" description="暂未搜到更多公告，修改搜索条件再试试" />
        <login-dialog title="更多优选公告，登录查看" visible="{{ loginDialogVisible && tabType == 1 }}" bind:loginSuccess="loginSuccess" wx:if="{{ !showListLoading && isShowLoginDialog }}" c-class="login-dialog" showTrigger />
        <view class="ending-content" wx:if="{{ announcementEnd && announcementList.length }}">到底啦~</view>

        <loading visible="{{ showListLoading }}" />
      </view>
    </scroll-view>

    <scroll-view style="height: {{ remainHeight + 'px' }}" hidden="{{ tabType != 2 }}" class="scroll-content" scroll-y bindscroll="onScroll" bindscrolltolower="companyLoadMore" refresher-enabled="true" bindrefresherrefresh="onRefreshCompany" refresher-triggered="{{ triggeredCompany }}" scroll-top="{{ scrollTo.company }}" scroll-with-animation>
      <view class="company-container">
        <!-- 单位的banner -->
        <view class="banner-content" wx:if="{{ companyBannerSwiper.list.length }}" style="background-position: left -{{ fixedTop }}px;">
          <t-swiper t-class="banner swiper" height="140rpx" autoplay interval="{{ companyBannerSwiper.interval }}" navigation="{{ { type: 'dots-bar' } }}" list="{{ companyBannerSwiper.list }}" bind:click="jumpCompanyBanner"> </t-swiper>
        </view>

        <view class="content-space">
          <view wx:if="{{ companyRecommendSwiper.originalList.length }}" class="units-content card">
            <view class="wrapper-title">推荐单位</view>
            <view class="units swiper" style="{{ companyRecommendSwiper.style }}">
              <!-- 推荐单位 -->
              <swiper class="units-swiper" bindchange="unitsChange" autoplay circular>
                <swiper-item class="units-list" wx:for="{{ companyRecommendSwiper.originalList }}" wx:for-item="item" wx:for-index="key" wx:key="key">
                  <view class="unit-item" wx:for="{{ item }}" wx:for-item="itemOne" wx:for-index="key" wx:key="key" data-item="{{ itemOne }}" bind:tap="companyRecommendJump">
                    <image class="logo {{ item.companyRole === '2' ? 'vip' : '' }}" src="{{ itemOne.imageUrl }}" mode="aspectFill" />
                    <view class="unit-detail">
                      <view class="name">{{ itemOne.title }}</view>
                      <view class="desc">{{ itemOne.subTitle }}</view>
                    </view>
                  </view>
                </swiper-item>
              </swiper>
            </view>
            <view wx:if="{{ companyRecommendSwiper.list.length }}" class="swiper-nav">
              <t-swiper-nav total="{{ companyRecommendSwiper.list.length }}" current="{{ companyRecommendSwiper.current }}" type="dots-bar"> </t-swiper-nav>
            </view>
          </view>

          <view class="filter-result card">
            <view class="wrapper-title">单位列表</view>

            <view class="root-portal-filter" id="company-filter">
              <root-portal class="root-portal" enable="{{ companyFilterFixed && tabType == 2 }}">
                <view class="filter-content {{ companyFilterFixed ? 'fixed' : '' }}" style="top:{{ fixedTop }}px">
                  <view class="left">
                    <view bind:tap="showPicker" data-type="companyAreaId" class="item {{ companyParams.areaId ? 'has-condition' : '' }}">{{ companyPickerAreaIdTxt }} </view>
                    <view class="item" bind:tap="showPicker" data-type="companyCompanyType" class="item {{ companyParams.companyType ? 'has-condition' : '' }}"> {{ companyPickerCompanyTypeTxt }} </view>
                  </view>
                  <view class="right">
                    <view bind:tap="showPicker" data-type="companyCompanyNature" class="item {{ companyParams.companyNature ? 'has-condition' : '' }}"> {{ companyPickerCompanyNatureTxt }}</view>
                  </view>
                </view>
              </root-portal>
            </view>

            <view class="result-content">
              <company-item c-class="item" wx:for="{{ companyList }}" wx:for-item="item" wx:for-index="index" wx:key="index" detail="{{ item }}" />
            </view>

            <empty wx:if="{{ !showListLoading && !companyList.length }}" c-class="empty-content" description="暂未搜到更多单位，修改搜索条件再试试" />
          </view>
        </view>

        <login-dialog visible="{{ loginDialogVisible && tabType == 2 }}" title="更多优选单位，登录查看" bind:loginSuccess="loginSuccess" wx:if="{{ !showListLoading && isShowLoginDialog }}" c-class="login-dialog" showTrigger />

        <view class="ending-content" wx:if="{{ companyEnd && companyList.length }}">到底啦~</view>
        <loading visible="{{ showListLoading }}" />
      </view>
    </scroll-view>
  </view>

  <view wx:if="{{ (backTop.announcement && tabType === 1) || (backTop.company && tabType === 2) }}" class="back-top-trigger" bind:tap="backTop"></view>

  <view wx:if="{{ showSelectPicker }}">
    <picker-area title="选择地区" type="intention" visible="{{ pickerVisible.announcementAreaId }}" model="{{ announcementParams.areaId }}" bind:change="pickerChange" data-type="announcement-areaId" limit="{{ 5 }}" unlimit />

    <picker-education visible="{{ pickerVisible.educationType }}" model="{{ announcementParams.educationType }}" data-type="announcement-educationType" bindchange="pickerChange" limit="{{ 5 }}" />

    <picker-group-type visible="{{ pickerVisible.groupType }}" model="{{ announcementParams.groupType }}" data-type="announcement-groupType" type="{{ 2 }}" defaultLabel="更多" bind:updateLabel="pickerLabel" bind:change="pickerChange" limit="{{ 12 }}" />

    <picker-major title="选择学科" visible="{{ pickerVisible.announcementMajorType }}" data-type="announcement-majorId" model="{{ announcementParams.majorId }}" bind:change="pickerChange" limit="{{ 5 }}" unlimit />

    <picker-company-type data-type="announcement-companyType" title="单位类型" model="{{ announcementParams.companyType }}" visible="{{ pickerVisible.announcementCompanyType }}" bind:change="pickerChange" limit="{{ 5 }}" unlimit />

    <picker-announcement-type data-type="announcement-type" title="公告类型" visible="{{ pickerVisible.announcementType }}" model="{{ announcementParams.type }}" bind:change="pickerChange" limit="{{ 5 }}" unlimit />

    <picker-area title="选择地区" type="intention" visible="{{ pickerVisible.companyAreaId }}" model="{{ companyParams.areaId }}" bind:change="pickerChange" data-type="company-areaId" limit="{{ 5 }}" unlimit />

    <picker-company-type data-type="company-companyType" title="单位类型" visible="{{ pickerVisible.companyCompanyType }}" model="{{ companyParams.companyType }}" bind:change="pickerChange" limit="{{ 5 }}" unlimit />

    <picker-company-nature data-type="company-companyNature" title="单位性质" visible="{{ pickerVisible.companyCompanyNature }}" model="{{ companyParams.companyNature }}" bind:change="pickerChange" limit="{{ 5 }}" unlimit />
  </view>
</layout>
