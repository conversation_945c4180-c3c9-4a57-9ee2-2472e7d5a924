{"usingComponents": {"t-swiper": "tdesign-miniprogram/swiper/swiper", "t-swiper-nav": "tdesign-miniprogram/swiper-nav/swiper-nav", "announcement-item": "/components/announcementItem/index", "company-item": "/components/companyItem/index", "empty": "/components/empty/index", "layout": "/pages/components/layout/index", "job-list-dialog": "/components/jobListDialog/index", "picker-announcement-type": "/components/pickerAnnouncementType", "picker-company-type": "/components/pickerCompanyType", "picker-company-nature": "/components/pickerCompanyNature", "picker-area": "/components/pickerArea", "picker-major": "/components/pickerMajor", "picker-job-category": "/components/pickerJobCategory", "picker-group-type": "/components/pickerGroupType", "loading": "/components/loading", "superiorFilterPopup": "/components/superiorFilterPopup", "picker-education": "/components/pickerEducation"}}