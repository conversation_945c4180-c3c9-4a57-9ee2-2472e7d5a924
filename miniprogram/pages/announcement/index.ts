import { handleTabBar } from '@/utils/tabBar'
import { throttle } from 'throttle-debounce'
import { getList as getAnnouncementList, getHomeShowcase as getAnnouncemenHomeShowcase } from '@/api/announcement'
import { getList as getCompanyList } from '@/api/company'
import { jump } from '@/utils/url'
import { arrayToMatrix } from '@/utils/array'
import { checkLogin, getAreaAreaInfo, getCommonShareInfo } from '@/utils/store'
import { getDictionaryLabel } from '@/api/config'
import { getResumeStepNum } from '@/api/person'
import { getElClientRect } from '@/utils/util'
import { dictionaryType } from '@/settings'

import { toWebPage } from '@/utils/util'
import { h5 } from '@/settings'

let _this = <any>null
Page({
  /**
   * 页面的初始数据
   */
  data: <any>{
    // scroll-view高度
    remainHeight: 0,
    headerStyle: getApp().globalData.headerStyle,
    // 未设置,1未登录,2已
    isLogin: false,
    isFinishStep: false,
    fixedTop: getApp().globalData.headerOffsetHeight,
    tabType: 1,
    loginDialogVisible: false,
    isFirstAnnouncement: true,
    isFirstComopany: true,
    isFirstOpen: true,
    // 选择器
    pickerVisible: {
      announcementAreaId: false,
      educationType: false,
      groupType: false,
      announcementMajorType: false,
      announcementCompanyType: false,
      announcementType: false,
      companyAreaId: false,
      companyCompanyType: false,
      companyCompanyNature: false
    },
    isShowLoginDialog: false,
    firstQuery: {},
    // 广告位相关
    announcementBannerSwiper: {
      interval: 5000,
      originalList: [],
      // 这个list是给图片显示的
      list: []
    },
    announcementFilterFixed: false,
    announcementFilterTop: 0,
    // 一条公告都木有
    announcementListEmpty: true,
    // 公告
    announcementList: [],
    announcementEnd: false,
    // 公告搜索条件
    announcementParams: {
      page: 1,
      type: '',
      majorId: '',
      areaId: '',
      companyType: '',
      educationType: '',
      groupType: '',
      jobType: '',
      keyword: '',
      isEstablishment: '',
      announcementHeat: ''
    },
    triggeredAnnouncement: false,
    announcementPickerAreaIdTxt: '地区',
    announcementPickerEducationTxt: '学历',
    announcementPickerGroupTxt: '更多',
    announcementPickerMajorIdTxt: '学科',
    announcementPickerCompanyTypeTxt: '单位类型',
    announcementPickerTypeTxt: '公告类型',
    companyPickerAreaIdTxt: '地区',
    companyPickerCompanyTypeTxt: '单位类型',
    companyPickerCompanyNatureTxt: '单位性质',
    // 一条单位都木有
    companyListEmpty: true,
    // 单位
    companyList: [],
    companyEnd: false,
    companyFilterFixed: false,
    companyFilterTop: 0,
    // 广告位相关
    companyBannerSwiper: {
      interval: 5000,
      originalList: [],
      // 这个list是给图片显示的
      list: []
    }, // 广告位相关
    companyRecommendSwiper: {
      interval: 5000,
      style: '--swiper-height: 264rpx', // 122
      current: 0,
      originalList: [],
      // 这个list是给图片显示的
      list: []
    },
    // 单位搜索条件
    companyParams: {
      page: 1,
      areaId: '',
      companyType: '',
      companyNature: ''
    },
    triggeredCompany: false,
    showListLoading: true,

    scrollTo: {
      announcement: 0,
      company: 0
    },

    backTop: {
      announcement: false,
      company: false
    },
    showSelectPicker: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(query) {
    const { tabType = 1 } = query
    this.setData({ tabType: Number(tabType) })
    if (query.announcementType) {
      this.data.firstQuery.announcementType = query.announcementType
    }

    if (query.companyType) {
      this.data.firstQuery.companyType = query.companyType
    }

    if (query.areaId) {
      this.data.firstQuery.areaId = query.areaId
    }
    const isLogin = checkLogin()
    this.setData({ isLogin })
    _this = this
    this.getAd()
    this.getRemainHeight()
  },

  getAd() {
    // 获取广告位信息(包含公告和单位)
    getAnnouncemenHomeShowcase()
      .then((r: any) => {
        const announcementBanner = r.announcementBanner.reduce((acc: any, cur: any) => {
          if (cur.imageUrl) {
            acc.push(cur.imageUrl)
          }
          return acc
        }, [])
        this.setData({
          'announcementBannerSwiper.list': announcementBanner,
          'announcementBannerSwiper.originalList': r.announcementBanner
        })

        const companyBanner = r.companyBanner.reduce((acc: any, cur: any) => {
          if (cur.imageUrl) {
            acc.push(cur.imageUrl)
          }
          return acc
        }, [])

        this.setData({
          'companyBannerSwiper.list': companyBanner,
          'companyBannerSwiper.originalList': r.companyBanner
        })

        // 4个为一组
        const companyRecommendTeam = arrayToMatrix(r.companyRecommend, 4)
        this.setData({
          'companyRecommendSwiper.list': companyRecommendTeam,
          'companyRecommendSwiper.originalList': companyRecommendTeam,
          'companyRecommendSwiper.style':
            r.companyRecommend.length > 2 ? '--swiper-height: 264rpx' : '--swiper-height: 122rpx'
        })

        this.getOffsetTop()
      })
      .catch(() => {})
  },

  async getRemainHeight() {
    const rect: any = await getElClientRect('.flex-grow')
    this.setData({ remainHeight: rect.height })
  },

  async getOffsetTop() {
    const { tabType } = this.data
    let tabKey = 'announcementFilterTop'
    let offsetTop = 0
    const announcementFilterEl: any = await getElClientRect('#announcement-filter')
    const { top: announcementFilterTop } = announcementFilterEl

    const companyFilterEl: any = await getElClientRect('#company-filter')
    const { top: companyFilterTop } = companyFilterEl
    switch (tabType) {
      case 1:
        tabKey = 'announcementFilterTop'
        offsetTop = announcementFilterTop
        break
      case 2:
        tabKey = 'companyFilterTop'
        offsetTop = companyFilterTop
        break
      default:
        break
    }
    const isReturn = this.data[tabKey] > 0
    if (isReturn) return
    this.setData({
      [tabKey]: offsetTop
    })
  },

  async search() {
    // 出loading
    if (this.data.isFirstOpen) {
      // 这里有一些情况,如果是从首页或者其他链接过来的,就需要设置对某些信息的设置
      if (this.data.tabType == 1) {
        if (this.data.firstQuery.announcementType) {
          // 从首页过来
          const type = dictionaryType['announcementType']
          const txt = await getDictionaryLabel({ type, value: this.data.firstQuery.announcementType })
          // 逗号转数组

          this.setData({
            // 数组
            'announcementParams.type': this.data.firstQuery.announcementType.split(','),
            announcementPickerTypeTxt: this.labelToShowLabel(txt['textList'], '公告类型')
          })
        }
      }

      if (this.data.tabType == 2) {
        if (this.data.firstQuery.companyType) {
          // 从首页过来
          const type = dictionaryType['companyType']
          const txt = await getDictionaryLabel({ type, value: this.data.firstQuery.companyType })
          this.setData({
            'companyParams.companyType': this.data.firstQuery.companyType.split(','),
            companyPickerCompanyTypeTxt: this.labelToShowLabel(txt['textList'], '单位类型')
          })
        }
      }

      let areaId = this.data.firstQuery.areaId
      let areaName = ''
      const txt = await getDictionaryLabel({ type: 101, value: areaId })

      if (areaId) {
        areaId = areaId
        areaName = txt['textList'].length > 1 ? this.labelToShowLabel(txt['textList'], '地区') : txt['textList'][0]
      } else {
        const areaInfo = getAreaAreaInfo()
        areaId = areaInfo.id
        areaName = areaInfo.name
      }
      this.setData({
        'announcementParams.areaId': areaId.split(','),
        'companyParams.areaId': areaId.split(','),
        announcementPickerAreaIdTxt: areaName,
        companyPickerAreaIdTxt: areaName
      })
    }
    if (this.data.tabType == 1) {
      this.getAnnouncementList()
      this.setData({ isFirstAnnouncement: false })
    }

    if (this.data.tabType == 2) {
      this.getCompanyList()
      this.setData({ isFirstComopany: false })
    }

    this.setData({ showSelectPicker: true })
  },

  showListLoading() {
    if (this.data.announcementParams.page == 1) {
      this.setData({ showListLoading: true })
    } else {
      this.setData({ showListLoading: true })
    }
  },
  hideListLoading() {
    this.setData({ showListLoading: false })
    wx.hideLoading()
  },

  async getAnnouncementList() {
    this.showListLoading()
    if (this.data.announcementParams.page == 1) {
      // 清空
      this.setData({ announcementList: [] })
    }

    const data = this.data.announcementParams
    //  majorId: '',areaId: '',companyType: '',jobType: '' 需要如果是数组转成逗号隔开
    if (data.majorId instanceof Array) {
      data.majorId = data.majorId.join(',')
    }

    if (data.areaId instanceof Array) {
      data.areaId = data.areaId.join(',')
    }

    if (data.companyType instanceof Array) {
      data.companyType = data.companyType.join(',')
    }
    if (data.jobType instanceof Array) {
      data.jobType = data.jobType.join(',')
    }

    if (data.type instanceof Array) {
      data.type = data.type.join(',')
    }

    if (data.groupType instanceof Array) {
      data.groupType = data.groupType.join(',')
    }

    const result = await getAnnouncementList(data)
    let { list, showcaseInfo = {} } = result
    const { position: index, url, img: src } = showcaseInfo

    if (src) {
      list.splice(index, 0, { url, src })
    }

    this.hideListLoading()

    const announcementList = this.data.announcementList.concat(list)
    _this.setData({
      announcementList
    })

    // 这里判断一下是否出现登录框?
    if (!this.data.isLogin) {
      this.setData({ isShowLoginDialog: true })
    } else {
      this.setData({ isShowLoginDialog: false })
    }

    if (result.list.length < 20) {
      // 没有更多了
      _this.setData({ announcementEnd: true })
    } else {
      _this.setData({ 'announcementParams.page': _this.data.announcementParams.page + 1 })
      _this.setData({ announcementEnd: false })
    }
    this.setData({ isFirstOpen: false })
    this.hideListLoading()
  },

  async getCompanyList() {
    this.showListLoading()

    const data = this.data.companyParams
    //  majorId: '',areaId: '',companyType: '',jobType: '' 需要如果是数组转成逗号隔开
    if (data.majorId instanceof Array) {
      data.majorId = data.majorId.join(',')
    }

    if (data.areaId instanceof Array) {
      data.areaId = data.areaId.join(',')
    }

    if (data.companyNature instanceof Array) {
      data.companyNature = data.companyNature.join(',')
    }

    if (data.companyType instanceof Array) {
      data.companyType = data.companyType.join(',')
    }

    const result = await getCompanyList(data)

    if (this.data.companyParams.page == 1) {
      // 清空
      this.setData({ companyList: [] })
    }
    const list = this.data.companyList.concat(result.list)
    _this.setData({
      companyList: list
    })
    // 这里判断一下是否出现登录框?
    if (!this.data.isLogin) {
      this.setData({ isShowLoginDialog: true })
    } else {
      this.setData({ isShowLoginDialog: false })
    }
    if (result.list.length < 20) {
      // 没有更多了
      _this.setData({ companyEnd: true })
    } else {
      _this.setData({ 'companyParams.page': _this.data.companyParams.page + 1 })
      _this.setData({ companyEnd: false })
    }
    this.setData({ isFirstOpen: false })
    this.hideListLoading()
  },

  switchType(e: any) {
    const {
      currentTarget: {
        dataset: { type }
      }
    } = e
    // 这里判断到是第一次来到这个tab
    if (type == 1 && this.data.isFirstAnnouncement) {
      this.getAnnouncementList()
      this.setData({ isFirstAnnouncement: false })
    }
    if (type == 2 && this.data.isFirstComopany) {
      this.getCompanyList()
      this.setData({ isFirstComopany: false })
    }

    this.setData({ tabType: type, loginDialogVisible: false })
    this.getOffsetTop()
  },

  unitsChange(e: any) {
    const {
      detail: { current }
    } = e
    this.setData({
      'companyRecommendSwiper.current': current
    })
  },

  labelToShowLabel(label: any, txt: string) {
    if (Array.isArray(label) && label.length <= 1) {
      label = txt + '∙' + label.length
    } else if (Array.isArray(label) && label.length > 1) {
      label = txt + '∙' + label.length
    } else {
      // 如果label超过三个字就...
      if (label && label.length > 3) {
        label = label.slice(0, 3) + '...'
      }
    }

    return label
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  setFixed: throttle(5, ({ offsetTop, scrollTop }) => {
    const { announcementFilterTop, companyFilterTop, tabType } = _this.data
    // 还没初始化完成不执行操作
    let isContinue = false
    let isFixed = false
    let tabKey = 'announcementFilterFixed'

    switch (tabType) {
      case 1:
        tabKey = 'announcementFilterFixed'
        isFixed = offsetTop + scrollTop >= announcementFilterTop
        isContinue = announcementFilterTop != 0
        break
      case 2:
        tabKey = 'companyFilterFixed'
        isFixed = offsetTop + scrollTop >= companyFilterTop
        isContinue = companyFilterTop != 0
        break
      default:
        break
    }
    if (!isContinue) return
    _this.setData({
      [tabKey]: isFixed,
      fixedTop: offsetTop
    })
  }),

  setBacktop: throttle(300, ({ scrollTop }) => {
    const { remainHeight, backTop, tabType } = _this.data
    const isShow = scrollTop > remainHeight
    const typeKey = tabType === 2 ? 'company' : 'announcement'
    const flag = backTop[typeKey]
    if (isShow !== flag) {
      _this.setData({
        [`backTop.${typeKey}`]: isShow
      })
    }
  }),

  backTop() {
    const { tabType } = this.data
    const typeKey = tabType === 2 ? 'company' : 'announcement'
    this.setData({
      [`scrollTo.${typeKey}`]: 0
    })
  },

  onScroll(e: any) {
    const {
      currentTarget: { offsetTop },
      detail: { scrollTop }
    } = e
    this.setFixed({ offsetTop, scrollTop })
    this.setBacktop({ offsetTop, scrollTop })
  },

  announcementLoadMore() {
    if (this.data.announcementEnd) {
      return
    }
    // 第二页需要登录才可以请求
    if (this.data.announcementParams.page >= 2 && !this.data.isLogin) {
      return
    }
    if (this.data.showListLoading) {
      return
    }
    this.getAnnouncementList()
  },

  companyLoadMore() {
    if (this.data.companyEnd) {
      return
    }
    if (this.data.companyParams.page >= 2 && !this.data.isLogin) {
      return
    }
    if (this.data.showListLoading) {
      return
    }
    this.getCompanyList()
  },

  showPicker(e: any) {
    const type = e.currentTarget.dataset.type
    const key = `pickerVisible.${type}`

    this.setData({
      [key]: true
    })
  },

  pickerChange(e: any) {
    const type = e.currentTarget.dataset.type
    const detail = e.detail
    const label = detail.label
    let value = detail.value
    // type 实际上是xxx-xxx的
    const selectType = type.split('-')[0]
    const selectParamsKey = type.split('-')[1]
    const paramsKey = `${selectType}Params.${selectParamsKey}`
    let selectText = ''
    const selectTextConfig = <any>{
      educationType: '学历',
      groupType: '更多',
      majorId: '学科',
      areaId: '地区',
      companyNature: '单位性质',
      companyType: '单位类型',
      type: '公告类型'
    }

    if (selectParamsKey == 'areaId') {
      // 如果是数组并且只有一个
      if (Array.isArray(label) && label.length <= 1) {
        selectText = label[0]
      } else if (Array.isArray(label) && label.length > 1) {
        selectText = selectTextConfig[selectParamsKey] + '∙' + label.length
      } else {
        if (label && label.length > 3) {
          selectText = label.slice(0, 3) + '...'
        } else {
          selectText = label
        }
      }
    } else {
      if (Array.isArray(label)) {
        if (label[0]) {
          selectText = selectTextConfig[selectParamsKey] + '∙' + label.length
        } else {
          selectText = selectTextConfig[selectParamsKey]
        }
      } else {
        if (label && label.length > 3) {
          selectText = label.slice(0, 3) + '...'
        } else {
          selectText = label
        }
      }
    }

    // 最后做一个保底兼容
    if (!selectText) {
      selectText = selectTextConfig[selectParamsKey]
    }

    if (selectType == 'announcement') {
      if (selectParamsKey == 'areaId') {
        this.setData({ announcementPickerAreaIdTxt: selectText })
      }

      if (selectParamsKey == 'educationType') {
        this.setData({ announcementPickerEducationTxt: selectText })
      }

      if (selectParamsKey == 'majorId') {
        this.setData({ announcementPickerMajorIdTxt: selectText })
      }

      if (selectParamsKey == 'type') {
        this.setData({ announcementPickerTypeTxt: selectText })
      }

      if (selectParamsKey == 'companyType') {
        this.setData({ announcementPickerCompanyTypeTxt: selectText })
      }

      if (selectParamsKey == 'groupType') {
        this.setData({ announcementPickerGroupTxt: selectText })
      }
    }

    if (selectType == 'company') {
      if (selectParamsKey == 'areaId') {
        this.setData({ companyPickerAreaIdTxt: selectText })
      }

      if (selectParamsKey == 'majorId') {
        this.setData({ companyPickerMajorIdTxt: selectText })
      }

      if (selectParamsKey == 'companyType') {
        this.setData({ companyPickerCompanyTypeTxt: selectText })
      }

      if (selectParamsKey == 'companyNature') {
        this.setData({ companyPickerCompanyNatureTxt: selectText })
      }
    }

    if (Array.isArray(value) && value.length <= 1) {
      value = value[0]
    }

    // 这里还做一个容错
    if (!value) {
      value = ''
    }

    this.setData({
      [paramsKey]: value
    })

    if (selectType == 'announcement') {
      // 设置分页为第一页
      this.setData({ 'announcementParams.page': 1 })
      this.getAnnouncementList()
    }

    if (selectType == 'company') {
      // 设置分页为第一页
      this.setData({ 'companyParams.page': 1 })
      this.getCompanyList()
    }
  },

  loginSuccess() {
    wx.reLaunch({ url: '/pages/announcement/index?tabType=' + this.data.tabType })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    handleTabBar(this, { value: 'announcement' })

    // 获取是否完善前三部
    const isFirstOpen = this.data.isFirstOpen
    const isLogin = checkLogin()
    if (isFirstOpen) {
      this.resetList()
    } else {
      // 改变了登陆状态
      if (isLogin != this.data.isLogin) {
        // 根据状态来判断是否需要重置
        this.setData({ isLogin })
        this.resetList()
      }
    }

    if (isLogin) {
      getResumeStepNum().then((res: any) => {
        if (res.step > 3) {
          this.setData({ isFinishStep: true })
        }
      })
    }
  },

  // 重置列表
  resetList() {
    this.resetAnnouncementParams(1)
    this.resetCompanyParams(1)
    this.search()
  },

  resetAnnouncementParams(type: number) {
    /**
     *     announcementPickerAreaIdTxt: '地区',
     announcementPickerMajorIdTxt: '学科',
     announcementPickerCompanyTypeTxt: '单位类型',
     announcementPickerTypeTxt: '公告类型',
     */
    if (type === 2) {
      this.setData({
        'announcementParams.page': 1
      })
      return
    }
    this.setData({
      announcementParams: {
        page: 1,
        type: '',
        majorId: '',
        areaId: '',
        companyType: '',
        jobType: '',
        keyword: '',
        isEstablishment: '',
        announcementHeat: ''
      }
    })
    // this.setData({ announcementList: [] })
    this.setData({ announcementEnd: false })
    this.setData({ announcementPickerAreaIdTxt: '地区' })
    this.setData({ announcementPickerMajorIdTxt: '学科' })
    this.setData({ announcementPickerCompanyTypeTxt: '单位类型' })
    this.setData({ announcementPickerTypeTxt: '公告类型' })
    this.setData({ announcementPickerGroupTxt: '更多' })
  },
  resetCompanyParams(type: number) {
    if (type === 2) {
      this.setData({
        'companyParams.page': 1
      })
      return
    }
    this.setData({
      companyParams: {
        page: 1,
        areaId: '',
        companyType: '',
        companyNature: ''
      }
    })
    this.setData({ companyEnd: false })
    // this.setData({ companyList: [] })
    this.setData({ companyPickerAreaIdTxt: '地区' })
    this.setData({ companyPickerMajorIdTxt: '学科' })
    this.setData({ companyPickerCompanyTypeTxt: '单位类型' })
    this.setData({ companyPickerCompanyNatureTxt: '单位性质' })
  },

  handleShowcase(e: WechatMiniprogram.CustomEvent) {
    const isLogin = checkLogin()

    if (!isLogin) {
      this.setData({ loginDialogVisible: true })
      return
    }

    const {
      currentTarget: {
        dataset: { url }
      }
    } = e

    toWebPage(`${h5}${url}`)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onRefreshAnnouncement() {
    this.setData({ triggeredAnnouncement: false })
    // this.setData({ 'announcementParams.page': 1 })
    this.resetAnnouncementParams(2)
    this.getAnnouncementList()
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onRefreshCompany() {
    this.setData({ triggeredCompany: false })
    // this.setData({ 'companyParams.page': 1 })
    this.resetCompanyParams(2)
    this.getCompanyList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  jumpAnnouncementBanner(e: any) {
    const index = e.detail.index
    const item = this.data.announcementBannerSwiper.originalList[index]
    jump(item.url, item.targetLinkType, item.id)
  },
  jumpCompanyBanner(e: any) {
    const index = e.detail.index
    const item = this.data.companyBannerSwiper.originalList[index]
    jump(item.url, item.targetLinkType, item.id)
  },

  companyRecommendJump(e: any) {
    const item = e.currentTarget.dataset.item
    jump(item.url, item.targetLinkType, item.id)
  },

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage() {
    const rs = await getCommonShareInfo()
    return {
      title: rs.title,
      path: rs.path,
      imageUrl: rs.imageUrl
    }
  },

  handleSuperior(e: WechatMiniprogram.CustomEvent) {
    const { detail } = e
    const { announcementParams } = this.data

    this.setData(
      {
        announcementParams: {
          ...announcementParams,
          ...detail,
          page: 1
        }
      },
      () => {
        this.getAnnouncementList()
      }
    )
  },

  /**
   * 用户点击右上角分享
   */

  methods: {}
})
