@use 'styles/variables' as *;

.layout {
  --td-swiper-nav-dot-size: 10rpx;
  --td-swiper-nav-dots-bar-active-width: 20rpx;
  --td-swiper-radius: var(--border-radius);

  .t-swiper-nav__dots-bar-item {
    margin: 0 5rpx;
  }

  .t-swiper-nav--bottom {
    bottom: 10rpx;
  }

  display: flex;
  flex-direction: column;
  height: 100vh;

  .flex-grow {
    flex-grow: 1;
    overflow: hidden;
  }

  .scroll-content {
    height: 100%;
    overflow: hidden;
  }

  .login-dialog {
    padding-top: 40rpx;
    padding-bottom: 40rpx;
  }
}

.header-container {
  --td-spacer-1: 30rpx;

  .t-navbar__content {
    align-items: flex-start;
    background: url('//img.gaoxiaojob.com/uploads/mini/announcement/header-bg.png') no-repeat center top/cover;
  }

  .t-navbar__left {
    height: 100%;
    flex-grow: 1;
  }

  .content {
    height: 100%;
    width: 100%;
    align-items: center;
    display: flex;
    justify-content: space-between;

    .tabs {
      display: flex;

      .item {
        position: relative;
        font-size: 36rpx;
        margin-right: 50rpx;
        color: var(--font-color-basic);
        transition: all ease 0.3s;
        font-weight: bold;
      }

      .active {
        color: var(--font-color);
        font-weight: bolder;

        &::after {
          content: '';
          position: absolute;
          display: block;
          width: 40rpx;
          height: 6rpx;
          border-radius: 6rpx;
          background: linear-gradient(-90deg, #ffffff, #ffa000);
          left: 50%;
          top: calc(100% + 10rpx);
          transform: translateX(-50%);
        }
      }
    }

    .search {
      width: 36rpx;
      height: 36rpx;
      margin-right: 20rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/icon/search-black.png') no-repeat center/36rpx;
    }
  }
}

@mixin result-item {
  .item {
    position: relative;

    &:last-child {
      &::after {
        display: none;
      }
    }

    &::after {
      display: block;
      position: absolute;
      bottom: 0;
      left: 30rpx;
      right: 30rpx;
      content: '';
      height: 2rpx;
      background-color: var(--border-color);
    }
  }
}

@mixin filter {
  .root-portal-filter {
    height: 92rpx;
    &[id='announcement-filter'] {
      margin-right: -30rpx;
    }
  }

  .root-portal {
    display: block;
  }

  .filter-content {
    z-index: 9;
    left: 0;
    right: 0;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-wrap: nowrap;
    padding: 20rpx 0rpx;
    background-color: #fff;
    overflow-x: scroll;

    &.fixed {
      position: fixed;
      padding: 20rpx 0rpx 20rpx 30rpx;
      box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(51, 51, 51, 0.04);
    }

    .left {
      display: flex;
      flex-grow: 1;
    }

    .item {
      flex-shrink: 0;
      position: relative;
      padding: 0 30rpx 0 18rpx;
      line-height: 48rpx;
      font-size: 24rpx;
      border-radius: 8rpx;
      position: relative;
      background-color: #f4f6fb;
      border: 2rpx solid #f4f6fb;
      margin-right: 20rpx;
      color: $font-color-basic;

      &.more {
        position: relative;
        &::before {
          content: "";
          width: 50rpx;
          height: 20rpx;
          top: -2rpx;
          right: -2rpx;
          position: absolute;
          background: url(//img.gaoxiaojob.com/uploads/mini/icon/vip.png) no-repeat center/100% 100%;
        }
      }

      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        right: 14rpx;
        bottom: 14rpx;
        display: block;
        border: 4rpx solid #c7c7c7;
        border-left-color: transparent;
        border-top-color: transparent;
      }

      &.has-condition {
        color: $color-primary;
        background-color: $color-primary-background;
        border-color: $color-primary;

        &::after {
          border-right-color: $color-primary;
          border-bottom-color: $color-primary;
        }
      }
    }

    .superior-filter {
      margin-right: 20rpx;
    }
  }
}

@include filter;

.empty-content {
  padding-top: 134rpx;
  padding-bottom: 30rpx;
}

.ending-content {
  padding: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--font-color-basic);
  font-size: 24rpx;

  &::after,
  &::before {
    content: '';
    width: 127rpx;
    height: 2rpx;
    margin: 0 14rpx;
    background: #ebebeb;
  }
}

.announcement-container {
  .top-content {
    padding: 0rpx 30rpx 0;
    background: url('//img.gaoxiaojob.com/uploads/mini/announcement/header-bg.png') no-repeat left -126rpx/100% auto #fff;

    .banner-content {
      padding-top: 40rpx;
    }
  }

  .result-content {
    margin: 30rpx 30rpx 0;
    background-color: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;

    @include result-item;

    .showcase {
      .image {
        display: block;
        width: 100%;
      }

      &::after {
        content: '';
        display: block;
        width: 630rpx;
        height: 2rpx;
        background-color: #ebebeb;
        margin: 0 auto;
      }

      &:last-of-type {
        &::after {
          display: none;
        }
      }
    }
  }
}

.company-container {
  .banner-content {
    background: url('//img.gaoxiaojob.com/uploads/mini/announcement/header-bg.png') no-repeat left -126rpx/100% auto;
    padding: 40rpx 30rpx 20rpx;
  }

  .card {
    border-radius: var(--border-radius);
    background-color: var(--color-white);
  }

  .wrapper-title {
    display: flex;
    align-items: center;
    padding: 22rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    padding-left: 42rpx;
    background: url('https://img.gaoxiaojob.com/uploads/mini/icon/title.png') no-repeat left/28rpx;
  }

  .content-space {
    padding: 0 30rpx;
  }

  .units-content {
    --swiper-height: 264rpx;

    padding: 0 30rpx 30rpx;
    margin: 20rpx 0;

    .units-swiper {
      height: calc(var(--swiper-height));
    }

    .units-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .unit-item {
        width: 305rpx;
        height: 122rpx;
        padding: 22rpx 10rpx 24rpx 20rpx;
        box-sizing: border-box;
        background-color: #f4f6fb;
        border-radius: var(--border-radius);
        display: flex;
        margin-bottom: 20rpx;

        &:nth-child(3),
        &:nth-child(4) {
          margin-bottom: 0;
        }

        .logo {
          width: 76rpx;
          height: 76rpx;
          border-radius: 50%;
          margin-right: 11rpx;
          flex-shrink: 0;
        }

        .vip {
          position: relative;
          &::after {
            content: '';
            display: block;
            position: absolute;
            right: 0;
            bottom: 0;
            width: 28rpx;
            height: 28rpx;
            background: url(#{$assets}/icon/certification.png) no-repeat center/contain;
          }
        }

        .unit-detail {
          flex-grow: 1;
          overflow: hidden;

          .name {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .desc {
            font-size: 24rpx;
            color: var(--font-color-label);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .swiper-nav {
      --td-swiper-nav-dot-color: #c7c7c7;
      --td-swiper-nav-dot-active-color: #{$color-primary};

      position: relative;
      height: 10rpx;
      display: flex;
      align-items: center;

      .t-swiper-nav--bottom {
        top: 10rpx;
      }
    }
  }

  .filter-result {
    .wrapper-title {
      margin: 20rpx 30rpx 4rpx;
      padding-bottom: 0;
      background-position: left 0 bottom 12rpx;
    }

    .filter-content {
      padding-left: 30rpx;
      padding-right: 30rpx;
    }

    .result-content {
      @include result-item;

      .item {
        &:first-child {
          padding-top: 12rpx;
        }
      }
    }

    .empty-content {
      padding-top: 50rpx;
      padding-bottom: 50rpx;
    }
  }
}

page {
  .back-top-trigger {
    bottom: 300rpx;
  }
}
