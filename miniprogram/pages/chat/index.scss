@use 'styles/variables' as *;

.chat-container {
  height: calc(100vh - 100rpx - env(safe-area-inset-bottom));
  display: flex;
  flex-direction: column;
}

.header-container {
  --td-spacer-1: 30rpx;

  position: sticky;
  top: 0;
  z-index: 1;

  background: $color-white url('//img.gaoxiaojob.com/uploads/mini/announcement/header-bg.png') no-repeat top center /
    cover;

  .t-navbar__content {
    align-items: flex-start;
    background: none;
  }

  .t-navbar__left {
    height: 100%;
    flex-grow: 1;
    font-weight: bold;
    font-size: 40rpx;
  }

  .status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 30rpx;
    font-size: 24rpx;

    span {
      display: inline-block;
      padding: 2rpx 25rpx;
    }
  }

  .active {
    color: $color-primary;
    border: 1px solid $color-primary;
    border-radius: 40rpx;
  }

  .clear {
    color: $font-color-label;
    padding-left: 40rpx;
    background: url(#{$assets}/chat/clear.png) no-repeat left center / 32rpx;
  }
}

.avatar {
  width: 90rpx;
  height: 90rpx;
  margin-right: 16rpx;
  border-radius: 50%;
  object-fit: cover;
}

.top-bar {
  position: sticky;
  top: 0;
}

.notify {
  display: flex;
  align-items: center;
  z-index: 2;
  font-size: 24rpx;
  padding: 18rpx 30rpx;
  background-color: $tag-primary-background;

  .open {
    flex: 1;
    color: $color-primary;
    padding-right: 22rpx;
    background: url('//img.gaoxiaojob.com/uploads/mini/icon/into.png') no-repeat center left 81rpx / 10rpx 18rpx;
  }

  .close {
    width: 28rpx;
    height: 28rpx;
    background: url('//img.gaoxiaojob.com/uploads/mini/icon/delete-round.png') no-repeat center / contain;
  }
}

.close-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background-color: $color-point;
  color: $color-white;
  font-size: 28rpx;
  padding: 0 12rpx;
  height: 100%;
}

.information {
  display: flex;
  padding: 25rpx 30rpx;
  font-size: 26rpx;
  color: $font-color-basic;

  .title {
    font-weight: bold;
    font-size: 30rpx;
    margin-bottom: 15rpx;
    color: $font-color;
    background: url(//img.gaoxiaojob.com/uploads/mini/chat/live.png) no-repeat left 130rpx center / 55rpx 19rpx;
  }
}

.empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: $color-background;

  image {
    width: 350rpx;
    height: 250rpx;
  }

  .tips {
    margin: 30rpx;
    color: $font-color-basic;
  }

  .t-button {
    width: 300rpx;
    height: 70rpx;
    margin-top: 30rpx;
  }
}

.list {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex-grow: 1;
}

.chat-list {
  .item {
    display: flex;
    padding: 25rpx 30rpx;

    &.is-top {
      background-color: #fffbf4;
    }
  }

  .btn-wrapper {
    display: flex;
    width: 268rpx;
    height: 100%;

    .top,
    .del {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 134rpx;
      color: $color-white;
    }

    .top {
      background-color: $color-primary;
    }

    .del {
      background-color: $color-point;
    }
  }

  .message {
    position: relative;

    .count {
      position: absolute;
      top: 0;
      left: 72rpx;
      padding: 0 8rpx;
      height: 26rpx;
      background-color: $color-point;
      color: $color-white;
      border-radius: 13rpx;
      font-size: 20rpx;
      line-height: 26rpx;
    }
  }

  .message-right {
    width: calc(100% - 100rpx);
  }
}
