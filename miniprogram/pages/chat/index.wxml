<view class="chat-container">
  <view class="header-container">
    <t-navbar style="{{ headerStyle }}">
      <view slot="left" class="custom-title">消息</view>
    </t-navbar>

    <view class="status">
      <view class="left">
        <span class="{{ status === '' ? 'active' : '' }}" data-status="" bindtap="handleCheck">全部</span>
        <span class="{{ status === '1' ? 'active' : '' }}" data-status="1" bindtap="handleCheck">未读</span>
      </view>

      <view class="clear" bindtap="handleClear">清除未读</view>
    </view>

    <view class="notify" wx:if="{{ isLogin && isShowSubscribeTips }}">
      <view class="text">开启消息通知，及时接收重要消息。</view>
      <view class="open" bindtap="handleOpen">去开启</view>
      <view class="close" bindtap="handleCloseTips"></view>
    </view>
  </view>

  <view class="list">
    <view class="notice" wx:if="{{ isShowDiscoverTips }}">
      <t-swipe-cell>
        <navigator url="/packages/discover/index/index">
          <view class="information">
            <image class="avatar" src="//img.gaoxiaojob.com/uploads/mini/chat/discover.png" alt="" />

            <view class="discover">
              <view class="title">职场发现</view>
              <view>高校试讲、结构化面试如何准备？点这里</view>
            </view>
          </view>
        </navigator>
        <view slot="right" class="close-btn" bindtap="handleCloseDiscover">关闭推荐</view>
      </t-swipe-cell>
    </view>

    <view class="empty" wx:if="{{ !isLogin }}">
      <image class="avatar" src="//img.gaoxiaojob.com/uploads/mini/empty/list-empty.png" />
      <view class="tips">请登录后查看直聊消息</view>
      <t-button theme="primary" bindtap="handleLogin">立即登录</t-button>
    </view>

    <view class="chat-list" wx:if="{{ chatList.length }}">
      <chat-contact-item chatList="{{ chatList }}" bind:update="getSessionList" />
    </view>

    <view class="empty" wx:if="{{ isLogin && !chatList.length }}">
      <image src="//img.gaoxiaojob.com/uploads/mini/empty/list-empty.png" />

      <view class="tips">暂无直聊消息</view>
    </view>
  </view>

  <login-dialog visible="{{ loginDialogVisible }}" bind:loginSuccess="successLogin" />
  <qrcode-popup visible="{{ qrVisible }}" />
  <add-desk-top />
</view>
