import { getCheckSubscribe } from '@/api/person'
import { checkLogin } from '@/utils/store'
import { handleTabBar } from '@/utils/tabBar'

// pages/chat.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLogin: false,
    loginDialogVisible: false,
    isShowSubscribeTips: true,
    isShowDiscoverTips: true,
    qrVisible: false,
    headerStyle: getApp().globalData.headerStyle,
    chatList: <any>[],
    status: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {},

  getSessionList() {
    const { sessionList } = getApp()

    const { status } = this.data

    this.setData({ chatList: status === '' ? sessionList : sessionList.filter((item: any) => item.unreadAmount !== 0) })
  },

  handleCheck() {
    const { status } = this.data
    this.setData({ status: status === '1' ? '' : '1' })
    this.getSessionList()
  },

  handleClear() {
    const { socketInstance } = getApp()
    socketInstance.handleChatClearAll()
  },

  updateStatus() {
    const { sessionList } = getApp()

    sessionList.forEach((item: any) => (item.unreadAmount = 0))

    this.getSessionList()
  },

  handleLogin() {
    this.setData({ loginDialogVisible: true })
  },

  successLogin() {
    this.setData({ isLogin: true })
  },

  handleCloseTips() {
    this.setData({ isShowSubscribeTips: false })
    getApp().updateSubscribeTipsStatus(false)
  },

  handleCloseDiscover() {
    this.setData({ isShowDiscoverTips: false })
    getApp().updateDiscoverStatus(false)
  },

  handleOpen() {
    this.setData({ qrVisible: true })
  },

  async showTips() {
    const { isShowSubscribeTips, isShowDiscoverTips } = getApp().globalData

    const { isSubscribe } = await getCheckSubscribe()

    if (isSubscribe === 1) {
      this.setData({ isShowSubscribeTips: false })
    } else {
      if (isShowSubscribeTips) {
        this.setData({ isShowSubscribeTips })
      }
    }

    this.setData({ isShowDiscoverTips })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    handleTabBar(this, { value: 'chat' })

    this.setData({ isLogin: checkLogin() })
    const { isLogin } = this.data

    if (!isLogin) return

    this.getSessionList()

    this.showTips()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {}

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {}
})
