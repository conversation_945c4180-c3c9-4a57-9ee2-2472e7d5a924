<wxs src="./utils.wxs" module="utils" />

<view class="top-container">
  <header title="{{ title }}" bind:back="handleBack" />

  <view class="title">
    <t-button size="extra-small" hover-class="none" bindtap="handleArea">{{ areaText }}</t-button>
  </view>

  <view class="content {{ resultList.length === 0 ? 'no-data' : '' }}">
    <t-tabs value="{{ tabValue }}" bind:change="onTabsChange">
      <t-tab-panel wx:for="{{ tabOption }}" wx:key="index" label="{{ item.label }}" value="{{ item.value }}" />
    </t-tabs>

    <block wx:if="{{ resultList.length }}">
      <block wx:for="{{ resultList }}" wx:key="id">
        <view class="result-item {{ tabValue === '1' ? 'job-class-name' : '' }}">
          <view class="num top-{{ index + 1 }}">{{ utils.filterText(index + 1) }}</view>

          <view class="box">
            <job-item wx:if="{{ tabValue === '1' }}" detail="{{ item }}" is-login="{{ true }}" show-welfare="{{ false }}" show-experience="{{ true }}" show-city="{{ false }}" />

            <announcement-item wx:if="{{ tabValue === '2' }}" c-class="announcement-item" detail="{{ item }}" show-area="{{ false }}" />

            <company-item wx:if="{{ tabValue === '3' }}" c-class="company-item" detail="{{ item }}" show-footer="{{ false }}" />
          </view>
        </view>
      </block>
    </block>

    <empty wx:else c-class="empty" description="暂无该地区榜单，切换地区再试试" />
  </view>

  <regin-picker model="{{ areaId }}" visible="{{ visible }}" title="地区选择" bind:change="handlePicker" />
</view>
