import { getAnnouncementHotList, getCompanyHotList, getJobHotList } from '@/api/region'
import { getAreaAreaInfo } from '@/utils/store'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    title: '',

    topValue: 0,

    areaId: '',
    areaText: '',
    visible: false,

    tabValue: '1',
    tabOption: [
      { label: '热门职位榜', value: '1' },
      { label: '热门公告榜', value: '2' },
      { label: '热门单位榜', value: '3' }
    ],

    resultList: []
  },

  handleBack() {
    wx.navigateBack()
  },

  handleArea() {
    this.setData({ visible: true })
  },

  handlePicker(event: WechatMiniprogram.CustomEvent) {
    const { label, value } = event.detail

    this.setData({ areaId: value, areaText: label })
    this.fetchData()
  },

  onTabsChange(event: WechatMiniprogram.CustomEvent) {
    const { value } = event.detail

    this.setData({ tabValue: value })
    this.fetchData()
  },

  setTargetTop() {
    wx.createSelectorQuery()
      .select('.title')
      .boundingClientRect((rect) => {
        const { top } = rect

        this.setData({ topValue: top })
      })
      .exec()
  },

  onPageScroll(option) {
    const { scrollTop } = option
    const { topValue } = this.data

    this.setData({ title: scrollTop >= topValue ? '热门榜单' : '' })
  },

  fetchArea() {
    const { id, name } = getAreaAreaInfo()
    this.setData({ areaId: id, areaText: name })
  },

  async fetchData() {
    const { tabValue, areaId } = this.data
    const fetchList = { '1': getJobHotList, '2': getAnnouncementHotList, '3': getCompanyHotList }

    // * reset empty list
    this.setData({ resultList: [] })

    const handler = fetchList[tabValue as keyof typeof fetchList]

    const data = await handler(areaId)

    this.setData({ resultList: data })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(option) {
    const { type = '1' } = option
    const { tabOption } = this.data
    const typeValue = tabOption.map((item) => item.value)

    const tabValue = typeValue.includes(type) ? type : '1'

    this.setData({ tabValue })
    this.setTargetTop()
    this.fetchArea()
    this.fetchData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
