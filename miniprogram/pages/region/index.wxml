<layout>
  <t-navbar style="{{ headerStyle }}" title="" left-arrow="{{ false }}" class="header"></t-navbar>

  <view class="region-container">
    <view class="title-wrapper"></view>

    <regin-picker model="{{ currentArea.id }}" visible="{{ showReginSelectShow }}" title="地区选择" bind:change="showReginSelectChange" />

    <view class="region-wrapper">
      <view class="region" bindtap="showReginSelect">{{ currentArea.name }}</view>
      <navigator class="search" url="/packages/search/index/index?areaId={{ currentArea.id }}" hover-class="none"></navigator>
    </view>

    <t-swiper wx:if="{{ bannerList.length }}" t-class="ad-swiper swiper" height="140rpx" autoplay interval="{{ defaultSwiperInterval }}" navigation="{{ { type: 'dots-bar' } }}" list="{{ bannerList }}" bind:click="clickBanner"> </t-swiper>

    <view wx:if="{{ noticeList.length }}" class="notice-wrapper card">
      <i slot="prefix-icon" class="notice-prefix"></i>
      <swiper class="swiper" indicator-dots="{{ false }}" interval="{{ defaultSwiperInterval }}" autoplay circular vertical>
        <swiper-item class="notice-item" wx:for="{{ noticeList }}" wx:key="key" data-item="{{ item }}" bind:tap="onTabNotice"> {{ item.title }} </swiper-item>
      </swiper>
      <i slot="suffix-icon" class="notice-suffix"></i>
    </view>

    <view class="hot-wrapper card">
      <view class="top" bind:tap="handleHotTop">
        <view class="title"> 每天9点更新 </view>
        <view class="more"></view>
      </view>
      <view class="content">
        <image class="list" src="{{ assetsURL }}/region/hot-list1.png" mode="aspectFit" data-type="1" bind:tap="handleHotTop" />
        <image class="list" src="{{ assetsURL }}/region/hot-list2.png" mode="aspectFit" data-type="2" bind:tap="handleHotTop" />
        <image class="list" src="{{ assetsURL }}/region/hot-list3.png" mode="aspectFit" data-type="3" bind:tap="handleHotTop" />
      </view>
    </view>

    <result class="job-wrapper" title="热招职位" list="{{ jobList }}" bind:click="handleClick" />
    <result class="announcement-wrapper" title="公告类型" list="{{ announcementList }}" bind:click="handleClick" />

    <view wx:if="{{ !isLogin || !isVip }}" class="vip-guide" bindtap="vipGuideClick">
      <image class="img" src="{{ assetsURL + '/vip/guide.png' }}" mode="aspectFit" />
    </view>

    <result class="theme-wrapper" title="精选主题" list="{{ subjectList }}" bind:click="handleClick" />
    <result class="major-wrapper" title="学科分类" list="{{ majorList }}" bind:click="handleClick" />
    <result class="company-wrapper" title="单位推荐" list="{{ companyList }}" bind:click="handleClick" />
    <result wx:if="{{ directCityList.length }}" class="city-wrapper" type="city" list="{{ directCityList }}" bind:click="switchRegion" />

    <login-dialog bind:loginSuccess="loginSuccess" visible="{{ loginDialogVisible }}" />
    <vip-tips-Dialog visible="{{ vipTipsDialogVisible }}" url="{{ buyUrl.vip }}" />
  </view>
</layout>
