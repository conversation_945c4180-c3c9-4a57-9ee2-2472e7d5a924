import { getAllAreaSelect } from '@/api/region'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    model: {
      type: String,
      value: ''
    },

    label: {
      type: String,
      value: ''
    },

    title: {
      type: String,
      value: ''
    },
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    options: []
  },

  lifetimes: {
    attached() {
      this.fetchData()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async fetchData() {
      const options = <never>await getAllAreaSelect()
      this.setData({ options })
    },

    handleChange(event: WechatMiniprogram.CustomEvent) {
      const { label, value } = event.detail

      this.triggerEvent('change', { value, label })
    }
  }
})
