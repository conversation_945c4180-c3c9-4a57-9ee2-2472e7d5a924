<view class="wrapper-card">
  <view class="type">
    <i class="icon"></i>
    {{ title }}
  </view>
  <view class="result-content">
    <block wx:if="{{ type === 'swiper' }}">
      <view class="swiper-content" style="{{ swiperStyle }}">
        <swiper bind:change="change" class="swiper" indicator-dots="{{ false }}" autoplay="{{ autoplay }}" current="0" interval="{{ interval }}" duration="1000" circular="false">
          <swiper-item class="swiper-item" wx:for="{{ list }}" wx:for-item="list" wx:key="key">
            <view class="list {{ item.type === 'isEstablishment' ? 'establishment' : '' }}" wx:for="{{ list }}" wx:for-item="item" wx:key="key" data-url="{{ item.url }}" bind:tap="click" data-detail="{{ item }}">
              <view class="name">{{ item.name }}</view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <view wx:if="{{ isShowNav }}" class="swiper-nav">
        <t-swiper-nav class="dots" total="{{ list.length }}" current="{{ current }}" type="dots-bar"></t-swiper-nav>
      </view>
    </block>

    <block wx:if="{{ type === 'city' }}">
      <view class="title-content">
        <view wx:for="{{ list }}" wx:for-item="item" wx:key="key" class="list" data-detail="{{ item }}" bind:tap="click">
          <view class="name"> {{ item.name }} </view>
        </view>
      </view>
    </block>
  </view>
</view>
