@use 'sass:meta';
@use 'styles/variables' as *;

.wrapper-card {
  --border-color: #f4f6fb;
  background-color: var(--color-white);
  border-radius: var(--border-radius);
  margin-bottom: 20rpx;
  display: flex;

  .type {
    width: 150rpx;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-right: 2rpx solid var(--border-color);
    font-size: 26rpx;
    font-weight: bold;

    .icon {
      width: 54rpx;
      height: 54rpx;
      margin-bottom: 10rpx;
    }
  }
}

.result-content {
  --td-swiper-nav-dot-size: 10rpx;
  --td-swiper-nav-dots-bar-active-width: 20rpx;
  --td-swiper-radius: var(--border-radius);
  --td-swiper-nav-dot-size: 10rpx;
  --td-swiper-nav-dots-bar-active-width: 20rpx;
  --td-swiper-nav-dot-color: #c7c7c7;
  --td-swiper-nav-dot-active-color: var(--color-primary);
  flex-grow: 1;

  @mixin listStyle {
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    box-sizing: border-box;
    width: calc(100% / 3);
    padding: 10rpx 10rpx;
    border-right: 2rpx solid #f4f6fb;
    border-bottom: 2rpx solid #f4f6fb;
    position: relative;

    &.establishment {
      background: url('#{$assets}/icon/vip-radius.png') no-repeat right top/45rpx 18rpx;

      .name {
        background: url('#{$assets}/icon/fire.png') no-repeat left/24rpx 24rpx;
        padding-left: 29rpx;
      }
    }

    &:nth-child(3n) {
      border-right: none;
    }
    .name {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-align: center;
    }
  }

  .swiper-content {
    --swiper-height: 180rpx;

    .swiper {
      height: var(--swiper-height);

      .swiper-item {
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
      }

      .list {
        @include listStyle;

        &:nth-child(7),
        &:nth-child(8),
        &:nth-child(9) {
          border-bottom: none;
        }
      }
    }
  }

  .swiper-nav {
    height: 46rpx;
    position: relative;

    .dots {
      .t-swiper-nav__dots-bar-item {
        margin: 0 5rpx;
      }
    }
  }

  .title-content {
    display: flex;
    flex-wrap: wrap;

    .list {
      @include listStyle;

      &:nth-child(3n + 1) {
        &:nth-last-child(-n + 3),
        &:nth-last-child(-n + 3) + .list {
          border-bottom: none;
        }
      }
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
