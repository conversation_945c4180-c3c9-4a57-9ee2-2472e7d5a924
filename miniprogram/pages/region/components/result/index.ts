// pages/region/components/swiper/index.ts
Component({
  options: {
    styleIsolation: 'shared'
  },
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: ''
    },
    list: {
      type: Array,
      value: [[]]
    },
    autoplay: {
      type: Boolean,
      value: false
    },
    interval: {
      type: Number,
      value: 5000
    },
    type: {
      type: String,
      value: 'swiper'
    },
    prefixUrl: {
      type: String,
      value: '/packages/search/result/index'
    }
  },

  observers: {
    list: function (list = [[]]) {
      const { column } = this.data
      const { length } = list
      const { length: itemLen = 0 } = list[0] || []
      this.setData({
        isShowNav: length > 1
      })
      if (itemLen === 0) return
      const row = Math.ceil(itemLen / column)
      this.setHeight(row > 2 ? row : 2)
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    swiperStyle: '',
    column: 3,
    rowHeight: 88,
    current: 0,
    isShowNav: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    change(e: any) {
      const {
        detail: { current }
      } = e
      this.setData({
        current
      })
    },

    setHeight(row: number) {
      const { rowHeight } = this.data
      this.setData({
        swiperStyle: `--swiper-height: ${rowHeight * row}rpx`
      })
    },

    click(e: any) {
      const {
        currentTarget: {
          dataset: { url, detail = {} }
        }
      } = e

      const { prefixUrl, title } = this.data

      const realUrl =
        title === '公告类型'
          ? `/pages/announcement/index${url}`.replace('type', 'announcementType')
          : `${prefixUrl}${url}`

      if (title === '公告类型') {
        wx.reLaunch({ url: realUrl })
        return
      }
      this.triggerEvent('click', { ...detail, url: realUrl })
    }
  }
})
