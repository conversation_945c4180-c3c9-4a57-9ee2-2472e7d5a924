import { assetsURL } from '@/settings'
import { handleTabBar } from '@/utils/tabBar'
import { showLoading } from '@/utils/util'
import { setAreaAreaInfo, getAreaAreaInfo } from '@/utils/store'
import { getAreaCurrent, getBannerList, getToutiaoList, getColumnList, getDirectCityList } from '@/api/region'
import { jump } from '@/utils/url'
import { checkLogin } from '@/utils/store'
import { getCommonShareInfo } from '@/utils/store'
import { getVipInfo } from '@/api/person'
import { toWebPage } from '@/utils/util'
import { h5 } from '@/settings'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLogin: false,
    loginDialogVisible: false,
    isVip: false,
    vipTipsDialogVisible: false,
    buyUrl: {
      vip: ''
    },

    assetsURL,
    headerStyle: getApp().globalData.headerStyle,
    defaultSwiperInterval: 5000,
    currentArea: {
      name: '',
      id: ''
    },
    bannerList: [],
    orBannerList: [],
    noticeList: [],
    jobList: [],
    announcementList: [],
    subjectList: [],
    companyList: [],
    majorList: [],
    directCityList: [],
    showReginSelectShow: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    let currentArea = {
      name: '',
      id: ''
    }
    // 去全局里面找有没有
    const areaInfo = getAreaAreaInfo()
    if (areaInfo) {
      currentArea = areaInfo
    } else {
      currentArea = await this.getCurrentArea()
      setAreaAreaInfo(currentArea)
    }
    this.setData({
      currentArea
    })
    this.getList()
  },

  showReginSelect() {
    // 显示地区选择模板
    this.setData({ showReginSelectShow: true })
  },

  changeReginSelect() {},

  // 点击公告
  onTabNotice(e: any) {
    const {
      currentTarget: {
        dataset: {
          item: { url, targetLinkType, id }
        }
      }
    } = e
    jump(url, targetLinkType, id)
  },

  clickBanner(e: any) {
    const index = e.detail.index
    const item = <any>this.data.orBannerList[index]
    jump(item.url, item.targetLinkType, item.id)
  },

  handleHotTop(event: WechatMiniprogram.CustomEvent) {
    const { type = '1' } = event.currentTarget.dataset

    wx.navigateTo({ url: `./top?type=${type}` })
  },

  showReginSelectChange(e: any) {
    const {
      detail: { value, label }
    } = e
    this.switchRegion({ detail: { id: value, name: label } })
  },

  async getCurrentArea() {
    return await (<any>getAreaCurrent())
  },

  async getList() {
    showLoading()
    const { id: areaId } = this.data.currentArea
    const orBannerList = await getBannerList({ areaId })
    this.data.orBannerList = orBannerList
    const noticeList = await getToutiaoList({ areaId })
    const bannerList = orBannerList.reduce((acc: any, cur: any) => {
      if (cur.imageUrl) {
        acc.push(cur.imageUrl)
      }
      return acc
    }, [])

    const { jobList, announcementList, subjectList, companyList, majorList } = await getColumnList({
      areaId
    })
    const directCityList = await getDirectCityList({ areaId })

    this.setData({
      bannerList,
      noticeList,
      jobList,
      announcementList,
      subjectList,
      companyList,
      majorList,
      directCityList
    })

    wx.hideLoading()
  },

  /**
   * 切换地区
   */
  async switchRegion(e: any) {
    const { detail } = e
    this.setData({
      currentArea: detail
    })
    setAreaAreaInfo(detail)
    handleTabBar(this)
    // 回到顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 100
    })
    await this.getList()
  },

  handleClick({ detail }: WechatMiniprogram.CustomEvent) {
    const { type, url } = detail
    if (type === 'isEstablishment') {
      this.handleEstablishmentQuery(url)
    } else {
      wx.navigateTo({
        url
      })
    }
  },

  handleCallback(callback = () => {}, toast = true) {
    const { isLogin } = this.data

    if (isLogin) {
      callback()
    } else {
      toast && this.setData({ loginDialogVisible: true })
    }
  },

  vipGuideClick() {
    const {
      isVip,
      buyUrl: { vip }
    } = this.data

    this.handleCallback(() => {
      if (!isVip) {
        toWebPage(`${h5}${vip}`)
      }
    })
  },

  handleEstablishmentQuery(url: string) {
    const { isVip } = this.data

    this.handleCallback(() => {
      if (!isVip) {
        this.setData({ vipTipsDialogVisible: true })
        return
      }
      wx.navigateTo({
        url
      })
    })
  },

  loginSuccess() {
    this.setData({ isLogin: true })
    this.fetchVipInfo()
  },

  async fetchVipInfo() {
    const { isVip, buyUrl } = await getVipInfo()

    this.setData({ isVip: isVip === '1', buyUrl })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getTabBar().checkMessage()

    handleTabBar(this, { value: 'region' })
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    const isLogin = checkLogin()
    this.setData({
      isLogin: isLogin
    })

    if (isLogin) {
      this.fetchVipInfo()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getList()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage() {
    const rs = await getCommonShareInfo()
    return {
      title: rs.title,
      path: rs.path,
      imageUrl: rs.imageUrl
    }
  }
})
