@use 'styles/variables' as *;

$current-color: #7a3800;

.top-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: $page-background;

  .t-navbar--fixed .t-navbar__content {
    background: url(#{$assets}/region/top/header.png) no-repeat top left / 100% 100%;
  }

  .t-tabs {
    border-radius: 16rpx;
    overflow: hidden;
  }

  .t-tabs__item-inner {
    color: $font-color;
    font-size: 32rpx;

    @at-root #{&}--active {
      color: $current-color;
      font-weight: bold;
    }
  }

  .t-tabs__track {
    background-color: $current-color;
  }

  .title {
    padding: 40rpx 60rpx;
    background: url(#{$assets}/region/top/title.png) no-repeat top left / 100%;

    &::before {
      content: '';
      display: block;
      margin-bottom: 30rpx;
      width: 297rpx;
      height: 59rpx;
      background: url(#{$assets}/region/top/text.png) no-repeat center / contain;
    }

    .t-button {
      padding-left: 25rpx;
      padding-right: 52rpx;
      color: $current-color;
      background: #f3cfa7 url(#{$assets}/region/top/arrow.png) no-repeat calc(100% - 30rpx) center / 14rpx;
    }
  }

  .content {
    flex: 1 0 auto;
    padding: 0 30rpx 60rpx;
    background: linear-gradient(180deg, #fef4dc, #f4f6fb);

    &.no-data {
      .t-tabs {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }

  .result-item {
    display: flex;
    margin-top: 20rpx;
    background-color: $color-white;
    border-radius: 16rpx;
    overflow: hidden;

    &.job-class-name {
      padding-right: 30rpx;

      .box {
        padding-bottom: 30rpx;
      }
    }

    .announcement-item,
    .company-item {
      padding-left: 0;
    }

    .num {
      flex: none;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100rpx;
      color: #d6d6d8;
      font-size: 44rpx;
      font-style: italic;
      font-weight: bold;

      @for $index from 1 through 3 {
        &.top-#{$index} {
          font-size: 0;
          background: url(#{$assets}/region/top/top#{$index}.png) no-repeat center / 40rpx 51rpx;
        }
      }
    }

    .box {
      flex: 1 0 auto;
      width: calc(100% - 100rpx);
      box-sizing: border-box;
    }
  }

  .empty {
    padding: 240rpx 0;
    background-color: $color-white;
    border-radius: 0 0 16rpx 16rpx;
  }
}
