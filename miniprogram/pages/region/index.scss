.header {
  --td-spacer-1: 30rpx;

  .t-navbar__content {
    align-items: flex-start;
    background: url('//img.gaoxiaojob.com/uploads/mini/nav/primary.png') no-repeat center top/cover;
  }
}

.region-container {
  padding: 10rpx 30rpx 0;
  background: url('//img.gaoxiaojob.com/uploads/mini/nav/primary.png') no-repeat left -136rpx/100% auto;

  .title-wrapper {
    margin-bottom: 40rpx;
    width: 250rpx;
    height: 48rpx;
    background: url('//img.gaoxiaojob.com/uploads/mini/region/hot-recruitment.png') no-repeat center/contain;
  }

  .region-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 34rpx;

    .region {
      font-size: 36rpx;
      font-weight: bold;
      display: flex;
      align-items: center;

      &::after {
        content: '';
        width: 18rpx;
        height: 8rpx;
        margin-left: 10rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/region/down.png') no-repeat center/contain;
      }
    }

    .search {
      width: 36rpx;
      height: 36rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/region/search.png') no-repeat center/36rpx;
    }
  }

  .swiper {
    --td-swiper-nav-dot-size: 10rpx;
    --td-swiper-nav-dots-bar-active-width: 20rpx;
    --td-swiper-radius: var(--border-radius);

    .t-swiper-nav__dots-bar-item {
      margin: 0 5rpx;
    }
  }

  .ad-swiper {
    margin-bottom: 20rpx;

    .t-swiper-nav--bottom {
      bottom: 10rpx;
    }
  }

  .card {
    background-color: var(--color-white);
    border-radius: var(--border-radius);
    margin-bottom: 20rpx;
  }

  .notice-wrapper {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;

    .swiper {
      flex-grow: 1;
      height: 40rpx;
      padding: 0 14rpx;
    }

    .notice-item {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .notice-prefix {
      flex-shrink: 0;
      width: 40rpx;
      height: 40rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/region/clock.png') no-repeat center/contain;
    }

    .notice-suffix {
      flex-shrink: 0;
      width: 58rpx;
      height: 20rpx;
      background: url('//img.gaoxiaojob.com/uploads/mini/region/hot.png') no-repeat center/contain;
    }
  }

  .hot-wrapper {
    padding: 0 30rpx 30rpx;

    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 0;

      .title {
        padding-left: 184rpx;
        font-size: 24rpx;
        color: var(--font-color-label);
        background: url('//img.gaoxiaojob.com/uploads/mini/region/hot-title.png') no-repeat left/172rpx 32rpx;
      }

      .more {
        width: 30rpx;
        height: 30rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/region/arrow.png') no-repeat center/contain;
      }
    }

    .content {
      display: flex;

      .list {
        flex-grow: 1;
        height: 140rpx;
      }
    }
  }

  $wrapperType: job, announcement, theme, company, major;

  @each $type in $wrapperType {
    .#{$type}-wrapper {
      .type {
        .icon {
          background: url('//img.gaoxiaojob.com/uploads/mini/region/#{$type}.png') no-repeat center/contain;
        }
      }

      .result-content {
        background: url('//img.gaoxiaojob.com/uploads/mini/region/#{$type}-marking.png')
          no-repeat
          right
          20rpx
          bottom
          0rpx/140rpx
          108rpx;
      }
    }
  }

  .city-wrapper {
    .type {
      .icon {
        width: 126rpx;
        height: 36rpx;
        background: url('//img.gaoxiaojob.com/uploads/mini/region/city-direct.png') no-repeat center/contain;
      }
    }
  }

  .vip-guide {
    height: 100rpx;
    margin-bottom: 20rpx;

    .img {
      height: 100%;
      width: 100%;
    }
  }
}
