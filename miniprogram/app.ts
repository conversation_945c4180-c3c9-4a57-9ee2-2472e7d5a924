import { getConfig } from './api/channel'
import { getAuthorization, setBaseConfig, setChannelSphid } from './utils/store'
import ChatSocket from './hooks/chat-socket'
import { getChatHistoryList } from './api/chat'
import { getEmoji } from '@/utils/store'
import { checkUserStatus } from './api/entry'
import { envVersionRelease } from './settings'
// import { refreshLoginStatus } from './utils/common'

if (envVersionRelease) {
  require('./utils/sdk/mtj-wx-sdk')
}

App<any>({
  globalData: {
    safeArea: {},
    windowHeight: 844,
    screenWidth: 375,
    screenHeight: 844,
    statusBarHeight: 20,
    headerStyle: '',
    headerOffsetHeight: 60,
    isShowSubscribeTips: true,
    isShowDiscoverTips: true,

    isAppleDevice: true
  },

  sessionList: [],

  emojiList: [],

  socketInstance: null,

  chatMessageCount: 0,

  updateSubscribeTipsStatus(status: boolean) {
    this.globalData.isShowSubscribeTips = status
  },

  updateDiscoverStatus(status: boolean) {
    this.globalData.isShowDiscoverTips = status
  },

  setDeviceInfo() {
    const { platform, system } = wx.getDeviceInfo()
    const re = /ios|iPhone|iPad|macOS/i

    this.globalData.isAppleDevice = re.test(platform) || re.test(system)
  },

  updateApp() {
    const updateManager = wx.getUpdateManager()

    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        updateManager.onUpdateReady(() => {
          updateManager.applyUpdate()
        })
      }
    })
  },

  onLaunch() {
    this.getSystemInfoSync()
    this.updateApp()
    this.socketConnect()
    this.setDeviceInfo()
    this.getGlobalConfig()
    this.getMenuButtonBoundingClientRect()
    this.getBaseCache()
    this.checkNetwork()
  },

  checkNetwork() {
    // 监听网络变化
    wx.onNetworkStatusChange((res) => {
      // console.log('监听到网络状态切换：' + res.isConnected)
      if (res.isConnected) {
        this.socketConnect()
        // wx.showToast({ title: '网络已连接！' })
      } else {
        // wx.showToast({ title: '设备无网络！', icon: 'error' })
      }
    })
  },

  getBaseCache() {
    wx.onBackgroundFetchData((res) => {
      const jsonString = res.fetchedData
      if (jsonString) {
        // 解析json
        const json = JSON.parse(jsonString)
        setBaseConfig(json.data)
      }
    })
    wx.getBackgroundFetchData({
      fetchType: 'pre',
      success(res: any) {
        const jsonString = res.fetchedData
        if (jsonString) {
          // 解析json
          const json = JSON.parse(jsonString)
          setBaseConfig(json.data)
        }
      }
    })
  },

  getGlobalConfig() {
    getConfig()
      .then((result: any) => {
        setChannelSphid(result.sphid)
      })
      .catch(() => {})
  },

  async fetchUserStatus(callback: (isLogin: boolean) => void) {
    const { isLogin } = await checkUserStatus()

    callback && callback(isLogin)
  },

  socketConnect(isError: boolean = false) {
    const { token } = getAuthorization()

    if (token) {
      this.socketInstance = new ChatSocket()
      this.socketInstance.handleChatLogin()
      // 错误导致的重连，咱不考虑重新加载这些数据，避免一直重连
      if (!isError) {
        this.getSessionList()
        this.getEmojiList()
      }
    }
  },

  async getSessionList() {
    this.sessionList = await getChatHistoryList()

    const pageInstance = getCurrentPages().at(-1)
    pageInstance?.getSessionList?.()
  },

  async getEmojiList() {
    const { list } = await getEmoji()
    this.emojiList = list
  },

  updateChatMessageCount(value: number) {
    this.chatMessageCount = value
  },

  clearSessionList() {
    this.sessionList = []
  },

  updateSession(data: any) {
    const { sessionList } = this
    const index = sessionList.findIndex((item: any) => item.chatId === data.chatId)

    if (index >= 0) {
      sessionList.splice(index, 1, data)
    } else {
      sessionList.unshift(data)
    }
  },

  updateReadStatus(data: any) {
    const { chatId } = data

    const currentPage = getCurrentPages().at(-1)
    const currentPageChatId = currentPage?.data.chatId

    if (chatId === currentPageChatId) {
      currentPage?.updateReadStatus(data)
    }
  },

  getSystemInfoSync() {
    const info = wx.getSystemInfoSync()
    const { statusBarHeight, screenWidth, screenHeight, safeArea, windowHeight } = info
    this.globalData.statusBarHeight = statusBarHeight
    this.globalData.screenWidth = screenWidth
    this.globalData.screenHeight = screenHeight
    this.globalData.safeArea = safeArea
    this.globalData.windowHeight = windowHeight
  },

  getMenuButtonBoundingClientRect() {
    const { top, height } = wx.getMenuButtonBoundingClientRect()
    this.setHeaderStyle(top, height)
  },

  setHeaderStyle(top: number, height: number) {
    this.globalData.headerStyle = `--td-navbar-padding-top: ${top}px; --td-navbar-height: ${height + 6}px;`
    this.globalData.headerOffsetHeight = Number(`${top + height + 6}`)
  },

  // 访问不存在的页面跳转到首页
  onPageNotFound() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  onShow() {
    this.updateSubscribeTipsStatus(true)
  },

  onHide() {
    // refreshLoginStatus()
  }
})
