import { chat } from '@/settings'
import { getAuthorization } from '@/utils/store'

export default class ChatSocket {
  private connected: boolean = false
  private processList: (() => void)[] = []

  private socketTask: WechatMiniprogram.SocketTask = wx.connectSocket({
    url: chat,
    header: {
      'content-type': 'application/json'
    }
  })

  private generateRandomString() {
    return Date.now() + Math.random().toString().slice(2)
  }

  private generateParams(chatId: string, type: string, content: object = {}) {
    return {
      type,
      chatId,
      cuid: this.generateRandomString(),
      content
    }
  }

  private getAppInstance() {
    return getApp()
  }

  private getCurrentPageInstance() {
    return getCurrentPages().at(-1)
  }

  private getChatPageInstance() {
    const currentPageInstance = this.getCurrentPageInstance()
    const route = currentPageInstance?.route ?? ''

    if (!/\/chat\/index/g.test(route)) return

    return currentPageInstance
  }

  private handleChatSend(data: object) {
    const dataString = JSON.stringify(data)
    const handler = () => this.socketTask.send({ data: dataString })

    if (!this.connected) {
      this.processList.push(handler)
      return
    }

    handler()
  }

  private handleMessagePing() {
    this.handleChatSend({ type: 'pong' })
  }

  private handleMessageUnread(data: any) {
    const currentPageInstance: any = this.getCurrentPageInstance()
    const { chatAmount } = data.content

    this.getAppInstance().updateChatMessageCount(chatAmount)

    currentPageInstance?.getTabBar()?.updateChatMessageCount()

    const myComponent = currentPageInstance?.selectComponent('#new-nav-bar')

    // 调用当前页面的方法
    if (myComponent) {
      myComponent.updateChatMessageCount()
    }
  }

  private handleUpdateMessage(data: any) {
    this.getAppInstance().updateReadStatus(data)
  }

  private handleErrorMessage(data: any) {
    this.getChatPageInstance()?.showErrorMessage?.(data.content)
  }

  private handleUpdateSession(data: any) {
    const { content } = data
    this.getAppInstance()?.updateSession(content)

    this.getChatPageInstance()?.getSessionList?.()
  }

  private handleClearAll() {
    this.getChatPageInstance()?.updateStatus?.()
  }

  private updateMessage(data: any) {
    this.getChatPageInstance()?.updateMessage?.(data)
  }

  private ignoreMessageHandleOptions: { [key: string]: (data: any) => void } = {
    ping: this.handleMessagePing,
    unread: this.handleMessageUnread,
    isRead: this.handleUpdateMessage,
    updateSession: this.handleUpdateSession,
    errorMessage: this.handleErrorMessage,
    clearAll: this.handleClearAll
  }

  constructor() {
    this.socketTask.onOpen(() => {
      this.connected = true
      this.processList.forEach((handler) => handler())
      this.processList.splice(0)
    })

    this.socketTask.onClose(() => {
      setTimeout(() => {
        this.getAppInstance().socketConnect(true)
      }, 1000)
    })

    this.socketTask.onError(() => {
      const page = this.getChatPageInstance()

      if (page?.is.includes('packages')) {
        wx.showToast({ title: '网络连接已断开，请重试！', icon: 'none' })
      }
    })

    this.socketTask.onMessage((res: WechatMiniprogram.SocketTaskOnMessageListenerResult) => {
      const data = JSON.parse(res.data as string)
      const { type } = data

      if (this.ignoreMessageHandleOptions[type]) {
        this.ignoreMessageHandleOptions[type].call(this, data)
        return
      }

      this.updateMessage(data)
    })
  }

  public handleChatClose() {
    this.getAppInstance().clearSessionList()
    this.handleMessageUnread({ content: { chatAmount: 0 } })
    this.socketTask.close({})
  }

  public handleChatLogin() {
    const { token } = getAuthorization()

    this.handleChatSend({ type: 'login', token })
  }

  public handleChatSetRead(chatId: string) {
    this.handleChatSend({ type: 'isRead', chatId })
  }

  public handleChatSendJobId(chatId: string, jobId: string) {
    this.handleChatSend(this.generateParams(chatId, 'jobCard', { jobId }))
  }

  public handleChatClearAll() {
    this.handleChatSend({ type: 'clearAll' })
  }

  public handleChatSendText(chatId: string, text: string) {
    this.handleChatSend(this.generateParams(chatId, 'text', { text }))
  }

  public handleChatIgnoreInviteJob(chatId: string, messageId: string) {
    this.handleChatSend(this.generateParams(chatId, 'inviteJobRequestIgnore', { messageId }))
  }

  public handleChatInterestInviteJob(chatId: string, messageId: string) {
    this.handleChatSend(this.generateParams(chatId, 'inviteJobRequestInterest', { messageId }))
  }

  public handleChatSendResume(chatId: string, jobApplyId: number) {
    this.handleChatSend(this.generateParams(chatId, 'resumeCard', { jobApplyId }))
  }

  public handleChatSendFile(chatId: string, fileId: string) {
    this.handleChatSend(this.generateParams(chatId, 'file', { fileId }))
  }

  public handleChatRequestFile(chatId: string, jobApplyId: string) {
    this.handleChatSend(this.generateParams(chatId, 'requestFile', { jobApplyId }))
  }
}
