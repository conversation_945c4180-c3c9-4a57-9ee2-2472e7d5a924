@use 'sass:meta';
@use 'styles/variables' as *;

page {
  @include tdesign-variables;

  color: #{$font-color};
  font-size: 28rpx;

  @include meta.load-css('styles/tdesign');

  button {
    &::after {
      border: none;
    }
  }

  .back-top-trigger {
    position: fixed;
    right: 30rpx;
    bottom: 120rpx;
    width: 74rpx;
    height: 74rpx;
    background: url('#{$assets}/icon/backtop.png') no-repeat center / contain;
    z-index: $popup-z-index - 1;
  }
}
