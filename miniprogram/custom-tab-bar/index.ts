import { assetsURL } from '@/settings'
import { getAreaAreaInfo, setGlobalAreaInfo } from '@/utils/store'
import { getAreaCurrent } from '@/api/region'
import { checkNewMessage } from '@/api/home'

Component({
  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {
    chatMessageCount: 0,

    hasUnRead: false,
    hasFeedback: false,

    backtop: false,
    callback: () => {},

    value: 'home',
    list: [
      {
        value: 'home',
        label: '找职位',
        iconPath: `${assetsURL}/tabs/home.png`,
        selectIconPath: `${assetsURL}/tabs/home-active.png`,
        url: '/pages/home/<USER>'
      },
      {
        value: 'region',
        label: '广东',
        iconPath: `${assetsURL}/tabs/region.png`,
        selectIconPath: `${assetsURL}/tabs/region-active.png`,
        url: '/pages/region/index'
      },
      {
        value: 'announcement',
        label: '招聘公告',
        iconPath: `${assetsURL}/tabs/announcement.png`,
        selectIconPath: `${assetsURL}/tabs/announcement-active.png`,
        url: '/pages/announcement/index'
      },
      {
        value: 'chat',
        label: '消息',
        iconPath: `${assetsURL}/tabs/chat.png`,
        selectIconPath: `${assetsURL}/tabs/chat-active.png`,
        url: '/pages/chat/index'
      },
      {
        value: 'person',
        label: '我的',
        iconPath: `${assetsURL}/tabs/personal.png`,
        selectIconPath: `${assetsURL}/tabs/personal-active.png`,
        url: '/pages/person/index'
      }
    ]
  },

  observers: {
    'backtop, value'() {
      this.updateTabBarList()
    }
  },

  lifetimes: {
    attached() {
      this.checkMessage()
      this.fetchAreaData()
      this.updateChatMessageCount()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async checkMessage() {
      const { hasUnRead = false, hasFeedback = false } = await checkNewMessage()

      this.setData({ hasUnRead, hasFeedback })
    },

    async fetchAreaData() {
      let label = ''
      const { name } = getAreaAreaInfo()
      if (name) {
        label = name
      } else {
        const area = await getAreaCurrent()
        setGlobalAreaInfo(area)
        label = area.name
      }
      const { list } = this.data

      list[1].label = label
      this.setData({ list })
    },

    updateChatMessageCount() {
      const { chatMessageCount } = getApp()

      this.setData({ chatMessageCount })
    },

    updateBacktop(value: boolean) {
      this.setData({ backtop: value })
    },

    updateCallback(callback = () => {}) {
      this.setData({ callback })
    },

    updateTabBarList() {
      const { value, list, backtop } = this.data
      const valid = value === 'home' && backtop
      const label = valid ? '回顶部' : '找职位'
      const selectIconPath = `${assetsURL}/tabs/${valid ? 'back-top' : 'home-active'}.png`

      list[0].label = label
      list[0].selectIconPath = selectIconPath

      this.setData({ list })
    },

    onChange(e: any) {
      // 跳转页面
      const { value } = e.detail
      const { list, backtop, callback }: any = this.data
      // 根据value去找url
      const url = list.find((item: any) => item.value === value).url

      wx.switchTab({ url })
      value === 'home' && backtop && callback()
    }
  }
})
