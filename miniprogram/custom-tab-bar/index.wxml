<t-tab-bar t-class="tab-bar" value="{{ value }}" bindchange="onChange" split="{{ false }}">
  <t-tab-bar-item t-class="tab-item" wx:for="{{ list }}" wx:key="index" value="{{ item.value }}">
    <t-badge count="{{ item.value === 'chat' ? chatMessageCount : 0 }}" offset="{{ [0, 4] }}" color="#FA635C">
      <view class="tab-item-content {{ value === item.value ? 'active' : '' }}">
        <image class="tab-item-icon" src="{{ value === item.value ? item.selectIconPath : item.iconPath }}" mode="heightFix" />

        <view class="tab-item-label">{{ item.label }}</view>

        <block wx:if="{{ item.value === 'person' }}">
          <view class="tab-badge is-special" wx:if="{{ hasFeedback }}">有反馈</view>

          <view class="tab-badge" wx:if="{{ hasFeedback === false && hasUnRead }}"></view>
        </block>
      </view>
    </t-badge>
  </t-tab-bar-item>
</t-tab-bar>
