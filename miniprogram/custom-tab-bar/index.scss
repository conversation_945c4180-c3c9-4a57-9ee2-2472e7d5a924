@use 'styles/variables' as *;

.tab-bar {
  --td-tab-bar-active-color: #{$color-primary};

  // height: 98rpx;
  padding: 0 18rpx;

  .tab-item {
    font-size: 22rpx;
    margin: 13rpx 0 9rpx;

    .tab-item-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .tab-item-icon {
      width: 44rpx;
      height: 44rpx;
    }

    .tab-item-label {
      height: 32rpx;
      line-height: 32rpx;
    }

    .tab-badge {
      position: absolute;
      top: 0;
      right: 0;
      width: 10rpx;
      height: 10rpx;
      background-color: $color-point;
      border-radius: 50%;

      &.is-special {
        top: -8rpx;
        left: 70%;
        padding: 0 8rpx;
        width: 60rpx;
        height: 32rpx;
        color: $color-white;
        font-size: 20rpx;
        line-height: 32rpx;
        border-radius: 10rpx 10rpx 10rpx 0;
      }
    }
  }
}
