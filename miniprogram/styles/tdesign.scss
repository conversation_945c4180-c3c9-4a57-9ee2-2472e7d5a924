@use './variables' as *;

// checkbox
.t-checkbox {
  .t-checkbox__border {
    display: none;
  }
}

.t-checkbox__icon-rectangle {
  border-width: 2px;
}

// button
.t-button {
  --td-primary-color-3: var(--color-primary-disabled);
  --td-button-font-weight: bold;

  $sizes: ('large', 88rpx, 32rpx), ('medium', 80rpx, 32rpx), ('small', 70rpx, 28rpx), ('extra-small', 56rpx, 28rpx);

  @each $item in $sizes {
    $name: nth($item, 1);
    $height: nth($item, 2);
    $font-size: nth($item, 3);

    &.t-button--size-#{$name} {
      --td-button-#{$name}-height: #{$height};
      --td-button-medium-font-size: #{$font-size};
      --td-button-border-radius: #{$height / 2};
    }
  }

  &#{&}--light {
    --td-button-light-color: #{$font-color-label};
    --td-button-light-bg-color: #f7f7f7;

    &.t-button--hover::after {
      --td-button-light-active-bg-color: #f7f7f7;
      --td-button-light-active-border-color: #f7f7f7;
    }
  }
}

// input
.t-input {
  .t-input__wrap--clearable-icon {
    font-size: 40rpx;
  }
}

// switch
.t-switch__body {
  --td-switch-height: 52rpx;
  --td-switch-checked-color: #{$color-primary};
  --td-switch-unchecked-color: #e2e5ea;
}

// tabs
.t-tabs {
  --td-tab-item-color: var(--font-color);
}

// popup border-radius
.t-popup--bottom {
  border-radius: 24rpx 24rpx 0 0;
}

.t-popup {
  .t-button {
    &.t-button--size-medium {
      --td-button-medium-height: 80rpx;
    }
  }
}

.t-dialog {
  .t-button {
    &.t-button--size-medium.t-button--text {
      --td-button-medium-height: 112rpx;
      --td-button-border-radius: 0;
    }
  }
}

// empty
.t-empty {
  --td-empty-description-color: var(--font-color-basic);
}
