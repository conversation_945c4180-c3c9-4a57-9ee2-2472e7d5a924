$assets: '//img.gaoxiaojob.com/uploads/mini';

$color-primary: #ffa000;

$color-white: #ffffff;
$color-black: #000000;
$color-point: #fa635c;

$color-background: #f4f6fb;

$color-primary-active: #f39c0a;
$color-primary-disabled: #ffd080;
$color-primary-background: #fffaf1;

$tag-primary-background: #fff3e0;
$tag-info-background: #f3f8fd;

$font-color: #333333;
$font-color-basic: rgba($font-color, 0.8);
$font-color-label: rgba($font-color, 0.6);
$font-color-tips: rgba($font-color, 0.4);

$border-color: #ebebeb;

$border-radius: 16rpx;

$page-background: #f4f6fb;

$popup-z-index: 11500;

/* tdesign variables */
@mixin tdesign-variables {
  --color-primary: #{$color-primary};

  --color-white: #{$color-white};
  --color-black: #{$color-black};
  --color-point: #{$color-point};

  --color-primary-active: #{$color-primary-active};
  --color-primary-disabled: #{$color-primary-disabled};
  --color-primary-background: #{$color-primary-background};

  --tag-primary-background: #{$tag-primary-background};
  --tag-info-background: #{$tag-info-background};

  --font-color: #{$font-color};
  --font-color-basic: #{$font-color-basic};
  --font-color-label: #{$font-color-label};
  --font-color-tips: #{$font-color-tips};

  --border-color: #{$border-color};

  --td-primary-color-7: #{$color-primary};
  --td-primary-color-8: #{$color-primary};

  --border-radius: #{$border-radius};

  --page-background: #{$page-background};
}

@mixin utils-ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/**
    * @description: 全局样式
    * @param { number } $line-size: 行数
    * @param { number } $line-height: 行高
    * @param { number } $font-size: 字体大小
    * @description $font-size default: 28rpx
*/
@mixin utils-ellipsis-lines($line-size, $line-height, $font-size: 28rpx) {
  @if (unitless($font-size)) {
    @error '$font-size must be a unit number';
  }

  $line-height: if(unitless($line-height), $font-size * $line-height, $line-height);
  $max-height: $line-height * $line-size;

  display: -webkit-box;
  -webkit-line-clamp: $line-size;
  -webkit-box-orient: vertical;
  max-height: $max-height;
  font-size: $font-size;
  line-height: $line-height;
  word-break: break-word;
  white-space: normal;
  text-overflow: ellipsis;
  overflow: hidden;
}
