import { checkAnnouncementApply, checkUserApply, jobApply } from '@/api/apply'
import { defaultDuration, h5 } from '@/settings'
import { showToast, toWebPage } from '@/utils/util'

const behavior = Behavior({
  data: {
    beforeApplyDialogVisible: false,
    deliverJobListVisible: false,
    isSendRequest: false,
    isAutoJump: false,
    beforeApplyDialogTitle: '',
    beforeApplyDialogConfirmButtonText: '',
    beforeApplyDialogCancelButtonText: '',
    beforeApplyDialogContent: '',
    beforeApplyDialogLink: '',
    applyJobList: [],
    jobId: ''
  },

  methods: {
    showTipsDialog(data: any) {
      const {
        isSendRequest,
        isAutoJump,
        title: beforeApplyDialogTitle,
        confirmButtonText: beforeApplyDialogConfirmButtonText,
        cancelButtonText: beforeApplyDialogCancelButtonText,
        content: beforeApplyDialogContent,
        link: beforeApplyDialogLink
      } = data

      this.setData({
        beforeApplyDialogVisible: true,
        isSendRequest,
        isAutoJump,
        beforeApplyDialogTitle,
        beforeApplyDialogConfirmBtn: { content: beforeApplyDialogConfirmButtonText, variant: 'basic' },
        beforeApplyDialogCancelBtn: { content: beforeApplyDialogCancelButtonText },
        beforeApplyDialogContent,
        beforeApplyDialogLink
      })

      this.close?.()
    },

    async handleCheckAnnouncementApply(announcementId: string) {
      const { toastDialogData, jobList } = await checkAnnouncementApply({ announcementId })

      if (Object.keys(toastDialogData).length !== 0) {
        if (toastDialogData.isAutoJump) {
          toWebPage(`${h5}${toastDialogData.link}`)
          return
        }
        this.showTipsDialog(toastDialogData)
        return
      }

      this.setData({ deliverJobListVisible: true, applyJobList: jobList })
    },

    handleCheckDialogCancel() {
      this.setData({ beforeApplyDialogVisible: false })
    },

    async handleCheckDialogConfirm() {
      const { beforeApplyDialogLink, isSendRequest, jobId } = this.data

      if (isSendRequest) {
        await jobApply({ jobId })

        wx.setClipboardData({
          data: beforeApplyDialogLink,

          success: () => {
            showToast({ title: '链接已复制，请在浏览器中打开查看', icon: 'none', duration: defaultDuration })

            this.handleCheckDialogCancel()
          }
        })

        return
      } else {
        wx.navigateTo({ url: beforeApplyDialogLink })
      }

      this.close?.()

      this.handleCheckDialogCancel()
    },

    async handleCheckJobInfo(jobId: string) {
      this.setData({ jobId })

      const { toastDialogData, applyDialogData } = await checkUserApply({ jobId })

      if (Object.keys(toastDialogData).length !== 0) {
        if (toastDialogData.isAutoJump) {
          await jobApply({ jobId })
          toWebPage(`${h5}${toastDialogData.link}`)
          return
        }
        this.showTipsDialog(toastDialogData)
        return
      }

      this.openDeliveryPopup(applyDialogData)
      this.close?.()
    }
  }
})

export { behavior as default }
