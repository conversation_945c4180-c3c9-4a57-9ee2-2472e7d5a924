const isValidKey = (key: any) => {
  if (typeof key === 'string') {
    return key.length > 0
  }
  return false
}

const getInputKey = (event: WechatMiniprogram.CustomEvent) => {
  const {
    target: {
      dataset: { key }
    }
  } = event
  return key
}

const getInputValue = (event: WechatMiniprogram.CustomEvent) => {
  const {
    detail: { value }
  } = event
  return value
}

const updateInputValue = (target: any, event: WechatMiniprogram.CustomEvent) => {
  const key = getInputKey(event)
  const value = getInputValue(event)

  if (isValidKey(key)) {
    target.setData({ [key]: value })
  } else {
    console.error('Key is invalid, please set attribute data-key')
  }
}

const clearInputValue = (target: any, event: WechatMiniprogram.CustomEvent) => {
  const key = getInputKey(event)

  if (isValidKey(key)) {
    target.setData({ [key]: '' })
  } else {
    console.error('Key is invalid, please set attribute data-key')
  }
}

const behavior = Behavior({
  methods: {
    handleUpdateInputValue(event: WechatMiniprogram.CustomEvent) {
      updateInputValue(this, event)
    },

    handleClearInputValue(event: WechatMiniprogram.CustomEvent) {
      clearInputValue(this, event)
    }
  }
})

export { behavior as default, updateInputValue as handleUpdateInputValue, clearInputValue as handleClearInputValue }
