import request from '@/utils/request'

export function getNative() {
  return request({
    url: '/config/get-native-city-area-list'
  })
}

export function getIntentionCity() {
  return request({
    url: '/config/get-hierarchy-city-list'
  })
}

export function getPolitical() {
  return request({
    url: '/config/get-political-status-list'
  })
}

export function getEducation() {
  return request({
    url: '/config/get-education-list'
  })
}

export function getMajor() {
  return request({
    url: '/config/get-mini-person-major-list'
  })
}

export function getJobCategory() {
  return request({
    url: '/config/get-category-job-list'
  })
}

export function getNature() {
  return request({
    url: '/config/get-nature-list'
  })
}

export function getWage() {
  return request({
    url: '/config/get-wage-range-list'
  })
}

export function getStatus() {
  return request({
    url: '/config/get-job-status-list'
  })
}

export function getArrive() {
  return request({
    url: '/config/get-arrive-date-list'
  })
}

export function getAnnouncementType() {
  return request({
    url: '/config/get-announcement-type-list'
  })
}

export function getCompanyType() {
  return request({
    url: '/config/get-company-type-list'
  })
}

export function getCompanyNature() {
  return request({
    url: '/config/get-company-nature-list'
  })
}

export function getSearchParams() {
  return request({
    url: '/config/get-job-search-params-v2'
  })
}

export function getAnnouncementSearchParams() {
  return request({
    url: '/config/get-announcement-search-params'
  })
}

export function getCommonShareInfo() {
  return request({
    url: '/config/get-mini-share-info'
  })
}

export function getDictionaryLabel(data: object) {
  return request({
    url: '/config/get-dictionary-text',
    data
  })
}

export function isShowDiscoverLive() {
  return request({
    url: '/config/mini-is-show-discover-live'
  })
}

export function getEmoji() {
  return request({
    url: '/config/get-emote-list'
  })
}

export function getBaseShowcase() {
  return request({
    url: '/config/get-base-showcase '
  })
}
