import request from '@/utils/request'

export function getPersonConfig() {
  return request({ url: '/home/<USER>' })
}

export function getVipInfo() {
  return request({ url: '/resume/get-vip-info' })
}

export function getUserInfo() {
  return request({ url: '/home/<USER>' })
}

export function getJobToolList() {
  return request({ url: '/home/<USER>' })
}

export function getFollowQrCode(data: object) {
  return request({ url: '/home/<USER>', data })
}

/**
 * @deprecated
 */
export function getCheckBindStatus() {
  return request({ url: '/home/<USER>' })
}

export function getCheckSubscribe() {
  return request({ url: '/home/<USER>' })
}

export function refreshResume() {
  return request({ url: '/resume/refresh' })
}

// 点击广告位
export function clickAdvert(data: any) {
  return request({ url: '/home/<USER>', data })
}

// 获取简历完成到第几步
export function getResumeStepNum() {
  return request({
    url: '/resume/get-step-num'
  })
}

export function setResumeShowStatus() {
  return request({ url: '/resume/change-show-status' })
}

export function resumeTopCheck() {
  return request({ url: '/resume-top/check' })
}

export function resumeTopValidate(data: any) {
  return request({ url: '/resume-top/validate', method: 'POST', data })
}

export function resumeTopAdd(data: any) {
  return request({ url: '/resume-top/add', method: 'POST', data })
}

export function getVipFilterInfo() {
  return request({ url: '/member/get-vip-filter-info' })
}
