import request from '../utils/request'

export function checkAnnouncementApply(data: any) {
  return request({
    url: '/job-apply/check-announcement-apply',
    data
  })
}

export function checkMemberComplete(data: any) {
  return request({
    url: '/job-apply/check-member-complete-status',
    data
  })
}

export function checkUserApply(data: object) {
  return request({
    url: '/job-apply/check-user-apply-status',
    method: 'POST',
    data
  })
}

export function jobApply(data: object) {
  return request({
    url: '/job-apply/submit',
    method: 'POST',
    data
  })
}
