import request from '../utils/request'

export function checkUserStatus() {
  return request({
    url: '/resume/check-user-status'
  })
}

export function checkCodeLogin(data: object) {
  return request({
    url: '/member/check-mini-code-login',
    method: 'POST',
    loading: false,
    data
  })
}

export function mobileCodeLogin(data: object) {
  return request({
    url: '/member/login-by-mini-mobile',
    method: 'POST',
    loading: '登录中',
    data
  })
}

export function checkMobileCodeLogin(data: object) {
  return request({
    url: '/member/validate-mobile-login-mini-code',
    method: 'POST',
    loading: '登录中',
    data
  })
}

export function scanCodeLogin(data: object) {
  return request({
    url: '/member/scan-mini-login-code',
    method: 'POST',
    loading: false,
    data
  })
}

export function sendSceneCode(data: object) {
  return request({
    url: '/member/login-code-notice',
    method: 'POST',
    loading: false,
    data
  })
}

// 获取手机登录验证码
export function getSMSCode(data: any) {
  return request({
    url: '/member/send-mobile-login-code',
    method: 'POST',
    data
  })
}

/**
 * 验证码登录
 * @deprecated
 */
export function smsCodeLogin(data: any) {
  return request({
    url: '/member/validate-mobile-login-code',
    method: 'POST',
    data
  })
}

/**
 * 微信code换手机号登录
 * @deprecated
 */
export function wxCodeLogin(data: any) {
  return request({
    url: '/member/login-by-mini-code',
    method: 'POST',
    data
  })
}
