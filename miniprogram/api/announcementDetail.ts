import request from '../utils/request'

export function getDetail(data: any) {
  return request({
    url: '/announcement/detail',
    method: 'GET',
    data
  })
}

export function getJobList(data: any) {
  return request({
    url: '/announcement/get-job-list',
    method: 'GET',
    data
  })
}

export function collect(announcementId: number | string) {
  return request({
    url: '/announcement/collect',
    method: 'POST',
    data: { announcementId }
  })
}

export function checkAnnouncementReport(data: object) {
  return request({ url: '/announcement/check-generate-report', data })
}

export function createAnnouncementReport(announcementId: string) {
  return request({ url: '/announcement/create-report', method: 'POST', data: { announcementId } })
}
