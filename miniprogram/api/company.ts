import request from '../utils/request'

export function getList(data: object) {
  return request({
    url: '/company/get-list',
    data
  })
}

export function getCompanyDetail(data: object) {
  return request({
    url: '/company/detail',
    data
  })
}

export function collect(data: object) {
  return request({
    url: '/company/collect',
    method: 'POST',
    data
  })
}

export function getCompanyFilterList(data: object) {
  return request({
    url: '/company/detail-filter',
    data
  })
}

export function getCompanyAnnouncementList(data: object) {
  return request({
    url: '/company/get-announcement-list',
    data
  })
}

export function getCompanyJobList(data: object) {
  return request({
    url: '/company/get-job-list',
    data
  })
}

export function getActivityList(data: object) {
  return request({
    url: '/company/get-activity-list',
    data
  })
}
