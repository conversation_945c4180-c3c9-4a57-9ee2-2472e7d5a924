import request from '../utils/request'

// 我的tabbar检查反馈及未读消息标识
export function checkNewMessage() {
  return request({ url: '/home/<USER>' })
}

export function getServiceTips() {
  return request({ url: '/home/<USER>' })
}

export function checkShowQrcode() {
  return request({ url: '/home/<USER>' })
}

// 获取首页的广告位数据
export function getHomeShowcase() {
  return request({
    url: '/home/<USER>'
  })
}

// 获取首页推荐职位
export function getRecommendJobList(data: object) {
  return request({
    url: '/home/<USER>',
    data
  })
}

// 获取临时求职意向
export function getTempIntention(data: object) {
  return request({
    url: '/resume-intention/get-temp-intention',
    method: 'POST',
    data
  })
}

// 获取求职意向列表
export function getResumeIntention() {
  return request({
    url: '/resume-intention/get-category-job-list'
  })
}

// 根据求职意向获取职位
export function getIntentionJob(data: object) {
  return request({
    url: '/home/<USER>',
    data
  })
}

/**
 * @deprecated
 */
export function loginByWxLoginCode(data: object) {
  return request({
    url: '/member/login-by-mini-login-code',
    method: 'POST',
    data
  })
}

// 获取我的页面弹窗信息home/actionGetResumeCompletePopInfo
export function getResumeCompletePopInfo() {
  return request({
    url: '/home/<USER>'
  })
}

// 获取我的页面弹窗信息home/actionGetResumeCompletePopInfo
export function setResumeCompletePopInfo() {
  return request({
    url: '/home/<USER>'
  })
}
