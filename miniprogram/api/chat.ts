import request from '../utils/request'

export function getPhraseList() {
  return request({
    url: '/chat-common-phrase/get-list'
  })
}

export function delPhrase(data: any) {
  return request({
    url: '/chat-common-phrase/delete',
    method: 'POST',
    data
  })
}

export function editPrase(data: any) {
  return request({
    url: '/chat-common-phrase/edit',
    method: 'POST',
    data
  })
}

export function getChatHistoryList() {
  return request({
    url: '/chat/get-chat-list'
  })
}

export function beforeCreateChatRoom(data: object) {
  return request({
    url: '/chat/create-room',
    method: 'POST',
    data
  })
}

export function getChatInfo(data: object) {
  return request({
    url: '/chat/get-chat-info',
    data
  })
}

export function getHistoryList(data: object) {
  return request({
    url: '/chat/get-history-list',
    data
  })
}

export function setContactPinned(chatId: string) {
  return request({ url: '/chat/set-top', method: 'POST', data: { chatId } })
}

export function deleteContact(chatId: string) {
  return request({ url: '/chat/del-room', method: 'POST', data: { chatId } })
}

export function beforeFileCheck(data: object) {
  return request({
    url: '/chat/check-request-file',
    method: 'POST',
    data
  })
}

export function getGreetingList() {
  return request({
    url: '/chat-common-greeting/get-list'
  })
}

export function delGreeting(data: any) {
  return request({
    url: '/chat-common-greeting/delete',
    method: 'POST',
    data
  })
}

export function editGreeting(data: any) {
  return request({
    url: '/chat-common-greeting/edit',
    method: 'POST',
    data
  })
}

export function addGreeting(data: any) {
  return request({
    url: '/chat-common-greeting/add',
    method: 'POST',
    data
  })
}

export function setGreetingState(data: any) {
  return request({
    url: '/chat-common-greeting/edit-is-greeting',
    method: 'POST',
    data
  })
}

export function editDefaultGreeting(data: any) {
  return request({
    url: '/chat-common-greeting/edit-default-greeting',
    method: 'POST',
    data
  })
}