import request from '../utils/request'

// 获取首页的全部广告位
export function getHomeShowcase() {
  return request({
    url: '/discover/home'
  })
}

// 获取热门直播的全部广告位
export function getHotShowCase() {
  return request({
    url: '/discover/hot-telecast'
  })
}

// 专场获取详情（主页）
export function getSpecialDetail(id: string) {
  return request({
    url: '/special-activity/get-detail',
    data: { specialActivityId: id }
  })
}

// 专场获取详情（活动场次）
export function getActivitySchedule(id: string) {
  return request({
    url: '/special-activity/get-activity-schedule',
    data: { specialActivityId: id }
  })
}

// 专场获取详情（活动场次）
export function getActivityScheduleV2(id: string) {
  return request({
    url: '/special-activity/get-activity-schedule-v2',
    data: { specialActivityId: id }
  })
}

// 专场详情（参会单位全部场次）
export function getAllSpecial(id: string) {
  return request({
    url: '/special-activity/get-activity-company-search-activity-tab',
    data: { specialActivityId: id }
  })
}

// 专场详情（参会单位筛选条件）
export function getCompanySearchParams(data: object) {
  return request({
    url: '/special-activity/get-activity-company-search-params',
    data
  })
}

// 专场详情（参会单位列表）
export function getActivityCompany(data: object) {
  return request({
    url: '/special-activity/get-activity-company-list',
    data
  })
}

// 活动汇总页面
export function getActivityGather() {
  return request({
    url: '/activity-home/index'
  })
}

// 活动汇总页面-活动分页列表
export function getActivityList(data: object) {
  return request({
    url: '/activity-home/get-activity-list',
    data
  })
}
