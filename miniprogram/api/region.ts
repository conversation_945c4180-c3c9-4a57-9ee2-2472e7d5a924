import request from '../utils/request'

// 获取默认地区
export function getAreaDefault() {
  return request({
    url: '/area/get-area-default'
  })
}

// 获取当前定位
export function getAreaCurrent() {
  return request({
    url: '/area/get-area-current'
  })
}

// 获取banner广告列表
export function getBannerList(data: any) {
  return request({
    url: '/area/get-banner-list',
    data
  })
}

// 获取头条广告列表
export function getToutiaoList(data: any) {
  return request({
    url: '/area/get-toutiao-list',
    data
  })
}

// 获取当前地点所有栏目
export function getColumnList(data: any) {
  return request({
    url: '/area/get-search-column-list',
    data
  })
}

// 获取城市直达列表
export function getDirectCityList(data: any) {
  return request({
    url: '/area/get-direct-city-list',
    data
  })
}

// 获取城市选择器
export function getAllAreaSelect() {
  return request({
    url: '/config/get-region-all-select'
  })
}

export function getJobHotList(areaId: string | number) {
  return request({
    url: '/area/get-job-hot-list',
    data: { areaId }
  })
}

export function getAnnouncementHotList(areaId: string | number) {
  return request({
    url: '/area/get-announcement-hot-list',
    data: { areaId }
  })
}

export function getCompanyHotList(areaId: string | number) {
  return request({
    url: '/area/get-company-hot-list',
    data: { areaId }
  })
}
