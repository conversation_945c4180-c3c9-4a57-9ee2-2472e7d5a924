import request from '../utils/request'

export function getDetail(data: any) {
  return request({
    url: '/job/detail',
    method: 'GET',
    data
  })
}

export function collect(jobId: number | string) {
  return request({
    url: '/job/collect',
    method: 'POST',
    data: { jobId }
  })
}

export function checkJobReport(data: object) {
  return request({ url: '/job/check-generate-report', data })
}

export function createJobReport(jobId: string) {
  return request({ url: '/job/create-report', method: 'POST', data: { jobId } })
}
