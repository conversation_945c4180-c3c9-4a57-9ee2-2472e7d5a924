const fs = require('node:fs')
const path = require('node:path')

const settingsPath = path.resolve(__dirname, '../miniprogram/settings.ts')

const settingsContent = fs.readFileSync(settingsPath, 'utf-8')

const releaseSettingsContent = settingsContent.replace(/export const env.*/, `export const env: Env = 'release'`)

fs.writeFileSync(settingsPath, releaseSettingsContent)

setTimeout(() => {
  console.log('🚀 ~ successfully!')
}, 3000)
