declare namespace Miniprogram {
  type OptionType = { label: string; value: string }

  type ValidateRule =
    | {
        required: boolean
        validator?: (value: any) => boolean
        message: string
      }
    | { required?: boolean; validator: (value: any) => boolean; message: string }

  type ValidateRules = Record<string, Array<ValidateRule>>

  type RequestMethods = 'OPTIONS' | 'GET' | 'HEAD' | 'POST' | 'PUT' | 'DELETE' | 'TRACE' | 'CONNECT'

  type RequestParams = { url: string; data?: any; method?: RequestMethods; loading?: boolean | string }

  type RequestResponse = { data: any; msg: string; result: number }

  type UploadSuccessCallback = (data: any) => void

  type UploadFailureCallback = (data: any) => void

  type UploadFileOption = {
    url: string
    type?: 'all' | 'video' | 'image' | 'file'
    count: number
    extension?: string[]
    formData?: Record<string, any>
  }

  type UploadImageOption = {
    url: string
    count: number
    mediaType?: Array<'image' | 'video' | 'mix'>
    formData?: Record<string, any>
  }
}
