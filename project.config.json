{"description": "项目配置文件", "packOptions": {"ignore": [{"value": "assets", "type": "folder"}], "include": []}, "watchOptions": {"ignore": ["miniprogram/assets"]}, "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "projectname": "new_gaoxiao_mini_app", "setting": {"useCompilerPlugins": ["typescript", "sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "minified": true, "es6": true, "enhance": true, "ignoreUploadUnusedFiles": true, "uglifyFileName": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "appid": "wx0a8ff20cfeff7f32", "libVersion": "2.30.3", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "scripts": {"beforeUpload": "npm run publish"}}