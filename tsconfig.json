{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES2020", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "strict": true, "strictPropertyInitialization": true, "lib": ["ESNext"], "rootDir": "./miniprogram", "outDir": "dist", "typeRoots": ["./typings"], "paths": {"@/*": ["./miniprogram/*"], "tdesign-miniprogram/*": ["./miniprogram/miniprogram_npm/tdesign-miniprogram/*"]}}, "include": ["./miniprogram", "./typings"], "exclude": ["./node_modules", "./miniprogram/miniprogram_npm", "./miniprogram/utils/sdk"]}