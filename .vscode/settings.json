{"files.eol": "\n", "files.associations": {"*.wxml": "html", "*.wxss": "css", "*.wxs": "javascript"}, "editor.tabSize": 2, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "never"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "html.validate.styles": false, "cSpell.words": ["gao<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugaocai", "miniprogram", "qrcode", "randstr", "Sphid", "swiper", "tdesign", "Wechat"]}